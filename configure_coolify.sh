#!/bin/bash

echo "🔍 Checking Apache Status and Coolify Configuration..."

# Check Apache port
APACHE_PORT=$(grep "Listen " /etc/apache2/ports.conf | grep -v ssl | head -1 | awk '{print $2}')
echo "📊 Apache is configured to run on port: $APACHE_PORT"

# Test Apache locally
echo "🧪 Testing Apache locally..."
if curl -s http://localhost:$APACHE_PORT > /dev/null; then
    echo "✅ Apache is responding on http://localhost:$APACHE_PORT"
    
    # Get a sample response
    echo "📄 Sample response from Apache:"
    curl -s http://localhost:$APACHE_PORT | head -20
else
    echo "❌ Apache is not responding on port $APACHE_PORT"
    echo "Checking PM2 status..."
    pm2 status
    echo ""
    echo "Checking PM2 logs..."
    pm2 logs mindscan-apache --lines 10
fi

echo ""
echo "🔧 Coolify Configuration Instructions:"
echo "======================================"
echo ""
echo "1. Open your Coolify dashboard"
echo "2. Go to your project/application settings"
echo "3. Configure the following:"
echo ""
echo "   Domain: mindscan.mendingmind.org"
echo "   Internal URL/Proxy Target: http://localhost:$APACHE_PORT"
echo "   OR"
echo "   Internal URL/Proxy Target: http://127.0.0.1:$APACHE_PORT"
echo ""
echo "4. If using Docker Compose or similar, add this service:"
echo ""
cat << EOF
services:
  mindscan-proxy:
    image: nginx:alpine
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    labels:
      - "coolify.managed=true"
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)"
      - "traefik.http.routers.mindscan.tls=true"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
      - "traefik.http.services.mindscan.loadbalancer.server.port=80"
EOF

echo ""
echo "5. Alternative: Create nginx.conf for reverse proxy:"
echo ""
cat << EOF
events {
    worker_connections 1024;
}

http {
    upstream apache_backend {
        server 127.0.0.1:$APACHE_PORT;
    }

    server {
        listen 80;
        server_name mindscan.mendingmind.org;

        location / {
            proxy_pass http://apache_backend;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
    }
}
EOF

echo ""
echo "🔧 Alternative: Direct Coolify Application Configuration"
echo "======================================================="
echo ""
echo "If you're creating a new application in Coolify:"
echo ""
echo "1. Create a new 'Service' type application"
echo "2. Use 'Docker Compose' configuration"
echo "3. Set the compose file to proxy to your Apache:"
echo ""
cat << EOF
version: '3.8'
services:
  web:
    image: nginx:alpine
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)"
      - "traefik.http.routers.mindscan.tls=true"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - coolify
    depends_on:
      - apache
  
  apache:
    image: busybox
    command: ["tail", "-f", "/dev/null"]
    network_mode: "host"
    
networks:
  coolify:
    external: true
EOF

echo ""
echo "📋 Quick Test Commands:"
echo "======================="
echo ""
echo "Test Apache directly:"
echo "curl http://localhost:$APACHE_PORT"
echo ""
echo "Test from outside (if firewall allows):"
echo "curl http://YOUR_SERVER_IP:$APACHE_PORT"
echo ""
echo "Check if domain resolves:"
echo "nslookup mindscan.mendingmind.org"
echo ""
echo "Test domain after Coolify configuration:"
echo "curl -H 'Host: mindscan.mendingmind.org' http://localhost:$APACHE_PORT"
echo ""
echo "🚨 Important Notes:"
echo "=================="
echo "- Make sure mindscan.mendingmind.org DNS points to your server"
echo "- Coolify should handle SSL/TLS automatically"
echo "- The Apache server must accept requests for 'mindscan.mendingmind.org'"
echo "- Check Coolify's proxy/traefik logs if domain doesn't work"
echo ""
