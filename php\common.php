<?php
session_start();
// $_SESSION["id"]="MSS6eh4N";
require_once($_SERVER['DOCUMENT_ROOT'] . "/vendor/autoload.php");

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\SMTP;

//echo $_SERVER['DOCUMENT_ROOT'];
include($_SERVER['DOCUMENT_ROOT'] . "/config/config.php");

$webUrl = "https://mindscan.mendingmind.org/";


function connect_db()
{
	global $servername, $username, $password, $dbname;
	//echo "ff".$servername. $username. $password. $dbname;f
	$conn = mysqli_connect($servername, $username, $password, $dbname);

	if (!$conn) {
		//die("Connecton failed:".mysqli_connect_error());
		echo "problem in server";
		exit();
	} else {
		//echo "connected successfully";
		return $conn;
		//mysqli_close($conn);
	}
}

function setSession($email)
{
	$_SESSION['id'] = $email;
	/*echo "<script>sessionStorage.setItem('username','".$email."');</script>";*/
}

function checkRequired($required = array())
{
	$error = true;
	foreach ($required as $field) {
		if (empty($_POST[$field])) {
			$error = false;
		}
	}
	return $error;
}

function clearSqlInjection($stringArray)
{
	foreach ($stringArray as $index => $strings) {
		$stringArray[$index] = preg_replace("/select|drop|exec|sel+ect|s+e+l+e+c+t|insert|delete|database|script|<|>|link=\"\"/i", "", $strings);;
	}
	return $stringArray;
}

function checkUserPhone($usernumber)
{
	$checkphone = fetchData("count(*) as count", "users", "phone='" . $usernumber . "'");
	if ($checkphone["count"] > 0)
		return 'true';
	else

		return 'false';
}

function checkSession()
{
	$isLogged = "false";
	if (isset($_SESSION["id"])) {
		$isLogged = "true";
	}
	return $isLogged;
}
function checkSessionAdmin()
{
	$isLogged = "false";
	if (isset($_SESSION["username"])) {
		$isLogged = "true";
	}
	return $isLogged;
}
function getColumnNames($tableName)
{
	$conn = connect_db();
	$abc = mysqli_query($conn, "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'id13991154_thebingostore' AND TABLE_NAME = '" . $tableName . "'");
	while ($row = mysqli_fetch_assoc($abc)) {
		$result[] = $row;
	}
	$columnArr = array_column($result, 'COLUMN_NAME');
	mysqli_close($conn);
	return $columnArr;
}

function formatDate($dateInput)
{
	// echo $dateInput."ddddddd".substr_count($dateInput,"-");
	if (strlen($dateInput) > 0 && (substr_count($dateInput, "-") == 2 || substr_count($dateInput, "-") == 2)) {
		$dateInput = strpos($dateInput, "-") !== False ? explode("-", $dateInput) : explode("-", $dateInput);
// 		print_r($dateInput);
		return $dateInput[2] . "-" . $dateInput[1] . "-" . $dateInput[0];
	}
}

function fetchData($colmuns, $tableName, $condition = "", $orderBy = "", $limit = "")
{
	if ((strtolower($colmuns)) == "all") {
		$colmuns = "*";
	}
	if ($limit != "") {
		$limit = " LIMIT " . $limit . " ";
	}
	if ($orderBy != "") {
		$orderBy = " ORDER BY " . $orderBy . " ";
	}
	if ($condition != "") {
		$condition = " WHERE " . $condition . " ";
	}
	$sql = "SELECT " . $colmuns . " FROM " . $tableName . $condition . $orderBy . $limit . ";";
	//echo $sql;
	$conn = connect_db();
	if ($fetchedData = mysqli_query($conn, $sql)) {
		$data = mysqli_fetch_assoc($fetchedData);
	} else {
		$data = mysqli_error($conn);
	}
	mysqli_close($conn);
	return $data;
}
function executeQuery($tableName, $colmuns, $values)
{
	$sql = "INSERT INTO " . $tableName . "(" . $colmuns . ") VALUES (" . $values . ");";
	//echo $sql;
	$conn = connect_db();
	if (mysqli_multi_query($conn, $sql)) {
		mysqli_close($conn);
		return true;
	} else {
		$er = mysqli_error($conn);
		mysqli_close($conn);
		return $er;
	}
}
function updateQuery($tableName, $values, $condition)
{
	$sql = "UPDATE " . $tableName . " SET " . $values . " WHERE " . $condition . ";";
	//echo $sql;
	$conn = connect_db();
	if (mysqli_multi_query($conn, $sql)) {
		mysqli_close($conn);
		return true;
	} else {
		$error = mysqli_error($conn);
		mysqli_close($conn);
		return $error;
	}
}
function deleteQuery($tableName, $condition)
{
	$sql = "DELETE FROM " . $tableName . " WHERE " . $condition . ";";
	//echo $sql;
	$conn = connect_db();
	if (mysqli_multi_query($conn, $sql)) {
		mysqli_close($conn);
		return true;
	} else {
		$err = mysqli_error($conn);
		mysqli_close($conn);
		return $err;
	}
}
function fetchAll($colmuns, $tableName, $condition = "", $orderBy = "", $limit = "")
{
	if ((strtolower($colmuns)) == "all") {
		$colmuns = "*";
	}
	if ($limit != "") {
		$limit = " LIMIT " . $limit . " ";
	}
	if ($orderBy != "") {
		$orderBy = " ORDER BY " . $orderBy . " ";
	}
	if ($condition != "") {
		$condition = " WHERE " . $condition . " ";
	}
	$sql = "SELECT " . $colmuns . " FROM " . $tableName . $condition . $orderBy . $limit . ";";
	//echo $sql;
	$conn = connect_db();
	if ($fetchedData = mysqli_query($conn, $sql)) {
		$data = mysqli_fetch_all($fetchedData);
	} else {
		$data = mysqli_error($conn);
	}
	mysqli_close($conn);
	return $data;
}
function fetchQuery($sql)
{
	$conn = connect_db();
	if ($fetchedData = mysqli_query($conn, $sql)) {
		$data = mysqli_fetch_all($fetchedData);
	} else {
		$data = mysqli_error($conn);
	}
	mysqli_close($conn);
	return $data;
}
function getRandomString($len)
{
	$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	$randomString = '';
	for ($i = 0; $i < $len; $i++) {
		$randomString .= $characters[rand(0, strlen($characters) - 1)];
	}
	return $randomString;
}

function getConclusion($type, $score)
{
	$level = "";
	$color = "";
	if ($type == "stress") {
		if (0 <= $score && $score <= 10) {
			$level = "Normal";
			$color = "#94cacc";
		}
		if (11 <= $score && $score <= 18) {
			$level = "Mild";
			$color = "#57aa46";
		}
		if (19 <= $score && $score <= 26) {
			$level = "Moderate";
			$color = "#f1c444";
		}
		if (27 <= $score && $score <= 34) {
			$level = "Severe";
			$color = "#f5841f";
		}
		if (35 <= $score && $score <= 42) {
			$level = "Extremely severe";
			$color = "#e72427";
		}
	}

	if ($type == "anxiety") {
		if (0 <= $score && $score <= 6) {
			$level = "Normal";
			$color = "#94cacc";
		}
		if (7 <= $score && $score <= 9) {
			$level = "Mild";
			$color = "#57aa46";
		}
		if (10 <= $score && $score <= 14) {
			$level = "Moderate";
			$color = "#f1c444";
		}
		if (15 <= $score && $score <= 19) {
			$level = "Severe";
			$color = "#f5841f";
		}
		if (20 <= $score && $score <= 42) {
			$level = "Extremely severe";
			$color = "#e72427";
		}
	}

	if ($type == "depression") {
		if (0 <= $score && $score <= 9) {
			$level = "Normal";
			$color = "#94cacc";
		}
		if (10 <= $score && $score <= 12) {
			$level = "Mild";
			$color = "#57aa46";
		}
		if (13 <= $score && $score <= 20) {
			$level = "Moderate";
			$color = "#f1c444";
		}
		if (21 <= $score && $score <= 27) {
			$level = "Severe";
			$color = "#f5841f";
		}
		if (28 <= $score && $score <= 42) {
			$level = "Extremely severe";
			$color = "#e72427";
		}
	}

	return array($level, $color);
}

function sendEmail($email_id, $email_user, $file_to_attach)
{

	$email = new PHPMailer();
// 	$email->SMTPDebug = SMTP::DEBUG_SERVER;                      //Enable verbose debug output
	$email->isSMTP();                                            //Send using SMTP
	$email->Host       = 'smtp.hostinger.com';                     //Set the SMTP server to send through
	$email->SMTPAuth   = true;
	$email->IsHTML(true);                    //Enable SMTP authentication
	$email->Username   = '<EMAIL>';                     //SMTP username
	$email->Password   = 'Kinj@ljain29';                               //SMTP password
	$email->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
	$email->Port       = 465;
	$email->SetFrom('<EMAIL>', 'MindScan | By Mending Mind'); //Name is optional
	$email->Subject   = 'Your Mental Health Checkup Report Is here !';
	$email->Body      = '<!DOCTYPE html> <html lang="en" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml"> <head> <title></title> <meta content="text/html; charset=utf-8" http-equiv="Content-Type" /> <meta content="width=device-width, initial-scale=1.0" name="viewport" /> <!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]--><!--[if !mso]><!--> <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900" rel="stylesheet" type="text/css" /><!--<![endif]--> <style> * { box-sizing: border-box; } body { margin: 0; padding: 0; } a[x-apple-data-detectors] { color: inherit !important; text-decoration: inherit !important; } #MessageViewBody a { color: inherit; text-decoration: none; } p { line-height: inherit } .desktop_hide, .desktop_hide table { mso-hide: all; display: none; max-height: 0px; overflow: hidden; } .image_block img+div { display: none; } @media (max-width:620px) { .desktop_hide table.icons-inner { display: inline-block !important; } .icons-inner { text-align: center; } .icons-inner td { margin: 0 auto; } .mobile_hide { display: none; } .row-content { width: 100% !important; } .stack .column { width: 100%; display: block; } .mobile_hide { min-height: 0; max-height: 0; max-width: 0; overflow: hidden; font-size: 0px; } .desktop_hide, .desktop_hide table { display: table !important; max-height: none !important; } } </style> </head> <body class="body" style="background-color: #ffffff; margin: 0; padding: 0; -webkit-text-size-adjust: none; text-size-adjust: none;"> <table border="0" cellpadding="0" cellspacing="0" class="nl-container" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff;" width="100%"> <tbody> <tr> <td> <table align="center" border="0" cellpadding="0" cellspacing="0" class="row row-1" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%"> <tbody> <tr> <td> <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 600px; margin: 0 auto;" width="600"> <tbody> <tr> <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; padding-bottom: 5px; padding-top: 5px; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%"> <table border="0" cellpadding="10" cellspacing="0" class="paragraph_block block-1" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%"> <tr> <td class="pad"> <div style="color:#101112;direction:ltr;font-family:\'Montserrat\', \'Trebuchet MS\', \'Lucida Grande\', \'Lucida Sans Unicode\', \'Lucida Sans\', Tahoma, sans-serif;font-size:16px;font-weight:400;letter-spacing:0px;line-height:120%;text-align:left;mso-line-height-alt:19.2px;"> <p style="margin: 0; margin-bottom: 16px;">Hey '.ucwords($email_user).', </p> <p style="margin: 0; margin-bottom: 16px;">Thank you for participating in our Mental Health Checkup Camp. We\'re glad you joined us.</p> <p style="margin: 0; margin-bottom: 16px;">This checkup is designed to help you understand your mental health better so you can take any necessary actions or seek professional help. Please find your report attached to this email. </p> <p style="margin: 0; margin-bottom: 16px;position: relative;">If you have any questions or would like to schedule a personal therapy session, feel free to message us on WhatsApp at <a href=https://wa.me/message/7TETCLQCB335O1 target=_blank style="position: absolute;right: 29%;bottom: -8%;"> <img style="margin-bottom: -5px;" src="https://mindscan.mendingmind.org/img/whats-app.png" alt="" width="20"> </a></p> <p style="margin: 0; margin-bottom: 16px;">We\'d love to stay connected! Follow us on social media: <a href="https://www.instagram.com/mending__mind/"" target="_blank" style="display: inline-block;margin-right: 5px;"><img alt="" src="https://mindscan.mendingmind.org/img/insta-logo.png" style="margin-bottom: -5px;" width="20"> </a><a href="https://www.facebook.com/mendingmindfoundation/" target="_blank" style="display:inline-block;margin-right:5px;position:relative"><img alt="" style="margin-bottom: -5px;" src="https://mindscan.mendingmind.org/img/fb-logo.png" width="20"> </a><a  style="display: inline-block;margin-right: 5px;" href="https://www.linkedin.com/company/mendingmind/" target="_blank"><img alt="" style="margin-bottom: -5px;" src="https://mindscan.mendingmind.org/img/linked-in-logo.png" width="20"></a></p> <p style="margin: 0; margin-bottom: 16px;">Wishing you a peaceful day and know that everything shall pass and you are enough! </p> <p style="margin: 0;">Love and Joy,<br />Team Mending Mind<br /><a href="tel:+918433805514" rel="noopener" style="text-decoration: none; color: #7747FF;" target="_blank" title="tel:+918433805514">+91-8433805514</a><br /><a href="mailto:<EMAIL>" rel="noopener" style="text-decoration: none; color: #7747FF;" target="_blank" title="<EMAIL>"><EMAIL></a> </p> </div> </td> </tr> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table><!-- End --> </body> </html>';
	$email->AddAddress($email_id);
	//echo $_SERVER['DOCUMENT_ROOT']."/".$file_to_attach;
	$email->AddAttachment($_SERVER['DOCUMENT_ROOT'] . "/" . $file_to_attach, 'Mending Mind-Mind Scan Report.pdf');

	if (!$email->Send()) {
		//echo "Error sending: " . $email->ErrorInfo;
	}
	//else
	// {
	//    echo "E-mail sent";
	// }
}
