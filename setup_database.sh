#!/bin/bash

# Database Setup Script for DevMindScan
echo "🗄️ Setting up MySQL/MariaDB for DevMindScan..."

# Install MySQL/MariaDB if not already installed
if ! command -v mysql &> /dev/null; then
    echo "📦 Installing MySQL server..."
    apt-get update
    apt-get install -y mysql-server
    
    # Secure MySQL installation
    echo "🔐 Securing MySQL installation..."
    mysql_secure_installation
fi

# Start MySQL service
systemctl start mysql
systemctl enable mysql

echo "💾 Creating database and user..."

# Create database and user
mysql -u root -p << 'EOF'
CREATE DATABASE IF NOT EXISTS devmindscan;
CREATE USER IF NOT EXISTS 'devmindscan_user'@'localhost' IDENTIFIED BY 'DevMind2024!';
GRANT ALL PRIVILEGES ON devmindscan.* TO 'devmindscan_user'@'localhost';
FLUSH PRIVILEGES;
EOF

echo "✅ Database setup completed!"
echo ""
echo "Database Details:"
echo "- Database: devmindscan"
echo "- User: devmindscan_user"
echo "- Password: DevMind2024!"
echo "- Host: localhost"
echo ""
echo "Update your config/config.php with these credentials."
