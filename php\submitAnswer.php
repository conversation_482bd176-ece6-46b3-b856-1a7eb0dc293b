<?php
include("common.php");
?>
<style>body{background:#a5d7df !important}</style>
<?php
$fields = ["id", "option"];
// print_r($_POST);
// exit();
if (checkRequired($fields)) {
    $_POST = clearSqlInjection($_POST);
    $id = "ANS" . getRandomString(6);
    $score = fetchData("score", "option_mapping", "id='" . $_POST['option'] . "'");
    $checkIfExists = fetchData("count(id) as count", "answers", "user_id='" . $_SESSION['id'] . "' AND q_id='" . $_POST['id'] . "'");
    //print_r($checkIfExists);
    if ($checkIfExists["count"] > 0) {
        deleteQuery("answers", "user_id='" . $_SESSION['id'] . "' AND q_id='" . $_POST['id'] . "'");
    }
    $score = $score["score"];
    $ins = executeQuery("answers", "id,user_id,q_id,answer,score", "'" . $id . "','" . $_SESSION['id'] . "','" . $_POST['id'] . "','" . $_POST['option'] . "','" . $score . "'");
    //echo "ins".$ins;
    if ($ins) {
        if($_POST["id"]=="Q26"){
            $update_status=updateQuery("users","status='1'","id='".$_SESSION['id']."'");
            $url="../reports";
        }
        elseif ($_POST["id"] == "Q21") {
            $url = "../filler-message"; 
        }
        else{
            $url = "../question?qn=" . ((string) (int) str_replace("Q", "", $_POST['id']) + 1);
        }        
        echo "<script>window.location.href='" . $url . "';</script>";
    } else {
        echo "error-" . $ins;
    }
}
else{
    $url="../question?qn=".((string) (int) str_replace("Q", "", $_POST['id']));
    echo "<script>window.location.href='" . $url . "';</script>";
}
?>