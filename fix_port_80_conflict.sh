#!/bin/bash

# Fix Apache port conflict - revert to port 8081 only
echo "🔧 Fixing Apache port conflict - using port 8081 only"

# Stop Apache
echo "🛑 Stopping Apache..."
pm2 stop mindscan-apache 2>/dev/null || true
pm2 delete mindscan-apache 2>/dev/null || true
pkill apache2 || true
sleep 2

# Configure Apache to ONLY use port 8081 (avoid port 80 conflict)
echo "🔧 Configuring Apache for port 8081 only..."
cat > /etc/apache2/ports.conf << 'EOF'
# Only listen on port 8081 to avoid conflict with Coolify
Listen 8081

<IfModule ssl_module>
    Listen 8524
</IfModule>

<IfModule mod_gnutls.c>
    Listen 8524
</IfModule>
EOF

# Create virtual host that handles domain properly on port 8081
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
<VirtualHost *:8081>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost *************
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    # Handle proxy headers from Coolify
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # Trust proxy headers from Coolify
    <IfModule mod_remoteip.c>
        RemoteIPHeader X-Forwarded-For
        RemoteIPTrustedProxy **********/12
        RemoteIPTrustedProxy 10.0.0.0/8
        RemoteIPTrustedProxy ***********/16
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>

<VirtualHost *:8524>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost *************
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Optimize .htaccess for better domain handling
echo "🔧 Optimizing .htaccess for domain routing..."
cat > /var/www/html/.htaccess << 'EOF'
RewriteEngine On

# Force domain consistency (optional)
# RewriteCond %{HTTP_HOST} !^mindscan\.mendingmind\.org$ [NC]
# RewriteCond %{HTTP_HOST} !^localhost$ [NC]
# RewriteCond %{HTTP_HOST} !^194\.238\.18\.96$ [NC]
# RewriteRule ^(.*)$ http://mindscan.mendingmind.org/$1 [R=301,L]

# Handle clean URLs - remove .php extension
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Add .php extension internally if file exists
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.+)$ $1.php [NC,L]

# Handle specific routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^login/?$ login.php [NC,L,QSA]
RewriteRule ^home/?$ home.php [NC,L,QSA]
RewriteRule ^questions/?$ questions.php [NC,L,QSA]
RewriteRule ^report/?$ report.php [NC,L,QSA]
RewriteRule ^reports/?$ reports.php [NC,L,QSA]

# Default route to index
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^$ index.php [NC,L]

# Security rules
RewriteRule ^config/ - [F,L]
RewriteRule ^\.env - [F,L]
RewriteRule ^composer\.(json|lock)$ - [F,L]

# MIME type and security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
EOF

# Enable Apache modules
echo "🔧 Enabling Apache modules..."
a2enmod rewrite
a2enmod headers
a2enmod remoteip
a2enmod expires

# Enable site
a2ensite mindscan
a2dissite 000-default

# Test configuration
echo "🧪 Testing Apache configuration..."
if apache2ctl configtest; then
    echo "✅ Apache configuration is valid"
else
    echo "❌ Apache configuration has errors"
    apache2ctl configtest
    exit 1
fi

# Start Apache on port 8081 only
echo "🚀 Starting Apache on port 8081..."
cd /var/www/html
pm2 start ecosystem.config.js
pm2 save

# Wait for startup
sleep 5

# Test Apache
echo "🧪 Testing Apache..."
if curl -s http://localhost:8081 > /dev/null; then
    echo "✅ Apache is running on port 8081"
    
    # Test with domain header
    if curl -s -H "Host: mindscan.mendingmind.org" http://localhost:8081 > /dev/null; then
        echo "✅ Apache responds to domain host header"
    else
        echo "❌ Issue with domain host header"
    fi
else
    echo "❌ Apache not responding on port 8081"
    pm2 logs mindscan-apache --lines 10
    exit 1
fi

echo ""
echo "🎯 NOW UPDATE COOLIFY DOCKER COMPOSE:"
echo "====================================="
echo ""
echo "Use this EXACT configuration in Coolify:"
echo ""
cat << 'DOCKER_COMPOSE'
version: '3.8'
services:
  mindscan-proxy:
    image: nginx:alpine
    container_name: mindscan-proxy
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)"
      - "traefik.http.routers.mindscan.entrypoints=web,websecure"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
      - "traefik.http.services.mindscan.loadbalancer.server.port=80"
      - "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: |
      sh -c 'echo "
      events { worker_connections 1024; }
      http {
        upstream backend {
          server host.docker.internal:8081;
        }
        server {
          listen 80;
          server_name mindscan.mendingmind.org;
          
          location / {
            proxy_pass http://backend;
            proxy_set_header Host \$$host;
            proxy_set_header X-Real-IP \$$remote_addr;
            proxy_set_header X-Forwarded-For \$$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$$scheme;
            proxy_set_header X-Forwarded-Host \$$host;
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
          }
        }
      }" > /etc/nginx/nginx.conf && nginx -g "daemon off;"'
DOCKER_COMPOSE

echo ""
echo "📋 Summary:"
echo "==========="
echo "✅ Apache: Running ONLY on port 8081 (no port conflicts)"
echo "✅ .htaccess: Optimized for domain routing and clean URLs"
echo "✅ Virtual Host: Handles domain properly with proxy headers"
echo ""
echo "🧪 Test commands:"
echo "Direct: curl -I http://*************:8081"
echo "Domain test: curl -I -H 'Host: mindscan.mendingmind.org' http://localhost:8081"
echo ""
echo "🚀 After updating Coolify Docker Compose:"
echo "curl -I http://mindscan.mendingmind.org"
echo "curl -I https://mindscan.mendingmind.org"
