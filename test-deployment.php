<?php
// Simple test script to verify deployment
echo "<!DOCTYPE html>\n";
echo "<html><head><title>Deployment Test</title></head><body>\n";
echo "<h1>🚀 Deployment Test</h1>\n";

// Test PHP version
echo "<h2>PHP Information</h2>\n";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>\n";

// Test PHP extensions
echo "<h2>PHP Extensions</h2>\n";
$required_extensions = ['mysqli', 'pdo', 'pdo_mysql', 'gd', 'zip', 'mbstring'];
foreach ($required_extensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "<p>$status $ext</p>\n";
}

// Test file permissions
echo "<h2>File Permissions</h2>\n";
$dirs_to_check = [
    '/var/www/html',
    '/var/www/html/generated_reports',
    '/var/www/html/reports'
];

foreach ($dirs_to_check as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? '✅ Writable' : '❌ Not writable';
        echo "<p>$dir: $writable</p>\n";
    } else {
        echo "<p>$dir: ❌ Directory not found</p>\n";
    }
}

// Test database connection
echo "<h2>Database Connection</h2>\n";
if (file_exists('/var/www/html/config/config.php')) {
    include '/var/www/html/config/config.php';
    try {
        $pdo = new PDO("mysql:host=$servername;dbname=$dbname", $username, $password);
        echo "<p>✅ Database connection successful</p>\n";
    } catch (PDOException $e) {
        echo "<p>❌ Database connection failed: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p>❌ Config file not found</p>\n";
}

// Test wkhtmltopdf
echo "<h2>PDF Generation</h2>\n";
$wkhtmltopdf_path = '/usr/bin/wkhtmltopdf';
if (file_exists($wkhtmltopdf_path)) {
    echo "<p>✅ wkhtmltopdf found at $wkhtmltopdf_path</p>\n";
} else {
    echo "<p>❌ wkhtmltopdf not found</p>\n";
}

// Test main application files
echo "<h2>Application Files</h2>\n";
$important_files = [
    'index.php',
    'login.php',
    'config/config.php',
    'composer.json'
];

foreach ($important_files as $file) {
    $status = file_exists("/var/www/html/$file") ? '✅' : '❌';
    echo "<p>$status $file</p>\n";
}

echo "<h2>Server Information</h2>\n";
echo "<p><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>\n";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>\n";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

echo "</body></html>\n";
?>
