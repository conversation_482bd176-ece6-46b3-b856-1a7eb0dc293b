# Simple .htaccess for Coolify (if needed)
RewriteEngine On

# Remove .php extension from URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Add .php extension internally
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [L]

# Handle directory index
DirectoryIndex index.php index.html
