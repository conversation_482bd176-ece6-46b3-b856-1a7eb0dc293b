#!/bin/bash
echo "Building PHP application with Nixpacks..."

# Create required directories
mkdir -p generated_reports
mkdir -p reports
chmod 755 generated_reports
chmod 755 reports

# Test PHP extensions
echo "Testing PHP extensions..."
php -m | grep -E "(mysqli|gd|zip|fileinfo|mbstring|pdo_mysql|bcmath|exif|iconv|intl)" || echo "Some PHP extensions may be missing"

# Test wkhtmltopdf
echo "Testing wkhtmltopdf..."
which wkhtmltopdf || echo "wkhtmltopdf not found in PATH"

# Install composer dependencies
if [ -f composer.json ]; then
    echo "Installing Composer dependencies..."
    composer install --no-dev --optimize-autoloader
fi

echo "Build completed!"
