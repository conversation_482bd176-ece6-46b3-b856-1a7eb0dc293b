{"packages": [{"name": "dompdf/dompdf", "version": "v2.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "e8d2d5e37e8b0b30f0732a011295ab80680d7e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/e8d2d5e37e8b0b30f0732a011295ab80680d7e85", "reference": "e8d2d5e37e8b0b30f0732a011295ab80680d7e85", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "masterminds/html5": "^2.0", "phenx/php-font-lib": ">=0.5.4 <1.0.0", "phenx/php-svg-lib": ">=0.3.3 <1.0.0", "php": "^7.1 || ^8.0"}, "require-dev": {"ext-json": "*", "ext-zip": "*", "mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5 || ^8 || ^9", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "time": "2023-02-07T12:51:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "The Dompdf Community", "homepage": "https://github.com/dompdf/dompdf/blob/master/AUTHORS.md"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/v2.0.3"}, "install-path": "../dompdf/dompdf"}, {"name": "masterminds/html5", "version": "2.7.6", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "897eb517a343a2281f11bc5556d6548db7d93947"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/897eb517a343a2281f11bc5556d6548db7d93947", "reference": "897eb517a343a2281f11bc5556d6548db7d93947", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-libxml": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7"}, "time": "2022-08-18T16:18:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.7.6"}, "install-path": "../masterminds/html5"}, {"name": "phenx/php-font-lib", "version": "0.5.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dompdf/php-font-lib.git", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-font-lib/zipball/dd448ad1ce34c63d09baccd05415e361300c35b4", "reference": "dd448ad1ce34c63d09baccd05415e361300c35b4", "shasum": ""}, "require": {"ext-mbstring": "*"}, "require-dev": {"symfony/phpunit-bridge": "^3 || ^4 || ^5"}, "time": "2021-12-17T19:44:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/dompdf/php-font-lib/issues", "source": "https://github.com/dompdf/php-font-lib/tree/0.5.4"}, "install-path": "../phenx/php-font-lib"}, {"name": "phenx/php-svg-lib", "version": "0.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dompdf/php-svg-lib.git", "reference": "76876c6cf3080bcb6f249d7d59705108166a6685"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/php-svg-lib/zipball/76876c6cf3080bcb6f249d7d59705108166a6685", "reference": "76876c6cf3080bcb6f249d7d59705108166a6685", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0", "sabberworm/php-css-parser": "^8.4"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "time": "2022-09-06T12:16:56+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/dompdf/php-svg-lib/issues", "source": "https://github.com/dompdf/php-svg-lib/tree/0.5.0"}, "install-path": "../phenx/php-svg-lib"}, {"name": "sabberworm/php-css-parser", "version": "8.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/e41d2140031d533348b2192a83f02d8dd8a71d30", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "^4.8.36"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "time": "2021-12-11T13:40:54+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.4.0"}, "install-path": "../sabberworm/php-css-parser"}], "dev": true, "dev-package-names": []}