<?php

namespace Masterminds\HTML5;

/**
 * Entity lookup tables.
 * This class is automatically generated.
 */
class Entities
{
    public static $byName = array(
        'Aacute' => 'Á',
        'Aacut' => 'Á',
        'aacute' => 'á',
        'aacut' => 'á',
        'Abreve' => 'Ă',
        'abreve' => 'ă',
        'ac' => '∾',
        'acd' => '∿',
        'acE' => '∾̳',
        'Acirc' => 'Â',
        'Acir' => 'Â',
        'acirc' => 'â',
        'acir' => 'â',
        'acute' => '´',
        'acut' => '´',
        'Acy' => 'А',
        'acy' => 'а',
        'AElig' => 'Æ',
        'AEli' => 'Æ',
        'aelig' => 'æ',
        'aeli' => 'æ',
        'af' => '⁡',
        'Afr' => '𝔄',
        'afr' => '𝔞',
        'Agrave' => 'À',
        'Agrav' => 'À',
        'agrave' => 'à',
        'agrav' => 'à',
        'alefsym' => 'ℵ',
        'aleph' => 'ℵ',
        'Alpha' => 'Α',
        'alpha' => 'α',
        'Amacr' => 'Ā',
        'amacr' => 'ā',
        'amalg' => '⨿',
        'AMP' => '&',
        'AM' => '&',
        'amp' => '&',
        'am' => '&',
        'And' => '⩓',
        'and' => '∧',
        'andand' => '⩕',
        'andd' => '⩜',
        'andslope' => '⩘',
        'andv' => '⩚',
        'ang' => '∠',
        'ange' => '⦤',
        'angle' => '∠',
        'angmsd' => '∡',
        'angmsdaa' => '⦨',
        'angmsdab' => '⦩',
        'angmsdac' => '⦪',
        'angmsdad' => '⦫',
        'angmsdae' => '⦬',
        'angmsdaf' => '⦭',
        'angmsdag' => '⦮',
        'angmsdah' => '⦯',
        'angrt' => '∟',
        'angrtvb' => '⊾',
        'angrtvbd' => '⦝',
        'angsph' => '∢',
        'angst' => 'Å',
        'angzarr' => '⍼',
        'Aogon' => 'Ą',
        'aogon' => 'ą',
        'Aopf' => '𝔸',
        'aopf' => '𝕒',
        'ap' => '≈',
        'apacir' => '⩯',
        'apE' => '⩰',
        'ape' => '≊',
        'apid' => '≋',
        'apos' => '\'',
        'ApplyFunction' => '⁡',
        'approx' => '≈',
        'approxeq' => '≊',
        'Aring' => 'Å',
        'Arin' => 'Å',
        'aring' => 'å',
        'arin' => 'å',
        'Ascr' => '𝒜',
        'ascr' => '𝒶',
        'Assign' => '≔',
        'ast' => '*',
        'asymp' => '≈',
        'asympeq' => '≍',
        'Atilde' => 'Ã',
        'Atild' => 'Ã',
        'atilde' => 'ã',
        'atild' => 'ã',
        'Auml' => 'Ä',
        'Aum' => 'Ä',
        'auml' => 'ä',
        'aum' => 'ä',
        'awconint' => '∳',
        'awint' => '⨑',
        'backcong' => '≌',
        'backepsilon' => '϶',
        'backprime' => '‵',
        'backsim' => '∽',
        'backsimeq' => '⋍',
        'Backslash' => '∖',
        'Barv' => '⫧',
        'barvee' => '⊽',
        'Barwed' => '⌆',
        'barwed' => '⌅',
        'barwedge' => '⌅',
        'bbrk' => '⎵',
        'bbrktbrk' => '⎶',
        'bcong' => '≌',
        'Bcy' => 'Б',
        'bcy' => 'б',
        'bdquo' => '„',
        'becaus' => '∵',
        'Because' => '∵',
        'because' => '∵',
        'bemptyv' => '⦰',
        'bepsi' => '϶',
        'bernou' => 'ℬ',
        'Bernoullis' => 'ℬ',
        'Beta' => 'Β',
        'beta' => 'β',
        'beth' => 'ℶ',
        'between' => '≬',
        'Bfr' => '𝔅',
        'bfr' => '𝔟',
        'bigcap' => '⋂',
        'bigcirc' => '◯',
        'bigcup' => '⋃',
        'bigodot' => '⨀',
        'bigoplus' => '⨁',
        'bigotimes' => '⨂',
        'bigsqcup' => '⨆',
        'bigstar' => '★',
        'bigtriangledown' => '▽',
        'bigtriangleup' => '△',
        'biguplus' => '⨄',
        'bigvee' => '⋁',
        'bigwedge' => '⋀',
        'bkarow' => '⤍',
        'blacklozenge' => '⧫',
        'blacksquare' => '▪',
        'blacktriangle' => '▴',
        'blacktriangledown' => '▾',
        'blacktriangleleft' => '◂',
        'blacktriangleright' => '▸',
        'blank' => '␣',
        'blk12' => '▒',
        'blk14' => '░',
        'blk34' => '▓',
        'block' => '█',
        'bne' => '=⃥',
        'bnequiv' => '≡⃥',
        'bNot' => '⫭',
        'bnot' => '⌐',
        'Bopf' => '𝔹',
        'bopf' => '𝕓',
        'bot' => '⊥',
        'bottom' => '⊥',
        'bowtie' => '⋈',
        'boxbox' => '⧉',
        'boxDL' => '╗',
        'boxDl' => '╖',
        'boxdL' => '╕',
        'boxdl' => '┐',
        'boxDR' => '╔',
        'boxDr' => '╓',
        'boxdR' => '╒',
        'boxdr' => '┌',
        'boxH' => '═',
        'boxh' => '─',
        'boxHD' => '╦',
        'boxHd' => '╤',
        'boxhD' => '╥',
        'boxhd' => '┬',
        'boxHU' => '╩',
        'boxHu' => '╧',
        'boxhU' => '╨',
        'boxhu' => '┴',
        'boxminus' => '⊟',
        'boxplus' => '⊞',
        'boxtimes' => '⊠',
        'boxUL' => '╝',
        'boxUl' => '╜',
        'boxuL' => '╛',
        'boxul' => '┘',
        'boxUR' => '╚',
        'boxUr' => '╙',
        'boxuR' => '╘',
        'boxur' => '└',
        'boxV' => '║',
        'boxv' => '│',
        'boxVH' => '╬',
        'boxVh' => '╫',
        'boxvH' => '╪',
        'boxvh' => '┼',
        'boxVL' => '╣',
        'boxVl' => '╢',
        'boxvL' => '╡',
        'boxvl' => '┤',
        'boxVR' => '╠',
        'boxVr' => '╟',
        'boxvR' => '╞',
        'boxvr' => '├',
        'bprime' => '‵',
        'Breve' => '˘',
        'breve' => '˘',
        'brvbar' => '¦',
        'brvba' => '¦',
        'Bscr' => 'ℬ',
        'bscr' => '𝒷',
        'bsemi' => '⁏',
        'bsim' => '∽',
        'bsime' => '⋍',
        'bsol' => '\\',
        'bsolb' => '⧅',
        'bsolhsub' => '⟈',
        'bull' => '•',
        'bullet' => '•',
        'bump' => '≎',
        'bumpE' => '⪮',
        'bumpe' => '≏',
        'Bumpeq' => '≎',
        'bumpeq' => '≏',
        'Cacute' => 'Ć',
        'cacute' => 'ć',
        'Cap' => '⋒',
        'cap' => '∩',
        'capand' => '⩄',
        'capbrcup' => '⩉',
        'capcap' => '⩋',
        'capcup' => '⩇',
        'capdot' => '⩀',
        'CapitalDifferentialD' => 'ⅅ',
        'caps' => '∩︀',
        'caret' => '⁁',
        'caron' => 'ˇ',
        'Cayleys' => 'ℭ',
        'ccaps' => '⩍',
        'Ccaron' => 'Č',
        'ccaron' => 'č',
        'Ccedil' => 'Ç',
        'Ccedi' => 'Ç',
        'ccedil' => 'ç',
        'ccedi' => 'ç',
        'Ccirc' => 'Ĉ',
        'ccirc' => 'ĉ',
        'Cconint' => '∰',
        'ccups' => '⩌',
        'ccupssm' => '⩐',
        'Cdot' => 'Ċ',
        'cdot' => 'ċ',
        'cedil' => '¸',
        'cedi' => '¸',
        'Cedilla' => '¸',
        'cemptyv' => '⦲',
        'cent' => '¢',
        'cen' => '¢',
        'CenterDot' => '·',
        'centerdot' => '·',
        'Cfr' => 'ℭ',
        'cfr' => '𝔠',
        'CHcy' => 'Ч',
        'chcy' => 'ч',
        'check' => '✓',
        'checkmark' => '✓',
        'Chi' => 'Χ',
        'chi' => 'χ',
        'cir' => '○',
        'circ' => 'ˆ',
        'circeq' => '≗',
        'circlearrowleft' => '↺',
        'circlearrowright' => '↻',
        'circledast' => '⊛',
        'circledcirc' => '⊚',
        'circleddash' => '⊝',
        'CircleDot' => '⊙',
        'circledR' => '®',
        'circledS' => 'Ⓢ',
        'CircleMinus' => '⊖',
        'CirclePlus' => '⊕',
        'CircleTimes' => '⊗',
        'cirE' => '⧃',
        'cire' => '≗',
        'cirfnint' => '⨐',
        'cirmid' => '⫯',
        'cirscir' => '⧂',
        'ClockwiseContourIntegral' => '∲',
        'CloseCurlyDoubleQuote' => '”',
        'CloseCurlyQuote' => '’',
        'clubs' => '♣',
        'clubsuit' => '♣',
        'Colon' => '∷',
        'colon' => ':',
        'Colone' => '⩴',
        'colone' => '≔',
        'coloneq' => '≔',
        'comma' => ',',
        'commat' => '@',
        'comp' => '∁',
        'compfn' => '∘',
        'complement' => '∁',
        'complexes' => 'ℂ',
        'cong' => '≅',
        'congdot' => '⩭',
        'Congruent' => '≡',
        'Conint' => '∯',
        'conint' => '∮',
        'ContourIntegral' => '∮',
        'Copf' => 'ℂ',
        'copf' => '𝕔',
        'coprod' => '∐',
        'Coproduct' => '∐',
        'COPY' => '©',
        'COP' => '©',
        'copy' => '©',
        'cop' => '©',
        'copysr' => '℗',
        'CounterClockwiseContourIntegral' => '∳',
        'crarr' => '↵',
        'Cross' => '⨯',
        'cross' => '✗',
        'Cscr' => '𝒞',
        'cscr' => '𝒸',
        'csub' => '⫏',
        'csube' => '⫑',
        'csup' => '⫐',
        'csupe' => '⫒',
        'ctdot' => '⋯',
        'cudarrl' => '⤸',
        'cudarrr' => '⤵',
        'cuepr' => '⋞',
        'cuesc' => '⋟',
        'cularr' => '↶',
        'cularrp' => '⤽',
        'Cup' => '⋓',
        'cup' => '∪',
        'cupbrcap' => '⩈',
        'CupCap' => '≍',
        'cupcap' => '⩆',
        'cupcup' => '⩊',
        'cupdot' => '⊍',
        'cupor' => '⩅',
        'cups' => '∪︀',
        'curarr' => '↷',
        'curarrm' => '⤼',
        'curlyeqprec' => '⋞',
        'curlyeqsucc' => '⋟',
        'curlyvee' => '⋎',
        'curlywedge' => '⋏',
        'curren' => '¤',
        'curre' => '¤',
        'curvearrowleft' => '↶',
        'curvearrowright' => '↷',
        'cuvee' => '⋎',
        'cuwed' => '⋏',
        'cwconint' => '∲',
        'cwint' => '∱',
        'cylcty' => '⌭',
        'Dagger' => '‡',
        'dagger' => '†',
        'daleth' => 'ℸ',
        'Darr' => '↡',
        'dArr' => '⇓',
        'darr' => '↓',
        'dash' => '‐',
        'Dashv' => '⫤',
        'dashv' => '⊣',
        'dbkarow' => '⤏',
        'dblac' => '˝',
        'Dcaron' => 'Ď',
        'dcaron' => 'ď',
        'Dcy' => 'Д',
        'dcy' => 'д',
        'DD' => 'ⅅ',
        'dd' => 'ⅆ',
        'ddagger' => '‡',
        'ddarr' => '⇊',
        'DDotrahd' => '⤑',
        'ddotseq' => '⩷',
        'deg' => '°',
        'de' => '°',
        'Del' => '∇',
        'Delta' => 'Δ',
        'delta' => 'δ',
        'demptyv' => '⦱',
        'dfisht' => '⥿',
        'Dfr' => '𝔇',
        'dfr' => '𝔡',
        'dHar' => '⥥',
        'dharl' => '⇃',
        'dharr' => '⇂',
        'DiacriticalAcute' => '´',
        'DiacriticalDot' => '˙',
        'DiacriticalDoubleAcute' => '˝',
        'DiacriticalGrave' => '`',
        'DiacriticalTilde' => '˜',
        'diam' => '⋄',
        'Diamond' => '⋄',
        'diamond' => '⋄',
        'diamondsuit' => '♦',
        'diams' => '♦',
        'die' => '¨',
        'DifferentialD' => 'ⅆ',
        'digamma' => 'ϝ',
        'disin' => '⋲',
        'div' => '÷',
        'divide' => '÷',
        'divid' => '÷',
        'divideontimes' => '⋇',
        'divonx' => '⋇',
        'DJcy' => 'Ђ',
        'djcy' => 'ђ',
        'dlcorn' => '⌞',
        'dlcrop' => '⌍',
        'dollar' => '$',
        'Dopf' => '𝔻',
        'dopf' => '𝕕',
        'Dot' => '¨',
        'dot' => '˙',
        'DotDot' => '⃜',
        'doteq' => '≐',
        'doteqdot' => '≑',
        'DotEqual' => '≐',
        'dotminus' => '∸',
        'dotplus' => '∔',
        'dotsquare' => '⊡',
        'doublebarwedge' => '⌆',
        'DoubleContourIntegral' => '∯',
        'DoubleDot' => '¨',
        'DoubleDownArrow' => '⇓',
        'DoubleLeftArrow' => '⇐',
        'DoubleLeftRightArrow' => '⇔',
        'DoubleLeftTee' => '⫤',
        'DoubleLongLeftArrow' => '⟸',
        'DoubleLongLeftRightArrow' => '⟺',
        'DoubleLongRightArrow' => '⟹',
        'DoubleRightArrow' => '⇒',
        'DoubleRightTee' => '⊨',
        'DoubleUpArrow' => '⇑',
        'DoubleUpDownArrow' => '⇕',
        'DoubleVerticalBar' => '∥',
        'DownArrow' => '↓',
        'Downarrow' => '⇓',
        'downarrow' => '↓',
        'DownArrowBar' => '⤓',
        'DownArrowUpArrow' => '⇵',
        'DownBreve' => '̑',
        'downdownarrows' => '⇊',
        'downharpoonleft' => '⇃',
        'downharpoonright' => '⇂',
        'DownLeftRightVector' => '⥐',
        'DownLeftTeeVector' => '⥞',
        'DownLeftVector' => '↽',
        'DownLeftVectorBar' => '⥖',
        'DownRightTeeVector' => '⥟',
        'DownRightVector' => '⇁',
        'DownRightVectorBar' => '⥗',
        'DownTee' => '⊤',
        'DownTeeArrow' => '↧',
        'drbkarow' => '⤐',
        'drcorn' => '⌟',
        'drcrop' => '⌌',
        'Dscr' => '𝒟',
        'dscr' => '𝒹',
        'DScy' => 'Ѕ',
        'dscy' => 'ѕ',
        'dsol' => '⧶',
        'Dstrok' => 'Đ',
        'dstrok' => 'đ',
        'dtdot' => '⋱',
        'dtri' => '▿',
        'dtrif' => '▾',
        'duarr' => '⇵',
        'duhar' => '⥯',
        'dwangle' => '⦦',
        'DZcy' => 'Џ',
        'dzcy' => 'џ',
        'dzigrarr' => '⟿',
        'Eacute' => 'É',
        'Eacut' => 'É',
        'eacute' => 'é',
        'eacut' => 'é',
        'easter' => '⩮',
        'Ecaron' => 'Ě',
        'ecaron' => 'ě',
        'ecir' => 'ê',
        'Ecirc' => 'Ê',
        'Ecir' => 'Ê',
        'ecirc' => 'ê',
        'ecolon' => '≕',
        'Ecy' => 'Э',
        'ecy' => 'э',
        'eDDot' => '⩷',
        'Edot' => 'Ė',
        'eDot' => '≑',
        'edot' => 'ė',
        'ee' => 'ⅇ',
        'efDot' => '≒',
        'Efr' => '𝔈',
        'efr' => '𝔢',
        'eg' => '⪚',
        'Egrave' => 'È',
        'Egrav' => 'È',
        'egrave' => 'è',
        'egrav' => 'è',
        'egs' => '⪖',
        'egsdot' => '⪘',
        'el' => '⪙',
        'Element' => '∈',
        'elinters' => '⏧',
        'ell' => 'ℓ',
        'els' => '⪕',
        'elsdot' => '⪗',
        'Emacr' => 'Ē',
        'emacr' => 'ē',
        'empty' => '∅',
        'emptyset' => '∅',
        'EmptySmallSquare' => '◻',
        'emptyv' => '∅',
        'EmptyVerySmallSquare' => '▫',
        'emsp' => ' ',
        'emsp13' => ' ',
        'emsp14' => ' ',
        'ENG' => 'Ŋ',
        'eng' => 'ŋ',
        'ensp' => ' ',
        'Eogon' => 'Ę',
        'eogon' => 'ę',
        'Eopf' => '𝔼',
        'eopf' => '𝕖',
        'epar' => '⋕',
        'eparsl' => '⧣',
        'eplus' => '⩱',
        'epsi' => 'ε',
        'Epsilon' => 'Ε',
        'epsilon' => 'ε',
        'epsiv' => 'ϵ',
        'eqcirc' => '≖',
        'eqcolon' => '≕',
        'eqsim' => '≂',
        'eqslantgtr' => '⪖',
        'eqslantless' => '⪕',
        'Equal' => '⩵',
        'equals' => '=',
        'EqualTilde' => '≂',
        'equest' => '≟',
        'Equilibrium' => '⇌',
        'equiv' => '≡',
        'equivDD' => '⩸',
        'eqvparsl' => '⧥',
        'erarr' => '⥱',
        'erDot' => '≓',
        'Escr' => 'ℰ',
        'escr' => 'ℯ',
        'esdot' => '≐',
        'Esim' => '⩳',
        'esim' => '≂',
        'Eta' => 'Η',
        'eta' => 'η',
        'ETH' => 'Ð',
        'ET' => 'Ð',
        'eth' => 'ð',
        'et' => 'ð',
        'Euml' => 'Ë',
        'Eum' => 'Ë',
        'euml' => 'ë',
        'eum' => 'ë',
        'euro' => '€',
        'excl' => '!',
        'exist' => '∃',
        'Exists' => '∃',
        'expectation' => 'ℰ',
        'ExponentialE' => 'ⅇ',
        'exponentiale' => 'ⅇ',
        'fallingdotseq' => '≒',
        'Fcy' => 'Ф',
        'fcy' => 'ф',
        'female' => '♀',
        'ffilig' => 'ﬃ',
        'fflig' => 'ﬀ',
        'ffllig' => 'ﬄ',
        'Ffr' => '𝔉',
        'ffr' => '𝔣',
        'filig' => 'ﬁ',
        'FilledSmallSquare' => '◼',
        'FilledVerySmallSquare' => '▪',
        'fjlig' => 'fj',
        'flat' => '♭',
        'fllig' => 'ﬂ',
        'fltns' => '▱',
        'fnof' => 'ƒ',
        'Fopf' => '𝔽',
        'fopf' => '𝕗',
        'ForAll' => '∀',
        'forall' => '∀',
        'fork' => '⋔',
        'forkv' => '⫙',
        'Fouriertrf' => 'ℱ',
        'fpartint' => '⨍',
        'frac12' => '½',
        'frac1' => '¼',
        'frac13' => '⅓',
        'frac14' => '¼',
        'frac15' => '⅕',
        'frac16' => '⅙',
        'frac18' => '⅛',
        'frac23' => '⅔',
        'frac25' => '⅖',
        'frac34' => '¾',
        'frac3' => '¾',
        'frac35' => '⅗',
        'frac38' => '⅜',
        'frac45' => '⅘',
        'frac56' => '⅚',
        'frac58' => '⅝',
        'frac78' => '⅞',
        'frasl' => '⁄',
        'frown' => '⌢',
        'Fscr' => 'ℱ',
        'fscr' => '𝒻',
        'gacute' => 'ǵ',
        'Gamma' => 'Γ',
        'gamma' => 'γ',
        'Gammad' => 'Ϝ',
        'gammad' => 'ϝ',
        'gap' => '⪆',
        'Gbreve' => 'Ğ',
        'gbreve' => 'ğ',
        'Gcedil' => 'Ģ',
        'Gcirc' => 'Ĝ',
        'gcirc' => 'ĝ',
        'Gcy' => 'Г',
        'gcy' => 'г',
        'Gdot' => 'Ġ',
        'gdot' => 'ġ',
        'gE' => '≧',
        'ge' => '≥',
        'gEl' => '⪌',
        'gel' => '⋛',
        'geq' => '≥',
        'geqq' => '≧',
        'geqslant' => '⩾',
        'ges' => '⩾',
        'gescc' => '⪩',
        'gesdot' => '⪀',
        'gesdoto' => '⪂',
        'gesdotol' => '⪄',
        'gesl' => '⋛︀',
        'gesles' => '⪔',
        'Gfr' => '𝔊',
        'gfr' => '𝔤',
        'Gg' => '⋙',
        'gg' => '≫',
        'ggg' => '⋙',
        'gimel' => 'ℷ',
        'GJcy' => 'Ѓ',
        'gjcy' => 'ѓ',
        'gl' => '≷',
        'gla' => '⪥',
        'glE' => '⪒',
        'glj' => '⪤',
        'gnap' => '⪊',
        'gnapprox' => '⪊',
        'gnE' => '≩',
        'gne' => '⪈',
        'gneq' => '⪈',
        'gneqq' => '≩',
        'gnsim' => '⋧',
        'Gopf' => '𝔾',
        'gopf' => '𝕘',
        'grave' => '`',
        'GreaterEqual' => '≥',
        'GreaterEqualLess' => '⋛',
        'GreaterFullEqual' => '≧',
        'GreaterGreater' => '⪢',
        'GreaterLess' => '≷',
        'GreaterSlantEqual' => '⩾',
        'GreaterTilde' => '≳',
        'Gscr' => '𝒢',
        'gscr' => 'ℊ',
        'gsim' => '≳',
        'gsime' => '⪎',
        'gsiml' => '⪐',
        'GT' => '>',
        'G' => '>',
        'Gt' => '≫',
        'gt' => '>',
        'g' => '>',
        'gtcc' => '⪧',
        'gtcir' => '⩺',
        'gtdot' => '⋗',
        'gtlPar' => '⦕',
        'gtquest' => '⩼',
        'gtrapprox' => '⪆',
        'gtrarr' => '⥸',
        'gtrdot' => '⋗',
        'gtreqless' => '⋛',
        'gtreqqless' => '⪌',
        'gtrless' => '≷',
        'gtrsim' => '≳',
        'gvertneqq' => '≩︀',
        'gvnE' => '≩︀',
        'Hacek' => 'ˇ',
        'hairsp' => ' ',
        'half' => '½',
        'hamilt' => 'ℋ',
        'HARDcy' => 'Ъ',
        'hardcy' => 'ъ',
        'hArr' => '⇔',
        'harr' => '↔',
        'harrcir' => '⥈',
        'harrw' => '↭',
        'Hat' => '^',
        'hbar' => 'ℏ',
        'Hcirc' => 'Ĥ',
        'hcirc' => 'ĥ',
        'hearts' => '♥',
        'heartsuit' => '♥',
        'hellip' => '…',
        'hercon' => '⊹',
        'Hfr' => 'ℌ',
        'hfr' => '𝔥',
        'HilbertSpace' => 'ℋ',
        'hksearow' => '⤥',
        'hkswarow' => '⤦',
        'hoarr' => '⇿',
        'homtht' => '∻',
        'hookleftarrow' => '↩',
        'hookrightarrow' => '↪',
        'Hopf' => 'ℍ',
        'hopf' => '𝕙',
        'horbar' => '―',
        'HorizontalLine' => '─',
        'Hscr' => 'ℋ',
        'hscr' => '𝒽',
        'hslash' => 'ℏ',
        'Hstrok' => 'Ħ',
        'hstrok' => 'ħ',
        'HumpDownHump' => '≎',
        'HumpEqual' => '≏',
        'hybull' => '⁃',
        'hyphen' => '‐',
        'Iacute' => 'Í',
        'Iacut' => 'Í',
        'iacute' => 'í',
        'iacut' => 'í',
        'ic' => '⁣',
        'Icirc' => 'Î',
        'Icir' => 'Î',
        'icirc' => 'î',
        'icir' => 'î',
        'Icy' => 'И',
        'icy' => 'и',
        'Idot' => 'İ',
        'IEcy' => 'Е',
        'iecy' => 'е',
        'iexcl' => '¡',
        'iexc' => '¡',
        'iff' => '⇔',
        'Ifr' => 'ℑ',
        'ifr' => '𝔦',
        'Igrave' => 'Ì',
        'Igrav' => 'Ì',
        'igrave' => 'ì',
        'igrav' => 'ì',
        'ii' => 'ⅈ',
        'iiiint' => '⨌',
        'iiint' => '∭',
        'iinfin' => '⧜',
        'iiota' => '℩',
        'IJlig' => 'Ĳ',
        'ijlig' => 'ĳ',
        'Im' => 'ℑ',
        'Imacr' => 'Ī',
        'imacr' => 'ī',
        'image' => 'ℑ',
        'ImaginaryI' => 'ⅈ',
        'imagline' => 'ℐ',
        'imagpart' => 'ℑ',
        'imath' => 'ı',
        'imof' => '⊷',
        'imped' => 'Ƶ',
        'Implies' => '⇒',
        'in' => '∈',
        'incare' => '℅',
        'infin' => '∞',
        'infintie' => '⧝',
        'inodot' => 'ı',
        'Int' => '∬',
        'int' => '∫',
        'intcal' => '⊺',
        'integers' => 'ℤ',
        'Integral' => '∫',
        'intercal' => '⊺',
        'Intersection' => '⋂',
        'intlarhk' => '⨗',
        'intprod' => '⨼',
        'InvisibleComma' => '⁣',
        'InvisibleTimes' => '⁢',
        'IOcy' => 'Ё',
        'iocy' => 'ё',
        'Iogon' => 'Į',
        'iogon' => 'į',
        'Iopf' => '𝕀',
        'iopf' => '𝕚',
        'Iota' => 'Ι',
        'iota' => 'ι',
        'iprod' => '⨼',
        'iquest' => '¿',
        'iques' => '¿',
        'Iscr' => 'ℐ',
        'iscr' => '𝒾',
        'isin' => '∈',
        'isindot' => '⋵',
        'isinE' => '⋹',
        'isins' => '⋴',
        'isinsv' => '⋳',
        'isinv' => '∈',
        'it' => '⁢',
        'Itilde' => 'Ĩ',
        'itilde' => 'ĩ',
        'Iukcy' => 'І',
        'iukcy' => 'і',
        'Iuml' => 'Ï',
        'Ium' => 'Ï',
        'iuml' => 'ï',
        'ium' => 'ï',
        'Jcirc' => 'Ĵ',
        'jcirc' => 'ĵ',
        'Jcy' => 'Й',
        'jcy' => 'й',
        'Jfr' => '𝔍',
        'jfr' => '𝔧',
        'jmath' => 'ȷ',
        'Jopf' => '𝕁',
        'jopf' => '𝕛',
        'Jscr' => '𝒥',
        'jscr' => '𝒿',
        'Jsercy' => 'Ј',
        'jsercy' => 'ј',
        'Jukcy' => 'Є',
        'jukcy' => 'є',
        'Kappa' => 'Κ',
        'kappa' => 'κ',
        'kappav' => 'ϰ',
        'Kcedil' => 'Ķ',
        'kcedil' => 'ķ',
        'Kcy' => 'К',
        'kcy' => 'к',
        'Kfr' => '𝔎',
        'kfr' => '𝔨',
        'kgreen' => 'ĸ',
        'KHcy' => 'Х',
        'khcy' => 'х',
        'KJcy' => 'Ќ',
        'kjcy' => 'ќ',
        'Kopf' => '𝕂',
        'kopf' => '𝕜',
        'Kscr' => '𝒦',
        'kscr' => '𝓀',
        'lAarr' => '⇚',
        'Lacute' => 'Ĺ',
        'lacute' => 'ĺ',
        'laemptyv' => '⦴',
        'lagran' => 'ℒ',
        'Lambda' => 'Λ',
        'lambda' => 'λ',
        'Lang' => '⟪',
        'lang' => '⟨',
        'langd' => '⦑',
        'langle' => '⟨',
        'lap' => '⪅',
        'Laplacetrf' => 'ℒ',
        'laquo' => '«',
        'laqu' => '«',
        'Larr' => '↞',
        'lArr' => '⇐',
        'larr' => '←',
        'larrb' => '⇤',
        'larrbfs' => '⤟',
        'larrfs' => '⤝',
        'larrhk' => '↩',
        'larrlp' => '↫',
        'larrpl' => '⤹',
        'larrsim' => '⥳',
        'larrtl' => '↢',
        'lat' => '⪫',
        'lAtail' => '⤛',
        'latail' => '⤙',
        'late' => '⪭',
        'lates' => '⪭︀',
        'lBarr' => '⤎',
        'lbarr' => '⤌',
        'lbbrk' => '❲',
        'lbrace' => '{',
        'lbrack' => '[',
        'lbrke' => '⦋',
        'lbrksld' => '⦏',
        'lbrkslu' => '⦍',
        'Lcaron' => 'Ľ',
        'lcaron' => 'ľ',
        'Lcedil' => 'Ļ',
        'lcedil' => 'ļ',
        'lceil' => '⌈',
        'lcub' => '{',
        'Lcy' => 'Л',
        'lcy' => 'л',
        'ldca' => '⤶',
        'ldquo' => '“',
        'ldquor' => '„',
        'ldrdhar' => '⥧',
        'ldrushar' => '⥋',
        'ldsh' => '↲',
        'lE' => '≦',
        'le' => '≤',
        'LeftAngleBracket' => '⟨',
        'LeftArrow' => '←',
        'Leftarrow' => '⇐',
        'leftarrow' => '←',
        'LeftArrowBar' => '⇤',
        'LeftArrowRightArrow' => '⇆',
        'leftarrowtail' => '↢',
        'LeftCeiling' => '⌈',
        'LeftDoubleBracket' => '⟦',
        'LeftDownTeeVector' => '⥡',
        'LeftDownVector' => '⇃',
        'LeftDownVectorBar' => '⥙',
        'LeftFloor' => '⌊',
        'leftharpoondown' => '↽',
        'leftharpoonup' => '↼',
        'leftleftarrows' => '⇇',
        'LeftRightArrow' => '↔',
        'Leftrightarrow' => '⇔',
        'leftrightarrow' => '↔',
        'leftrightarrows' => '⇆',
        'leftrightharpoons' => '⇋',
        'leftrightsquigarrow' => '↭',
        'LeftRightVector' => '⥎',
        'LeftTee' => '⊣',
        'LeftTeeArrow' => '↤',
        'LeftTeeVector' => '⥚',
        'leftthreetimes' => '⋋',
        'LeftTriangle' => '⊲',
        'LeftTriangleBar' => '⧏',
        'LeftTriangleEqual' => '⊴',
        'LeftUpDownVector' => '⥑',
        'LeftUpTeeVector' => '⥠',
        'LeftUpVector' => '↿',
        'LeftUpVectorBar' => '⥘',
        'LeftVector' => '↼',
        'LeftVectorBar' => '⥒',
        'lEg' => '⪋',
        'leg' => '⋚',
        'leq' => '≤',
        'leqq' => '≦',
        'leqslant' => '⩽',
        'les' => '⩽',
        'lescc' => '⪨',
        'lesdot' => '⩿',
        'lesdoto' => '⪁',
        'lesdotor' => '⪃',
        'lesg' => '⋚︀',
        'lesges' => '⪓',
        'lessapprox' => '⪅',
        'lessdot' => '⋖',
        'lesseqgtr' => '⋚',
        'lesseqqgtr' => '⪋',
        'LessEqualGreater' => '⋚',
        'LessFullEqual' => '≦',
        'LessGreater' => '≶',
        'lessgtr' => '≶',
        'LessLess' => '⪡',
        'lesssim' => '≲',
        'LessSlantEqual' => '⩽',
        'LessTilde' => '≲',
        'lfisht' => '⥼',
        'lfloor' => '⌊',
        'Lfr' => '𝔏',
        'lfr' => '𝔩',
        'lg' => '≶',
        'lgE' => '⪑',
        'lHar' => '⥢',
        'lhard' => '↽',
        'lharu' => '↼',
        'lharul' => '⥪',
        'lhblk' => '▄',
        'LJcy' => 'Љ',
        'ljcy' => 'љ',
        'Ll' => '⋘',
        'll' => '≪',
        'llarr' => '⇇',
        'llcorner' => '⌞',
        'Lleftarrow' => '⇚',
        'llhard' => '⥫',
        'lltri' => '◺',
        'Lmidot' => 'Ŀ',
        'lmidot' => 'ŀ',
        'lmoust' => '⎰',
        'lmoustache' => '⎰',
        'lnap' => '⪉',
        'lnapprox' => '⪉',
        'lnE' => '≨',
        'lne' => '⪇',
        'lneq' => '⪇',
        'lneqq' => '≨',
        'lnsim' => '⋦',
        'loang' => '⟬',
        'loarr' => '⇽',
        'lobrk' => '⟦',
        'LongLeftArrow' => '⟵',
        'Longleftarrow' => '⟸',
        'longleftarrow' => '⟵',
        'LongLeftRightArrow' => '⟷',
        'Longleftrightarrow' => '⟺',
        'longleftrightarrow' => '⟷',
        'longmapsto' => '⟼',
        'LongRightArrow' => '⟶',
        'Longrightarrow' => '⟹',
        'longrightarrow' => '⟶',
        'looparrowleft' => '↫',
        'looparrowright' => '↬',
        'lopar' => '⦅',
        'Lopf' => '𝕃',
        'lopf' => '𝕝',
        'loplus' => '⨭',
        'lotimes' => '⨴',
        'lowast' => '∗',
        'lowbar' => '_',
        'LowerLeftArrow' => '↙',
        'LowerRightArrow' => '↘',
        'loz' => '◊',
        'lozenge' => '◊',
        'lozf' => '⧫',
        'lpar' => '(',
        'lparlt' => '⦓',
        'lrarr' => '⇆',
        'lrcorner' => '⌟',
        'lrhar' => '⇋',
        'lrhard' => '⥭',
        'lrm' => '‎',
        'lrtri' => '⊿',
        'lsaquo' => '‹',
        'Lscr' => 'ℒ',
        'lscr' => '𝓁',
        'Lsh' => '↰',
        'lsh' => '↰',
        'lsim' => '≲',
        'lsime' => '⪍',
        'lsimg' => '⪏',
        'lsqb' => '[',
        'lsquo' => '‘',
        'lsquor' => '‚',
        'Lstrok' => 'Ł',
        'lstrok' => 'ł',
        'LT' => '<',
        'L' => '<',
        'Lt' => '≪',
        'lt' => '<',
        'l' => '<',
        'ltcc' => '⪦',
        'ltcir' => '⩹',
        'ltdot' => '⋖',
        'lthree' => '⋋',
        'ltimes' => '⋉',
        'ltlarr' => '⥶',
        'ltquest' => '⩻',
        'ltri' => '◃',
        'ltrie' => '⊴',
        'ltrif' => '◂',
        'ltrPar' => '⦖',
        'lurdshar' => '⥊',
        'luruhar' => '⥦',
        'lvertneqq' => '≨︀',
        'lvnE' => '≨︀',
        'macr' => '¯',
        'mac' => '¯',
        'male' => '♂',
        'malt' => '✠',
        'maltese' => '✠',
        'Map' => '⤅',
        'map' => '↦',
        'mapsto' => '↦',
        'mapstodown' => '↧',
        'mapstoleft' => '↤',
        'mapstoup' => '↥',
        'marker' => '▮',
        'mcomma' => '⨩',
        'Mcy' => 'М',
        'mcy' => 'м',
        'mdash' => '—',
        'mDDot' => '∺',
        'measuredangle' => '∡',
        'MediumSpace' => ' ',
        'Mellintrf' => 'ℳ',
        'Mfr' => '𝔐',
        'mfr' => '𝔪',
        'mho' => '℧',
        'micro' => 'µ',
        'micr' => 'µ',
        'mid' => '∣',
        'midast' => '*',
        'midcir' => '⫰',
        'middot' => '·',
        'middo' => '·',
        'minus' => '−',
        'minusb' => '⊟',
        'minusd' => '∸',
        'minusdu' => '⨪',
        'MinusPlus' => '∓',
        'mlcp' => '⫛',
        'mldr' => '…',
        'mnplus' => '∓',
        'models' => '⊧',
        'Mopf' => '𝕄',
        'mopf' => '𝕞',
        'mp' => '∓',
        'Mscr' => 'ℳ',
        'mscr' => '𝓂',
        'mstpos' => '∾',
        'Mu' => 'Μ',
        'mu' => 'μ',
        'multimap' => '⊸',
        'mumap' => '⊸',
        'nabla' => '∇',
        'Nacute' => 'Ń',
        'nacute' => 'ń',
        'nang' => '∠⃒',
        'nap' => '≉',
        'napE' => '⩰̸',
        'napid' => '≋̸',
        'napos' => 'ŉ',
        'napprox' => '≉',
        'natur' => '♮',
        'natural' => '♮',
        'naturals' => 'ℕ',
        'nbsp' => ' ',
        'nbs' => ' ',
        'nbump' => '≎̸',
        'nbumpe' => '≏̸',
        'ncap' => '⩃',
        'Ncaron' => 'Ň',
        'ncaron' => 'ň',
        'Ncedil' => 'Ņ',
        'ncedil' => 'ņ',
        'ncong' => '≇',
        'ncongdot' => '⩭̸',
        'ncup' => '⩂',
        'Ncy' => 'Н',
        'ncy' => 'н',
        'ndash' => '–',
        'ne' => '≠',
        'nearhk' => '⤤',
        'neArr' => '⇗',
        'nearr' => '↗',
        'nearrow' => '↗',
        'nedot' => '≐̸',
        'NegativeMediumSpace' => '​',
        'NegativeThickSpace' => '​',
        'NegativeThinSpace' => '​',
        'NegativeVeryThinSpace' => '​',
        'nequiv' => '≢',
        'nesear' => '⤨',
        'nesim' => '≂̸',
        'NestedGreaterGreater' => '≫',
        'NestedLessLess' => '≪',
        'NewLine' => '
',
        'nexist' => '∄',
        'nexists' => '∄',
        'Nfr' => '𝔑',
        'nfr' => '𝔫',
        'ngE' => '≧̸',
        'nge' => '≱',
        'ngeq' => '≱',
        'ngeqq' => '≧̸',
        'ngeqslant' => '⩾̸',
        'nges' => '⩾̸',
        'nGg' => '⋙̸',
        'ngsim' => '≵',
        'nGt' => '≫⃒',
        'ngt' => '≯',
        'ngtr' => '≯',
        'nGtv' => '≫̸',
        'nhArr' => '⇎',
        'nharr' => '↮',
        'nhpar' => '⫲',
        'ni' => '∋',
        'nis' => '⋼',
        'nisd' => '⋺',
        'niv' => '∋',
        'NJcy' => 'Њ',
        'njcy' => 'њ',
        'nlArr' => '⇍',
        'nlarr' => '↚',
        'nldr' => '‥',
        'nlE' => '≦̸',
        'nle' => '≰',
        'nLeftarrow' => '⇍',
        'nleftarrow' => '↚',
        'nLeftrightarrow' => '⇎',
        'nleftrightarrow' => '↮',
        'nleq' => '≰',
        'nleqq' => '≦̸',
        'nleqslant' => '⩽̸',
        'nles' => '⩽̸',
        'nless' => '≮',
        'nLl' => '⋘̸',
        'nlsim' => '≴',
        'nLt' => '≪⃒',
        'nlt' => '≮',
        'nltri' => '⋪',
        'nltrie' => '⋬',
        'nLtv' => '≪̸',
        'nmid' => '∤',
        'NoBreak' => '⁠',
        'NonBreakingSpace' => ' ',
        'Nopf' => 'ℕ',
        'nopf' => '𝕟',
        'Not' => '⫬',
        'not' => '¬',
        'no' => '¬',
        'NotCongruent' => '≢',
        'NotCupCap' => '≭',
        'NotDoubleVerticalBar' => '∦',
        'NotElement' => '∉',
        'NotEqual' => '≠',
        'NotEqualTilde' => '≂̸',
        'NotExists' => '∄',
        'NotGreater' => '≯',
        'NotGreaterEqual' => '≱',
        'NotGreaterFullEqual' => '≧̸',
        'NotGreaterGreater' => '≫̸',
        'NotGreaterLess' => '≹',
        'NotGreaterSlantEqual' => '⩾̸',
        'NotGreaterTilde' => '≵',
        'NotHumpDownHump' => '≎̸',
        'NotHumpEqual' => '≏̸',
        'notin' => '∉',
        'notindot' => '⋵̸',
        'notinE' => '⋹̸',
        'notinva' => '∉',
        'notinvb' => '⋷',
        'notinvc' => '⋶',
        'NotLeftTriangle' => '⋪',
        'NotLeftTriangleBar' => '⧏̸',
        'NotLeftTriangleEqual' => '⋬',
        'NotLess' => '≮',
        'NotLessEqual' => '≰',
        'NotLessGreater' => '≸',
        'NotLessLess' => '≪̸',
        'NotLessSlantEqual' => '⩽̸',
        'NotLessTilde' => '≴',
        'NotNestedGreaterGreater' => '⪢̸',
        'NotNestedLessLess' => '⪡̸',
        'notni' => '∌',
        'notniva' => '∌',
        'notnivb' => '⋾',
        'notnivc' => '⋽',
        'NotPrecedes' => '⊀',
        'NotPrecedesEqual' => '⪯̸',
        'NotPrecedesSlantEqual' => '⋠',
        'NotReverseElement' => '∌',
        'NotRightTriangle' => '⋫',
        'NotRightTriangleBar' => '⧐̸',
        'NotRightTriangleEqual' => '⋭',
        'NotSquareSubset' => '⊏̸',
        'NotSquareSubsetEqual' => '⋢',
        'NotSquareSuperset' => '⊐̸',
        'NotSquareSupersetEqual' => '⋣',
        'NotSubset' => '⊂⃒',
        'NotSubsetEqual' => '⊈',
        'NotSucceeds' => '⊁',
        'NotSucceedsEqual' => '⪰̸',
        'NotSucceedsSlantEqual' => '⋡',
        'NotSucceedsTilde' => '≿̸',
        'NotSuperset' => '⊃⃒',
        'NotSupersetEqual' => '⊉',
        'NotTilde' => '≁',
        'NotTildeEqual' => '≄',
        'NotTildeFullEqual' => '≇',
        'NotTildeTilde' => '≉',
        'NotVerticalBar' => '∤',
        'npar' => '∦',
        'nparallel' => '∦',
        'nparsl' => '⫽⃥',
        'npart' => '∂̸',
        'npolint' => '⨔',
        'npr' => '⊀',
        'nprcue' => '⋠',
        'npre' => '⪯̸',
        'nprec' => '⊀',
        'npreceq' => '⪯̸',
        'nrArr' => '⇏',
        'nrarr' => '↛',
        'nrarrc' => '⤳̸',
        'nrarrw' => '↝̸',
        'nRightarrow' => '⇏',
        'nrightarrow' => '↛',
        'nrtri' => '⋫',
        'nrtrie' => '⋭',
        'nsc' => '⊁',
        'nsccue' => '⋡',
        'nsce' => '⪰̸',
        'Nscr' => '𝒩',
        'nscr' => '𝓃',
        'nshortmid' => '∤',
        'nshortparallel' => '∦',
        'nsim' => '≁',
        'nsime' => '≄',
        'nsimeq' => '≄',
        'nsmid' => '∤',
        'nspar' => '∦',
        'nsqsube' => '⋢',
        'nsqsupe' => '⋣',
        'nsub' => '⊄',
        'nsubE' => '⫅̸',
        'nsube' => '⊈',
        'nsubset' => '⊂⃒',
        'nsubseteq' => '⊈',
        'nsubseteqq' => '⫅̸',
        'nsucc' => '⊁',
        'nsucceq' => '⪰̸',
        'nsup' => '⊅',
        'nsupE' => '⫆̸',
        'nsupe' => '⊉',
        'nsupset' => '⊃⃒',
        'nsupseteq' => '⊉',
        'nsupseteqq' => '⫆̸',
        'ntgl' => '≹',
        'Ntilde' => 'Ñ',
        'Ntild' => 'Ñ',
        'ntilde' => 'ñ',
        'ntild' => 'ñ',
        'ntlg' => '≸',
        'ntriangleleft' => '⋪',
        'ntrianglelefteq' => '⋬',
        'ntriangleright' => '⋫',
        'ntrianglerighteq' => '⋭',
        'Nu' => 'Ν',
        'nu' => 'ν',
        'num' => '#',
        'numero' => '№',
        'numsp' => ' ',
        'nvap' => '≍⃒',
        'nVDash' => '⊯',
        'nVdash' => '⊮',
        'nvDash' => '⊭',
        'nvdash' => '⊬',
        'nvge' => '≥⃒',
        'nvgt' => '>⃒',
        'nvHarr' => '⤄',
        'nvinfin' => '⧞',
        'nvlArr' => '⤂',
        'nvle' => '≤⃒',
        'nvlt' => '<⃒',
        'nvltrie' => '⊴⃒',
        'nvrArr' => '⤃',
        'nvrtrie' => '⊵⃒',
        'nvsim' => '∼⃒',
        'nwarhk' => '⤣',
        'nwArr' => '⇖',
        'nwarr' => '↖',
        'nwarrow' => '↖',
        'nwnear' => '⤧',
        'Oacute' => 'Ó',
        'Oacut' => 'Ó',
        'oacute' => 'ó',
        'oacut' => 'ó',
        'oast' => '⊛',
        'ocir' => 'ô',
        'Ocirc' => 'Ô',
        'Ocir' => 'Ô',
        'ocirc' => 'ô',
        'Ocy' => 'О',
        'ocy' => 'о',
        'odash' => '⊝',
        'Odblac' => 'Ő',
        'odblac' => 'ő',
        'odiv' => '⨸',
        'odot' => '⊙',
        'odsold' => '⦼',
        'OElig' => 'Œ',
        'oelig' => 'œ',
        'ofcir' => '⦿',
        'Ofr' => '𝔒',
        'ofr' => '𝔬',
        'ogon' => '˛',
        'Ograve' => 'Ò',
        'Ograv' => 'Ò',
        'ograve' => 'ò',
        'ograv' => 'ò',
        'ogt' => '⧁',
        'ohbar' => '⦵',
        'ohm' => 'Ω',
        'oint' => '∮',
        'olarr' => '↺',
        'olcir' => '⦾',
        'olcross' => '⦻',
        'oline' => '‾',
        'olt' => '⧀',
        'Omacr' => 'Ō',
        'omacr' => 'ō',
        'Omega' => 'Ω',
        'omega' => 'ω',
        'Omicron' => 'Ο',
        'omicron' => 'ο',
        'omid' => '⦶',
        'ominus' => '⊖',
        'Oopf' => '𝕆',
        'oopf' => '𝕠',
        'opar' => '⦷',
        'OpenCurlyDoubleQuote' => '“',
        'OpenCurlyQuote' => '‘',
        'operp' => '⦹',
        'oplus' => '⊕',
        'Or' => '⩔',
        'or' => '∨',
        'orarr' => '↻',
        'ord' => 'º',
        'order' => 'ℴ',
        'orderof' => 'ℴ',
        'ordf' => 'ª',
        'ordm' => 'º',
        'origof' => '⊶',
        'oror' => '⩖',
        'orslope' => '⩗',
        'orv' => '⩛',
        'oS' => 'Ⓢ',
        'Oscr' => '𝒪',
        'oscr' => 'ℴ',
        'Oslash' => 'Ø',
        'Oslas' => 'Ø',
        'oslash' => 'ø',
        'oslas' => 'ø',
        'osol' => '⊘',
        'Otilde' => 'Õ',
        'Otild' => 'Õ',
        'otilde' => 'õ',
        'otild' => 'õ',
        'Otimes' => '⨷',
        'otimes' => '⊗',
        'otimesas' => '⨶',
        'Ouml' => 'Ö',
        'Oum' => 'Ö',
        'ouml' => 'ö',
        'oum' => 'ö',
        'ovbar' => '⌽',
        'OverBar' => '‾',
        'OverBrace' => '⏞',
        'OverBracket' => '⎴',
        'OverParenthesis' => '⏜',
        'par' => '¶',
        'para' => '¶',
        'parallel' => '∥',
        'parsim' => '⫳',
        'parsl' => '⫽',
        'part' => '∂',
        'PartialD' => '∂',
        'Pcy' => 'П',
        'pcy' => 'п',
        'percnt' => '%',
        'period' => '.',
        'permil' => '‰',
        'perp' => '⊥',
        'pertenk' => '‱',
        'Pfr' => '𝔓',
        'pfr' => '𝔭',
        'Phi' => 'Φ',
        'phi' => 'φ',
        'phiv' => 'ϕ',
        'phmmat' => 'ℳ',
        'phone' => '☎',
        'Pi' => 'Π',
        'pi' => 'π',
        'pitchfork' => '⋔',
        'piv' => 'ϖ',
        'planck' => 'ℏ',
        'planckh' => 'ℎ',
        'plankv' => 'ℏ',
        'plus' => '+',
        'plusacir' => '⨣',
        'plusb' => '⊞',
        'pluscir' => '⨢',
        'plusdo' => '∔',
        'plusdu' => '⨥',
        'pluse' => '⩲',
        'PlusMinus' => '±',
        'plusmn' => '±',
        'plusm' => '±',
        'plussim' => '⨦',
        'plustwo' => '⨧',
        'pm' => '±',
        'Poincareplane' => 'ℌ',
        'pointint' => '⨕',
        'Popf' => 'ℙ',
        'popf' => '𝕡',
        'pound' => '£',
        'poun' => '£',
        'Pr' => '⪻',
        'pr' => '≺',
        'prap' => '⪷',
        'prcue' => '≼',
        'prE' => '⪳',
        'pre' => '⪯',
        'prec' => '≺',
        'precapprox' => '⪷',
        'preccurlyeq' => '≼',
        'Precedes' => '≺',
        'PrecedesEqual' => '⪯',
        'PrecedesSlantEqual' => '≼',
        'PrecedesTilde' => '≾',
        'preceq' => '⪯',
        'precnapprox' => '⪹',
        'precneqq' => '⪵',
        'precnsim' => '⋨',
        'precsim' => '≾',
        'Prime' => '″',
        'prime' => '′',
        'primes' => 'ℙ',
        'prnap' => '⪹',
        'prnE' => '⪵',
        'prnsim' => '⋨',
        'prod' => '∏',
        'Product' => '∏',
        'profalar' => '⌮',
        'profline' => '⌒',
        'profsurf' => '⌓',
        'prop' => '∝',
        'Proportion' => '∷',
        'Proportional' => '∝',
        'propto' => '∝',
        'prsim' => '≾',
        'prurel' => '⊰',
        'Pscr' => '𝒫',
        'pscr' => '𝓅',
        'Psi' => 'Ψ',
        'psi' => 'ψ',
        'puncsp' => ' ',
        'Qfr' => '𝔔',
        'qfr' => '𝔮',
        'qint' => '⨌',
        'Qopf' => 'ℚ',
        'qopf' => '𝕢',
        'qprime' => '⁗',
        'Qscr' => '𝒬',
        'qscr' => '𝓆',
        'quaternions' => 'ℍ',
        'quatint' => '⨖',
        'quest' => '?',
        'questeq' => '≟',
        'QUOT' => '"',
        'QUO' => '"',
        'quot' => '"',
        'quo' => '"',
        'rAarr' => '⇛',
        'race' => '∽̱',
        'Racute' => 'Ŕ',
        'racute' => 'ŕ',
        'radic' => '√',
        'raemptyv' => '⦳',
        'Rang' => '⟫',
        'rang' => '⟩',
        'rangd' => '⦒',
        'range' => '⦥',
        'rangle' => '⟩',
        'raquo' => '»',
        'raqu' => '»',
        'Rarr' => '↠',
        'rArr' => '⇒',
        'rarr' => '→',
        'rarrap' => '⥵',
        'rarrb' => '⇥',
        'rarrbfs' => '⤠',
        'rarrc' => '⤳',
        'rarrfs' => '⤞',
        'rarrhk' => '↪',
        'rarrlp' => '↬',
        'rarrpl' => '⥅',
        'rarrsim' => '⥴',
        'Rarrtl' => '⤖',
        'rarrtl' => '↣',
        'rarrw' => '↝',
        'rAtail' => '⤜',
        'ratail' => '⤚',
        'ratio' => '∶',
        'rationals' => 'ℚ',
        'RBarr' => '⤐',
        'rBarr' => '⤏',
        'rbarr' => '⤍',
        'rbbrk' => '❳',
        'rbrace' => '}',
        'rbrack' => ']',
        'rbrke' => '⦌',
        'rbrksld' => '⦎',
        'rbrkslu' => '⦐',
        'Rcaron' => 'Ř',
        'rcaron' => 'ř',
        'Rcedil' => 'Ŗ',
        'rcedil' => 'ŗ',
        'rceil' => '⌉',
        'rcub' => '}',
        'Rcy' => 'Р',
        'rcy' => 'р',
        'rdca' => '⤷',
        'rdldhar' => '⥩',
        'rdquo' => '”',
        'rdquor' => '”',
        'rdsh' => '↳',
        'Re' => 'ℜ',
        'real' => 'ℜ',
        'realine' => 'ℛ',
        'realpart' => 'ℜ',
        'reals' => 'ℝ',
        'rect' => '▭',
        'REG' => '®',
        'RE' => '®',
        'reg' => '®',
        're' => '®',
        'ReverseElement' => '∋',
        'ReverseEquilibrium' => '⇋',
        'ReverseUpEquilibrium' => '⥯',
        'rfisht' => '⥽',
        'rfloor' => '⌋',
        'Rfr' => 'ℜ',
        'rfr' => '𝔯',
        'rHar' => '⥤',
        'rhard' => '⇁',
        'rharu' => '⇀',
        'rharul' => '⥬',
        'Rho' => 'Ρ',
        'rho' => 'ρ',
        'rhov' => 'ϱ',
        'RightAngleBracket' => '⟩',
        'RightArrow' => '→',
        'Rightarrow' => '⇒',
        'rightarrow' => '→',
        'RightArrowBar' => '⇥',
        'RightArrowLeftArrow' => '⇄',
        'rightarrowtail' => '↣',
        'RightCeiling' => '⌉',
        'RightDoubleBracket' => '⟧',
        'RightDownTeeVector' => '⥝',
        'RightDownVector' => '⇂',
        'RightDownVectorBar' => '⥕',
        'RightFloor' => '⌋',
        'rightharpoondown' => '⇁',
        'rightharpoonup' => '⇀',
        'rightleftarrows' => '⇄',
        'rightleftharpoons' => '⇌',
        'rightrightarrows' => '⇉',
        'rightsquigarrow' => '↝',
        'RightTee' => '⊢',
        'RightTeeArrow' => '↦',
        'RightTeeVector' => '⥛',
        'rightthreetimes' => '⋌',
        'RightTriangle' => '⊳',
        'RightTriangleBar' => '⧐',
        'RightTriangleEqual' => '⊵',
        'RightUpDownVector' => '⥏',
        'RightUpTeeVector' => '⥜',
        'RightUpVector' => '↾',
        'RightUpVectorBar' => '⥔',
        'RightVector' => '⇀',
        'RightVectorBar' => '⥓',
        'ring' => '˚',
        'risingdotseq' => '≓',
        'rlarr' => '⇄',
        'rlhar' => '⇌',
        'rlm' => '‏',
        'rmoust' => '⎱',
        'rmoustache' => '⎱',
        'rnmid' => '⫮',
        'roang' => '⟭',
        'roarr' => '⇾',
        'robrk' => '⟧',
        'ropar' => '⦆',
        'Ropf' => 'ℝ',
        'ropf' => '𝕣',
        'roplus' => '⨮',
        'rotimes' => '⨵',
        'RoundImplies' => '⥰',
        'rpar' => ')',
        'rpargt' => '⦔',
        'rppolint' => '⨒',
        'rrarr' => '⇉',
        'Rrightarrow' => '⇛',
        'rsaquo' => '›',
        'Rscr' => 'ℛ',
        'rscr' => '𝓇',
        'Rsh' => '↱',
        'rsh' => '↱',
        'rsqb' => ']',
        'rsquo' => '’',
        'rsquor' => '’',
        'rthree' => '⋌',
        'rtimes' => '⋊',
        'rtri' => '▹',
        'rtrie' => '⊵',
        'rtrif' => '▸',
        'rtriltri' => '⧎',
        'RuleDelayed' => '⧴',
        'ruluhar' => '⥨',
        'rx' => '℞',
        'Sacute' => 'Ś',
        'sacute' => 'ś',
        'sbquo' => '‚',
        'Sc' => '⪼',
        'sc' => '≻',
        'scap' => '⪸',
        'Scaron' => 'Š',
        'scaron' => 'š',
        'sccue' => '≽',
        'scE' => '⪴',
        'sce' => '⪰',
        'Scedil' => 'Ş',
        'scedil' => 'ş',
        'Scirc' => 'Ŝ',
        'scirc' => 'ŝ',
        'scnap' => '⪺',
        'scnE' => '⪶',
        'scnsim' => '⋩',
        'scpolint' => '⨓',
        'scsim' => '≿',
        'Scy' => 'С',
        'scy' => 'с',
        'sdot' => '⋅',
        'sdotb' => '⊡',
        'sdote' => '⩦',
        'searhk' => '⤥',
        'seArr' => '⇘',
        'searr' => '↘',
        'searrow' => '↘',
        'sect' => '§',
        'sec' => '§',
        'semi' => ';',
        'seswar' => '⤩',
        'setminus' => '∖',
        'setmn' => '∖',
        'sext' => '✶',
        'Sfr' => '𝔖',
        'sfr' => '𝔰',
        'sfrown' => '⌢',
        'sharp' => '♯',
        'SHCHcy' => 'Щ',
        'shchcy' => 'щ',
        'SHcy' => 'Ш',
        'shcy' => 'ш',
        'ShortDownArrow' => '↓',
        'ShortLeftArrow' => '←',
        'shortmid' => '∣',
        'shortparallel' => '∥',
        'ShortRightArrow' => '→',
        'ShortUpArrow' => '↑',
        'shy' => '­',
        'sh' => '­',
        'Sigma' => 'Σ',
        'sigma' => 'σ',
        'sigmaf' => 'ς',
        'sigmav' => 'ς',
        'sim' => '∼',
        'simdot' => '⩪',
        'sime' => '≃',
        'simeq' => '≃',
        'simg' => '⪞',
        'simgE' => '⪠',
        'siml' => '⪝',
        'simlE' => '⪟',
        'simne' => '≆',
        'simplus' => '⨤',
        'simrarr' => '⥲',
        'slarr' => '←',
        'SmallCircle' => '∘',
        'smallsetminus' => '∖',
        'smashp' => '⨳',
        'smeparsl' => '⧤',
        'smid' => '∣',
        'smile' => '⌣',
        'smt' => '⪪',
        'smte' => '⪬',
        'smtes' => '⪬︀',
        'SOFTcy' => 'Ь',
        'softcy' => 'ь',
        'sol' => '/',
        'solb' => '⧄',
        'solbar' => '⌿',
        'Sopf' => '𝕊',
        'sopf' => '𝕤',
        'spades' => '♠',
        'spadesuit' => '♠',
        'spar' => '∥',
        'sqcap' => '⊓',
        'sqcaps' => '⊓︀',
        'sqcup' => '⊔',
        'sqcups' => '⊔︀',
        'Sqrt' => '√',
        'sqsub' => '⊏',
        'sqsube' => '⊑',
        'sqsubset' => '⊏',
        'sqsubseteq' => '⊑',
        'sqsup' => '⊐',
        'sqsupe' => '⊒',
        'sqsupset' => '⊐',
        'sqsupseteq' => '⊒',
        'squ' => '□',
        'Square' => '□',
        'square' => '□',
        'SquareIntersection' => '⊓',
        'SquareSubset' => '⊏',
        'SquareSubsetEqual' => '⊑',
        'SquareSuperset' => '⊐',
        'SquareSupersetEqual' => '⊒',
        'SquareUnion' => '⊔',
        'squarf' => '▪',
        'squf' => '▪',
        'srarr' => '→',
        'Sscr' => '𝒮',
        'sscr' => '𝓈',
        'ssetmn' => '∖',
        'ssmile' => '⌣',
        'sstarf' => '⋆',
        'Star' => '⋆',
        'star' => '☆',
        'starf' => '★',
        'straightepsilon' => 'ϵ',
        'straightphi' => 'ϕ',
        'strns' => '¯',
        'Sub' => '⋐',
        'sub' => '⊂',
        'subdot' => '⪽',
        'subE' => '⫅',
        'sube' => '⊆',
        'subedot' => '⫃',
        'submult' => '⫁',
        'subnE' => '⫋',
        'subne' => '⊊',
        'subplus' => '⪿',
        'subrarr' => '⥹',
        'Subset' => '⋐',
        'subset' => '⊂',
        'subseteq' => '⊆',
        'subseteqq' => '⫅',
        'SubsetEqual' => '⊆',
        'subsetneq' => '⊊',
        'subsetneqq' => '⫋',
        'subsim' => '⫇',
        'subsub' => '⫕',
        'subsup' => '⫓',
        'succ' => '≻',
        'succapprox' => '⪸',
        'succcurlyeq' => '≽',
        'Succeeds' => '≻',
        'SucceedsEqual' => '⪰',
        'SucceedsSlantEqual' => '≽',
        'SucceedsTilde' => '≿',
        'succeq' => '⪰',
        'succnapprox' => '⪺',
        'succneqq' => '⪶',
        'succnsim' => '⋩',
        'succsim' => '≿',
        'SuchThat' => '∋',
        'Sum' => '∑',
        'sum' => '∑',
        'sung' => '♪',
        'Sup' => '⋑',
        'sup' => '³',
        'sup1' => '¹',
        'sup2' => '²',
        'sup3' => '³',
        'supdot' => '⪾',
        'supdsub' => '⫘',
        'supE' => '⫆',
        'supe' => '⊇',
        'supedot' => '⫄',
        'Superset' => '⊃',
        'SupersetEqual' => '⊇',
        'suphsol' => '⟉',
        'suphsub' => '⫗',
        'suplarr' => '⥻',
        'supmult' => '⫂',
        'supnE' => '⫌',
        'supne' => '⊋',
        'supplus' => '⫀',
        'Supset' => '⋑',
        'supset' => '⊃',
        'supseteq' => '⊇',
        'supseteqq' => '⫆',
        'supsetneq' => '⊋',
        'supsetneqq' => '⫌',
        'supsim' => '⫈',
        'supsub' => '⫔',
        'supsup' => '⫖',
        'swarhk' => '⤦',
        'swArr' => '⇙',
        'swarr' => '↙',
        'swarrow' => '↙',
        'swnwar' => '⤪',
        'szlig' => 'ß',
        'szli' => 'ß',
        'Tab' => '	',
        'target' => '⌖',
        'Tau' => 'Τ',
        'tau' => 'τ',
        'tbrk' => '⎴',
        'Tcaron' => 'Ť',
        'tcaron' => 'ť',
        'Tcedil' => 'Ţ',
        'tcedil' => 'ţ',
        'Tcy' => 'Т',
        'tcy' => 'т',
        'tdot' => '⃛',
        'telrec' => '⌕',
        'Tfr' => '𝔗',
        'tfr' => '𝔱',
        'there4' => '∴',
        'Therefore' => '∴',
        'therefore' => '∴',
        'Theta' => 'Θ',
        'theta' => 'θ',
        'thetasym' => 'ϑ',
        'thetav' => 'ϑ',
        'thickapprox' => '≈',
        'thicksim' => '∼',
        'ThickSpace' => '  ',
        'thinsp' => ' ',
        'ThinSpace' => ' ',
        'thkap' => '≈',
        'thksim' => '∼',
        'THORN' => 'Þ',
        'THOR' => 'Þ',
        'thorn' => 'þ',
        'thor' => 'þ',
        'Tilde' => '∼',
        'tilde' => '˜',
        'TildeEqual' => '≃',
        'TildeFullEqual' => '≅',
        'TildeTilde' => '≈',
        'times' => '×',
        'time' => '×',
        'timesb' => '⊠',
        'timesbar' => '⨱',
        'timesd' => '⨰',
        'tint' => '∭',
        'toea' => '⤨',
        'top' => '⊤',
        'topbot' => '⌶',
        'topcir' => '⫱',
        'Topf' => '𝕋',
        'topf' => '𝕥',
        'topfork' => '⫚',
        'tosa' => '⤩',
        'tprime' => '‴',
        'TRADE' => '™',
        'trade' => '™',
        'triangle' => '▵',
        'triangledown' => '▿',
        'triangleleft' => '◃',
        'trianglelefteq' => '⊴',
        'triangleq' => '≜',
        'triangleright' => '▹',
        'trianglerighteq' => '⊵',
        'tridot' => '◬',
        'trie' => '≜',
        'triminus' => '⨺',
        'TripleDot' => '⃛',
        'triplus' => '⨹',
        'trisb' => '⧍',
        'tritime' => '⨻',
        'trpezium' => '⏢',
        'Tscr' => '𝒯',
        'tscr' => '𝓉',
        'TScy' => 'Ц',
        'tscy' => 'ц',
        'TSHcy' => 'Ћ',
        'tshcy' => 'ћ',
        'Tstrok' => 'Ŧ',
        'tstrok' => 'ŧ',
        'twixt' => '≬',
        'twoheadleftarrow' => '↞',
        'twoheadrightarrow' => '↠',
        'Uacute' => 'Ú',
        'Uacut' => 'Ú',
        'uacute' => 'ú',
        'uacut' => 'ú',
        'Uarr' => '↟',
        'uArr' => '⇑',
        'uarr' => '↑',
        'Uarrocir' => '⥉',
        'Ubrcy' => 'Ў',
        'ubrcy' => 'ў',
        'Ubreve' => 'Ŭ',
        'ubreve' => 'ŭ',
        'Ucirc' => 'Û',
        'Ucir' => 'Û',
        'ucirc' => 'û',
        'ucir' => 'û',
        'Ucy' => 'У',
        'ucy' => 'у',
        'udarr' => '⇅',
        'Udblac' => 'Ű',
        'udblac' => 'ű',
        'udhar' => '⥮',
        'ufisht' => '⥾',
        'Ufr' => '𝔘',
        'ufr' => '𝔲',
        'Ugrave' => 'Ù',
        'Ugrav' => 'Ù',
        'ugrave' => 'ù',
        'ugrav' => 'ù',
        'uHar' => '⥣',
        'uharl' => '↿',
        'uharr' => '↾',
        'uhblk' => '▀',
        'ulcorn' => '⌜',
        'ulcorner' => '⌜',
        'ulcrop' => '⌏',
        'ultri' => '◸',
        'Umacr' => 'Ū',
        'umacr' => 'ū',
        'uml' => '¨',
        'um' => '¨',
        'UnderBar' => '_',
        'UnderBrace' => '⏟',
        'UnderBracket' => '⎵',
        'UnderParenthesis' => '⏝',
        'Union' => '⋃',
        'UnionPlus' => '⊎',
        'Uogon' => 'Ų',
        'uogon' => 'ų',
        'Uopf' => '𝕌',
        'uopf' => '𝕦',
        'UpArrow' => '↑',
        'Uparrow' => '⇑',
        'uparrow' => '↑',
        'UpArrowBar' => '⤒',
        'UpArrowDownArrow' => '⇅',
        'UpDownArrow' => '↕',
        'Updownarrow' => '⇕',
        'updownarrow' => '↕',
        'UpEquilibrium' => '⥮',
        'upharpoonleft' => '↿',
        'upharpoonright' => '↾',
        'uplus' => '⊎',
        'UpperLeftArrow' => '↖',
        'UpperRightArrow' => '↗',
        'Upsi' => 'ϒ',
        'upsi' => 'υ',
        'upsih' => 'ϒ',
        'Upsilon' => 'Υ',
        'upsilon' => 'υ',
        'UpTee' => '⊥',
        'UpTeeArrow' => '↥',
        'upuparrows' => '⇈',
        'urcorn' => '⌝',
        'urcorner' => '⌝',
        'urcrop' => '⌎',
        'Uring' => 'Ů',
        'uring' => 'ů',
        'urtri' => '◹',
        'Uscr' => '𝒰',
        'uscr' => '𝓊',
        'utdot' => '⋰',
        'Utilde' => 'Ũ',
        'utilde' => 'ũ',
        'utri' => '▵',
        'utrif' => '▴',
        'uuarr' => '⇈',
        'Uuml' => 'Ü',
        'Uum' => 'Ü',
        'uuml' => 'ü',
        'uum' => 'ü',
        'uwangle' => '⦧',
        'vangrt' => '⦜',
        'varepsilon' => 'ϵ',
        'varkappa' => 'ϰ',
        'varnothing' => '∅',
        'varphi' => 'ϕ',
        'varpi' => 'ϖ',
        'varpropto' => '∝',
        'vArr' => '⇕',
        'varr' => '↕',
        'varrho' => 'ϱ',
        'varsigma' => 'ς',
        'varsubsetneq' => '⊊︀',
        'varsubsetneqq' => '⫋︀',
        'varsupsetneq' => '⊋︀',
        'varsupsetneqq' => '⫌︀',
        'vartheta' => 'ϑ',
        'vartriangleleft' => '⊲',
        'vartriangleright' => '⊳',
        'Vbar' => '⫫',
        'vBar' => '⫨',
        'vBarv' => '⫩',
        'Vcy' => 'В',
        'vcy' => 'в',
        'VDash' => '⊫',
        'Vdash' => '⊩',
        'vDash' => '⊨',
        'vdash' => '⊢',
        'Vdashl' => '⫦',
        'Vee' => '⋁',
        'vee' => '∨',
        'veebar' => '⊻',
        'veeeq' => '≚',
        'vellip' => '⋮',
        'Verbar' => '‖',
        'verbar' => '|',
        'Vert' => '‖',
        'vert' => '|',
        'VerticalBar' => '∣',
        'VerticalLine' => '|',
        'VerticalSeparator' => '❘',
        'VerticalTilde' => '≀',
        'VeryThinSpace' => ' ',
        'Vfr' => '𝔙',
        'vfr' => '𝔳',
        'vltri' => '⊲',
        'vnsub' => '⊂⃒',
        'vnsup' => '⊃⃒',
        'Vopf' => '𝕍',
        'vopf' => '𝕧',
        'vprop' => '∝',
        'vrtri' => '⊳',
        'Vscr' => '𝒱',
        'vscr' => '𝓋',
        'vsubnE' => '⫋︀',
        'vsubne' => '⊊︀',
        'vsupnE' => '⫌︀',
        'vsupne' => '⊋︀',
        'Vvdash' => '⊪',
        'vzigzag' => '⦚',
        'Wcirc' => 'Ŵ',
        'wcirc' => 'ŵ',
        'wedbar' => '⩟',
        'Wedge' => '⋀',
        'wedge' => '∧',
        'wedgeq' => '≙',
        'weierp' => '℘',
        'Wfr' => '𝔚',
        'wfr' => '𝔴',
        'Wopf' => '𝕎',
        'wopf' => '𝕨',
        'wp' => '℘',
        'wr' => '≀',
        'wreath' => '≀',
        'Wscr' => '𝒲',
        'wscr' => '𝓌',
        'xcap' => '⋂',
        'xcirc' => '◯',
        'xcup' => '⋃',
        'xdtri' => '▽',
        'Xfr' => '𝔛',
        'xfr' => '𝔵',
        'xhArr' => '⟺',
        'xharr' => '⟷',
        'Xi' => 'Ξ',
        'xi' => 'ξ',
        'xlArr' => '⟸',
        'xlarr' => '⟵',
        'xmap' => '⟼',
        'xnis' => '⋻',
        'xodot' => '⨀',
        'Xopf' => '𝕏',
        'xopf' => '𝕩',
        'xoplus' => '⨁',
        'xotime' => '⨂',
        'xrArr' => '⟹',
        'xrarr' => '⟶',
        'Xscr' => '𝒳',
        'xscr' => '𝓍',
        'xsqcup' => '⨆',
        'xuplus' => '⨄',
        'xutri' => '△',
        'xvee' => '⋁',
        'xwedge' => '⋀',
        'Yacute' => 'Ý',
        'Yacut' => 'Ý',
        'yacute' => 'ý',
        'yacut' => 'ý',
        'YAcy' => 'Я',
        'yacy' => 'я',
        'Ycirc' => 'Ŷ',
        'ycirc' => 'ŷ',
        'Ycy' => 'Ы',
        'ycy' => 'ы',
        'yen' => '¥',
        'ye' => '¥',
        'Yfr' => '𝔜',
        'yfr' => '𝔶',
        'YIcy' => 'Ї',
        'yicy' => 'ї',
        'Yopf' => '𝕐',
        'yopf' => '𝕪',
        'Yscr' => '𝒴',
        'yscr' => '𝓎',
        'YUcy' => 'Ю',
        'yucy' => 'ю',
        'Yuml' => 'Ÿ',
        'yuml' => 'ÿ',
        'yum' => 'ÿ',
        'Zacute' => 'Ź',
        'zacute' => 'ź',
        'Zcaron' => 'Ž',
        'zcaron' => 'ž',
        'Zcy' => 'З',
        'zcy' => 'з',
        'Zdot' => 'Ż',
        'zdot' => 'ż',
        'zeetrf' => 'ℨ',
        'ZeroWidthSpace' => '​',
        'Zeta' => 'Ζ',
        'zeta' => 'ζ',
        'Zfr' => 'ℨ',
        'zfr' => '𝔷',
        'ZHcy' => 'Ж',
        'zhcy' => 'ж',
        'zigrarr' => '⇝',
        'Zopf' => 'ℤ',
        'zopf' => '𝕫',
        'Zscr' => '𝒵',
        'zscr' => '𝓏',
        'zwj' => '‍',
        'zwnj' => '‌',
    );
}
