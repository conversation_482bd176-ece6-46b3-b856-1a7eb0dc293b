#!/bin/bash

# Find available port and fix Apache configuration
echo "🔍 Checking what's using ports..."

# Check what's using port 8080
echo "Port 8080 usage:"
netstat -tulpn | grep :8080 || echo "Nothing found with netstat, checking with ss..."
ss -tulpn | grep :8080 || echo "Port 8080 might be in use by system"

# Check other common ports
echo ""
echo "🔍 Finding available port..."

# Function to check if port is available
check_port() {
    local port=$1
    if ! ss -tulpn | grep -q ":$port "; then
        echo "✅ Port $port is available"
        return 0
    else
        echo "❌ Port $port is in use"
        return 1
    fi
}

# Try different ports
AVAILABLE_PORT=""
for port in 8081 8082 8083 8084 8085 9000 9001 9002 9003; do
    if check_port $port; then
        AVAILABLE_PORT=$port
        break
    fi
done

if [ -z "$AVAILABLE_PORT" ]; then
    echo "❌ No available ports found! Using random port..."
    AVAILABLE_PORT=$((8000 + RANDOM % 1000))
fi

echo ""
echo "🚀 Using port: $AVAILABLE_PORT"

# Stop and clean up Apache processes
echo "🛑 Stopping all Apache processes..."
pm2 stop mindscan-apache 2>/dev/null || true
pm2 delete mindscan-apache 2>/dev/null || true
pkill apache2 || true
sleep 2

# Update Apache configuration with available port
echo "🔧 Configuring Apache to use port $AVAILABLE_PORT..."

# Update ports.conf
sed -i "s/Listen [0-9]*/Listen $AVAILABLE_PORT/" /etc/apache2/ports.conf

# Update virtual host
cat > /etc/apache2/sites-available/mindscan.conf << EOF
<VirtualHost *:$AVAILABLE_PORT>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog \${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog \${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>
EOF

# Update PM2 ecosystem
cat > /var/www/html/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid'
    }
  }]
};
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
apache2ctl configtest

# Start Apache with PM2
echo "🚀 Starting Apache on port $AVAILABLE_PORT..."
cd /var/www/html
pm2 start ecosystem.config.js
pm2 save

# Wait for startup
sleep 5

# Test the port
echo "🧪 Testing Apache on port $AVAILABLE_PORT..."
if curl -s http://localhost:$AVAILABLE_PORT > /dev/null; then
    echo "✅ Apache is running successfully on port $AVAILABLE_PORT"
    TEST_RESULT="SUCCESS"
else
    echo "❌ Apache test failed on port $AVAILABLE_PORT"
    TEST_RESULT="FAILED"
fi

echo ""
echo "🎉 Port configuration completed!"
echo "=================================="
echo ""
echo "📋 Final Configuration:"
echo "- Apache running on: http://localhost:$AVAILABLE_PORT"
echo "- Status: $TEST_RESULT"
echo ""
echo "📊 PM2 Status:"
pm2 status
echo ""
echo "🔧 Coolify Configuration Needed:"
echo "In Coolify dashboard, configure your application:"
echo "1. Go to your application settings"
echo "2. Set Internal Port: $AVAILABLE_PORT"
echo "3. Set Protocol: HTTP"
echo "4. Domain: mindscan.mendingmind.org"
echo ""
echo "🧪 Test Commands:"
echo "- Local test: curl http://localhost:$AVAILABLE_PORT"
echo "- View logs: pm2 logs mindscan-apache"
echo "- Restart: pm2 restart mindscan-apache"
echo ""
echo "✅ Your application should now work with Coolify routing to port $AVAILABLE_PORT!"

# Save port info for reference
echo $AVAILABLE_PORT > /var/www/html/APACHE_PORT.txt
echo "📝 Port number saved to: /var/www/html/APACHE_PORT.txt"
