FROM php:8.2-fpm

# Install dependencies
RUN apt-get update && apt-get install -y nginx

# Copy PHP files
COPY . /var/www/html/

# Copy custom nginx config
COPY nginx.conf /etc/nginx/sites-available/default

# Set working directory
WORKDIR /var/www/html

# Set permissions
RUN chown -R www-data:www-data /var/www/html

# Start both PHP-FPM and Nginx
CMD service php8.2-fpm start && nginx -g "daemon off;"
