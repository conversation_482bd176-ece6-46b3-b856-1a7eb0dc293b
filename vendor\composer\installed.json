[{"name": "mikehaertl/php-shellcommand", "version": "1.6.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mikehaertl/php-shellcommand.git", "reference": "3488d7803df1e8f1a343d3d0ca452d527ad8d5e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-shellcommand/zipball/3488d7803df1e8f1a343d3d0ca452d527ad8d5e5", "reference": "3488d7803df1e8f1a343d3d0ca452d527ad8d5e5", "shasum": ""}, "require": {"php": ">= 5.3.0"}, "require-dev": {"phpunit/phpunit": ">4.0 <=9.4"}, "time": "2021-03-17T06:54:33+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"mikehaertl\\shellcommand\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An object oriented interface to shell commands", "keywords": ["shell"]}, {"name": "mikehaertl/php-tmpfile", "version": "1.2.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mikehaertl/php-tmpfile.git", "reference": "70a5b70b17bc0d9666388e6a551ecc93d0b40a10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/php-tmpfile/zipball/70a5b70b17bc0d9666388e6a551ecc93d0b40a10", "reference": "70a5b70b17bc0d9666388e6a551ecc93d0b40a10", "shasum": ""}, "require-dev": {"php": ">=5.3.0", "phpunit/phpunit": ">4.0 <=9.4"}, "time": "2021-03-01T18:26:25+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"mikehaertl\\tmp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A convenience class for temporary files", "keywords": ["files"]}, {"name": "mikehaertl/phpwkhtmltopdf", "version": "2.5.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/mikehaertl/phpwkhtmltopdf.git", "reference": "17ee71341591415d942774eda2c98d8ba7ea9e90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mikehaertl/phpwkhtmltopdf/zipball/17ee71341591415d942774eda2c98d8ba7ea9e90", "reference": "17ee71341591415d942774eda2c98d8ba7ea9e90", "shasum": ""}, "require": {"mikehaertl/php-shellcommand": "^1.5.0", "mikehaertl/php-tmpfile": "^1.2.1", "php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": ">4.0 <9.4"}, "time": "2021-03-01T19:41:06+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"mikehaertl\\wkhtmlto\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A slim PHP wrapper around wkhtmltopdf with an easy to use and clean OOP interface", "homepage": "http://mikehaertl.github.com/phpwkhtmltopdf/", "keywords": ["pdf", "wkhtmltoimage", "wkhtmltopdf"]}, {"name": "phpmailer/phpmailer", "version": "v6.8.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "df16b615e371d81fb79e506277faea67a1be18f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/df16b615e371d81fb79e506277faea67a1be18f1", "reference": "df16b615e371d81fb79e506277faea67a1be18f1", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.2", "doctrine/annotations": "^1.2.6 || ^1.13.3", "php-parallel-lint/php-console-highlighter": "^1.0.0", "php-parallel-lint/php-parallel-lint": "^1.3.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.7.1", "yoast/phpunit-polyfills": "^1.0.4"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "ext-openssl": "Needed for secure SMTP sending and DKIM signing", "greew/oauth2-azure-provider": "Needed for Microsoft Azure XOAUTH2 authentication", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)", "thenetworg/oauth2-azure": "Needed for Microsoft XOAUTH2 authentication"}, "time": "2023-03-06T14:43:22+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "funding": [{"url": "https://github.com/Synchro", "type": "github"}]}]