<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit58f45eddc298e47bb7de04331b10c53e
{
    public static $prefixLengthsPsr4 = array (
        'm' => 
        array (
            'mikeha<PERSON>l\\wkhtmlto\\' => 20,
            'mike<PERSON><PERSON>l\\tmp\\' => 15,
            'mike<PERSON><PERSON>l\\shellcommand\\' => 24,
        ),
        'P' => 
        array (
            'PHPMailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'mikehaertl\\wkhtmlto\\' => 
        array (
            0 => __DIR__ . '/..' . '/mikehaertl/phpwkhtmltopdf/src',
        ),
        'mikehaertl\\tmp\\' => 
        array (
            0 => __DIR__ . '/..' . '/mikehaertl/php-tmpfile/src',
        ),
        'mikehaertl\\shellcommand\\' => 
        array (
            0 => __DIR__ . '/..' . '/mikehaertl/php-shellcommand/src',
        ),
        'P<PERSON><PERSON>ailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit58f45eddc298e47bb7de04331b10c53e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit58f45eddc298e47bb7de04331b10c53e::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
