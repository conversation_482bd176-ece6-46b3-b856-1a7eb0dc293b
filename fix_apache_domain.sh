#!/bin/bash

# Fix Apache proxy and .htaccess routing issues
echo "🔧 Fixing Apache proxy and .htaccess routing for mindscan.mendingmind.org"

# Stop current setup
echo "🛑 Stopping current setup..."
pm2 stop mindscan-apache 2>/dev/null || true
pm2 delete mindscan-apache 2>/dev/null || true
pkill apache2 || true
sleep 2

# Remove any existing proxy containers
docker stop mindscan-proxy-xokcs0kk40gsw4okk4s8ss4k 2>/dev/null || true
docker rm mindscan-proxy-xokcs0kk40gsw4okk4s8ss4k 2>/dev/null || true

echo "🔧 Configuring Apache to handle domain directly..."

# Configure Apache to listen on port 80 (will be behind Coolify proxy)
cat > /etc/apache2/ports.conf << 'EOF'
# Listen on port 80 for Coolify proxy
Listen 80

# Keep port 8081 for direct access  
Listen 8081

<IfModule ssl_module>
    Listen 443
    Listen 8524
</IfModule>

<IfModule mod_gnutls.c>
    Listen 443
    Listen 8524
</IfModule>
EOF

# Create a new virtual host that handles both domain and direct access
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
# Direct access on port 8081
<VirtualHost *:8081>
    ServerName localhost
    ServerAlias *************
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_direct_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_direct_access.log combined
</VirtualHost>

# Domain access on port 80 (behind Coolify proxy)
<VirtualHost *:80>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    # Handle proxy headers from Coolify
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # Trust proxy headers
    <IfModule mod_remoteip.c>
        RemoteIPHeader X-Forwarded-For
        RemoteIPTrustedProxy **********/12
        RemoteIPTrustedProxy 10.0.0.0/8
        RemoteIPTrustedProxy ***********/16
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_domain_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_domain_access.log combined
</VirtualHost>

# HTTPS version (if needed later)
<VirtualHost *:443>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Fix .htaccess file to work properly with domain routing
echo "🔧 Fixing .htaccess routing..."
cat > /var/www/html/.htaccess << 'EOF'
RewriteEngine On

# Handle domain-specific routing
RewriteCond %{HTTP_HOST} ^(www\.)?mindscan\.mendingmind\.org$ [NC]
RewriteRule ^$ /index.php [L]

# Remove .php extension from URLs (SEO friendly)
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Add .php extension internally
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [NC,L]

# Handle common PHP routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ $1.php [NC,L,QSA]

# Force trailing slash for directories
RewriteCond %{REQUEST_FILENAME} -d
RewriteCond %{REQUEST_URI} !/$
RewriteRule ^(.*)$ $1/ [R=301,L]

# Security: Block access to sensitive files
RewriteRule ^config/ - [F,L]
RewriteRule ^\.env - [F,L]
RewriteRule ^composer\.(json|lock)$ - [F,L]

# Enable CORS for API calls (if needed)
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
</IfModule>
EOF

# Enable required Apache modules
echo "🔧 Enabling Apache modules..."
a2enmod rewrite
a2enmod headers
a2enmod remoteip
a2enmod ssl

# Enable the site and disable default
a2ensite mindscan
a2dissite 000-default

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
if apache2ctl configtest; then
    echo "✅ Apache configuration is valid"
else
    echo "❌ Apache configuration has errors"
    apache2ctl configtest
    exit 1
fi

# Update PM2 configuration to use both ports
cat > /var/www/html/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid'
    }
  }]
};
EOF

# Start Apache
echo "🚀 Starting Apache with dual port configuration..."
cd /var/www/html
pm2 start ecosystem.config.js
pm2 save

# Wait for Apache to start
sleep 5

# Test both ports
echo "🧪 Testing Apache on both ports..."

# Test port 8081 (direct access)
if curl -s http://localhost:8081 > /dev/null; then
    echo "✅ Apache responds on port 8081 (direct access)"
else
    echo "❌ Apache not responding on port 8081"
fi

# Test port 80 (domain access)
if curl -s http://localhost:80 > /dev/null; then
    echo "✅ Apache responds on port 80 (domain access)"
else
    echo "❌ Apache not responding on port 80"
fi

# Test domain with Host header
echo "🧪 Testing domain routing with Host header..."
if curl -s -H "Host: mindscan.mendingmind.org" http://localhost:80 > /dev/null; then
    echo "✅ Apache responds to domain host header"
else
    echo "❌ Apache not responding to domain host header"
fi

echo ""
echo "🎯 NEXT STEP: Configure Coolify to route to port 80"
echo "=================================================="
echo ""
echo "In Coolify Docker Compose, use this configuration:"
echo ""
cat << 'COMPOSE'
version: '3.8'
services:
  mindscan-proxy:
    image: nginx:alpine
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)"
      - "traefik.http.routers.mindscan.entrypoints=web,websecure"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
      - "traefik.http.services.mindscan.loadbalancer.server.port=80"
      - "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:80"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: |
      sh -c 'echo "
      events { worker_connections 1024; }
      http {
        server {
          listen 80;
          location / {
            proxy_pass http://host.docker.internal:80;
            proxy_set_header Host \$$host;
            proxy_set_header X-Real-IP \$$remote_addr;
            proxy_set_header X-Forwarded-For \$$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$$scheme;
          }
        }
      }" > /etc/nginx/nginx.conf && nginx -g "daemon off;"'
COMPOSE

echo ""
echo "📋 Final Status:"
echo "================"
echo "✅ Apache: Listening on ports 80 and 8081"
echo "✅ .htaccess: Fixed for domain routing"
echo "✅ Virtual hosts: Configured for both direct and domain access"
echo ""
echo "🧪 Test commands:"
echo "Direct access: curl -I http://*************:8081"
echo "Domain test:   curl -I -H 'Host: mindscan.mendingmind.org' http://localhost:80"
echo "After Coolify: curl -I http://mindscan.mendingmind.org"
echo ""
echo "📊 Monitor logs:"
echo "pm2 logs mindscan-apache"
echo "tail -f /var/log/apache2/mindscan_domain_error.log"
