# Nixpacks configuration for PHP application
[start]
cmd = "./start.sh"

[variables]
APACHE_DOCUMENT_ROOT = "/var/www/html"
PHP_INI_SCAN_DIR = "/usr/local/etc/php/conf.d"

[phases.setup]
aptPkgs = [
    "wkhtmltopdf",
    "libpng-dev", 
    "libxml2-dev",
    "libzip-dev",
    "libfreetype6-dev",
    "libjpeg-dev",
    "libicu-dev"
]

nixPkgs = ["php82", "php82Extensions.mysqli", "php82Extensions.gd", "php82Extensions.zip", "php82Extensions.fileinfo", "php82Extensions.mbstring", "php82Extensions.pdo_mysql", "php82Extensions.bcmath", "php82Extensions.exif", "php82Extensions.iconv", "php82Extensions.intl"]

[phases.install]
cmds = [
    "apt-get update",
    "apt-get install -y apache2 apache2-utils",
    "a2enmod rewrite",
    "a2enmod php8.2 || echo 'PHP module may need different name'",
    "mkdir -p /var/www/html/generated_reports",
    "mkdir -p /var/www/html/reports", 
    "chown -R www-data:www-data /var/www/html",
    "chmod -R 755 /var/www/html/generated_reports",
    "chmod -R 755 /var/www/html/reports",
    "chmod +x start.sh"
]
