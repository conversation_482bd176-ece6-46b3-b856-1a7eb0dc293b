<?php
// Simple debug script to check PHP configuration
// Remove this file in production!

echo "<h1>PHP Debug Information</h1>";

echo "<h2>PHP Version</h2>";
echo phpversion();

echo "<h2>Loaded Extensions</h2>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<ul>";
foreach ($extensions as $ext) {
    echo "<li>$ext</li>";
}
echo "</ul>";

echo "<h2>Required Extensions Check</h2>";
$required = ['mysqli', 'gd', 'zip', 'fileinfo', 'mbstring', 'pdo_mysql', 'bcmath', 'exif', 'iconv', 'intl'];
echo "<ul>";
foreach ($required as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? "✅ LOADED" : "❌ MISSING";
    echo "<li>$ext: $status</li>";
}
echo "</ul>";

echo "<h2>wkhtmltopdf Check</h2>";
$wkhtmltopdf = shell_exec('which wkhtmltopdf 2>/dev/null');
if ($wkhtmltopdf) {
    echo "✅ wkhtmltopdf found at: " . trim($wkhtmltopdf);
    $version = shell_exec('wkhtmltopdf --version 2>/dev/null');
    if ($version) {
        echo "<br>Version: " . trim($version);
    }
} else {
    echo "❌ wkhtmltopdf not found";
}

echo "<h2>Directory Permissions</h2>";
$dirs = ['generated_reports', 'reports'];
foreach ($dirs as $dir) {
    if (file_exists($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? "✅ WRITABLE" : "❌ NOT WRITABLE";
        echo "$dir: $perms $writable<br>";
    } else {
        echo "$dir: ❌ DOES NOT EXIST<br>";
    }
}

echo "<h2>Apache Modules (if available)</h2>";
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "✅ mod_rewrite is loaded<br>";
    } else {
        echo "❌ mod_rewrite is not loaded<br>";
    }
    echo "All modules: " . implode(', ', $modules);
} else {
    echo "apache_get_modules() not available (may be running under different SAPI)";
}

echo "<h2>Environment Variables</h2>";
$env_vars = ['DB_HOST', 'DB_USER', 'DB_PASS', 'DB_NAME', 'APACHE_DOCUMENT_ROOT'];
foreach ($env_vars as $var) {
    $value = getenv($var);
    if ($value) {
        echo "$var: " . (strpos($var, 'PASS') !== false ? '***hidden***' : $value) . "<br>";
    } else {
        echo "$var: ❌ NOT SET<br>";
    }
}

echo "<h2>PHP Configuration</h2>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
ul { list-style-type: none; padding: 0; }
li { padding: 5px; background: #f5f5f5; margin: 2px 0; border-radius: 3px; }
</style>
