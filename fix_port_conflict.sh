#!/bin/bash

# Fix Apache port conflict with Coolify
echo "🔧 Fixing Apache port conflict with Coolify..."

# Stop PM2 Apache process
pm2 stop mindscan-apache
pm2 delete mindscan-apache

# Kill any running Apache processes
pkill apache2 || true

# Configure Apache to run on port 8080 instead of 80
echo "🔧 Configuring Apache to run on port 8080..."

# Update Apache ports configuration
sed -i 's/Listen 80/Listen 8080/' /etc/apache2/ports.conf
sed -i 's/Listen 443/Listen 8443/' /etc/apache2/ports.conf

# Update virtual host to use port 8080
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
<VirtualHost *:8080>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>

<VirtualHost *:8443>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Update PM2 ecosystem to use port 8080
cat > /var/www/html/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid',
      APACHE_LISTEN_PORT: '8080'
    }
  }]
};
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
apache2ctl configtest

# Start Apache with PM2 on port 8080
echo "🚀 Starting Apache on port 8080..."
cd /var/www/html
pm2 start ecosystem.config.js
pm2 save

# Wait a moment for Apache to start
sleep 3

# Test if Apache is running on port 8080
echo "🧪 Testing Apache on port 8080..."
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ Apache is running on port 8080"
else
    echo "❌ Apache failed to start on port 8080"
fi

echo ""
echo "🎉 Apache port conflict fixed!"
echo ""
echo "📋 Configuration:"
echo "- Apache running on: http://localhost:8080"
echo "- Apache SSL port: 8443 (if needed)"
echo "- Let Coolify proxy handle: mindscan.mendingmind.org -> localhost:8080"
echo ""
echo "📊 PM2 Status:"
pm2 status
echo ""
echo "🔧 Coolify Configuration:"
echo "In Coolify, set your application to proxy to: http://localhost:8080"
echo "Or configure Coolify to route mindscan.mendingmind.org to port 8080"
echo ""
echo "🧪 Test locally:"
echo "curl http://localhost:8080"
echo ""
echo "✅ Your application should now work with Coolify!"
