#!/bin/bash

# Start script for the PHP application with Apache
echo "Starting PHP application with Apache..."

# Ensure Apache modules are enabled
echo "Ensuring Apache modules are enabled..."
a2enmod rewrite || echo "mod_rewrite may already be enabled"
a2enmod php8.2 || a2enmod php || echo "PHP module may already be enabled"

# Set correct permissions
echo "Setting permissions..."
chown -R www-data:www-data /var/www/html || echo "www-data user permissions already set"
chmod -R 755 /var/www/html/generated_reports || echo "generated_reports permissions already set"
chmod -R 755 /var/www/html/reports || echo "reports permissions already set"

# Create Apache configuration for clean URLs
echo "Configuring Apache..."
cat > /etc/apache2/sites-available/000-default.conf << 'EOF'
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/error.log
    CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF

# Start Apache in foreground
echo "Starting Apache..."
exec apache2-foreground
