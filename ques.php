<?php
include("navbar.php");
if (isset($_GET["qn"])) {
    $qn = $_GET["qn"];
} else {
    $qn = "1";
}
//echo $qn;
if (isset($_GET["action"]) && $_GET["action"] == "back") {
    $getSelected = fetchData("all", "answers", "user_id='" . $_SESSION['id'] . "' AND q_id='Q" . $qn . "'");
    // print_r($getSelected);
}
$question = fetchData("id,question,option1,(select option_name from option_mapping where id=a.option1) as option_1,option2,(select option_name from option_mapping where id=a.option2) as option_2,option3,(select option_name from option_mapping where id=a.option3) as option_3,option4,(select option_name from option_mapping where id=a.option4) as option_4,show_message", "questions a", "id='Q" . $qn . "'");
//print_r($question["show_message"]);
// echo "status".strpos(strtolower($question["show_message"]), "m")!=false;
if ($question["show_message"] != "no" && strpos(strtolower($question["show_message"]), "m")!==false) {
    //echo "hell";
    $getMessage = fetchData("message", "messages", "id='" . $question['show_message'] . "'");
    $msg = $getMessage["message"];
}
//print_r($question);
?>
<style>
#row_data{
      
    background: linear-gradient(to right, #fcc200, #fcc2009e);
    border-radius: 10px;
    padding:20px;
}

.white-text {
    color: #000 !important;
}
 .mx-auto,
footer.page-footer a {
    color: #000;
}
.option {
   border: 2px solid #fcc200;
  color:#000;
}
.option:hover {
    background-color:#85cbcd;
   border: 2px solid #85cbcd;
     color: #fff;
  
}
[type="radio"]:checked + label, [type="radio"]:not(:checked) + label {
    color: #000;
   font-weight: 500;
}
.nav-btn:hover {
    background: #85cbcd;
    color: #fff;
    border-color: #fcc200;
}
</style>
<section class="vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row" id="row_data">
            <!-- <div class="col-11  mt-lg-5 col-lg-10 mx-auto pl-lg-0">
            <h5 class="h5-responsive">
                ​​Check your emotional well-being with our self-report check-up. Evaluate your mental health wellbeing.
            </h5>
        </div> -->
            <div class="col-11 col-lg-10 mx-auto question">
                <h5 class="font-weight-bold h5-responsive">
                    <?php echo $question["question"]; ?>
                </h5>
            </div>
            <div class="col-11  col-lg-10 mx-auto">
                <form action="php/submitAnswer.php" method="post" name="questions" id="questions">
                    <input type="text" name="id" id="id" hidden value="<?php echo $question['id']; ?>">
                    <div class="row justify-content-lg-center">
                        <div class="d-none mx-4 d-lg-block pl-lg-0">
                            <div class="option" id="option1" data-id="option1">
                                <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT1" ? "checked" : ""; ?> value="<?php echo $question['option1']; ?>" name="option">
                                <label for="option1">
                                    <?php echo $question['option_1']; ?>
                                </label>
                            </div>
                        </div>
                        <div class="col-lg-6 d-lg-none px-0">
                            <div class="option py-4" id="option1_mob" data-id="option1">
                                <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT1" ? "checked" : ""; ?> value="<?php echo $question['option1']; ?>" name="option">
                                <label for="option1">
                                    <?php echo $question['option_1']; ?>
                                </label>
                            </div>
                        </div>
                        <div class="d-none mx-4 d-lg-block pr-0">
                            <div class="option" id="option2" data-id="option2">
                                <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT2" ? "checked" : ""; ?> value="<?php echo $question['option2']; ?>" name="option">
                                <label for="option2">
                                    <?php echo $question['option_2']; ?>
                                </label>
                            </div>
                        </div>
                        <div class="col-lg-6 d-lg-none px-0">
                            <div class="option py-4" id="option2_mob" data-id="option2">
                                <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT2" ? "checked" : ""; ?> value="<?php echo $question['option2']; ?>" name="option">
                                <label for="option2">
                                    <?php echo $question['option_2']; ?>
                                </label>
                            </div>
                        </div>
                        <div class="d-none mx-4 d-lg-block pl-0">
                            <div class="option" id="option3" data-id="option3">
                                <input type="radio" id="option3" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT3" ? "checked" : ""; ?>
                                    value="<?php echo $question['option3']; ?>" name="option">
                                <label for="option3">
                                    <?php echo $question['option_3']; ?>
                                </label>
                            </div>
                        </div>
                        <div class="col-lg-6 px-0 d-lg-none">
                            <div class="option py-4" id="option3_mob" data-id="option3">
                                <input type="radio" id="option3" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT3" ? "checked" : ""; ?>
                                    value="<?php echo $question['option3']; ?>" name="option">
                                <label for="option3">
                                    <?php echo $question['option_3']; ?>
                                </label>
                            </div>
                        </div>
                        <?php
                        if (strlen($question["option4"]) > 0) {
                            echo '<div class="d-none mx-4 d-lg-block pr-0">
                            <div class="option"  id="option4" data-id="option4">
                                <input type="radio" id="option4" ' . (isset($getSelected) && $getSelected["answer"] == "OPT1" ? "checked" : "") . ' value="' . $question["option4"] . '" name="option">
                                <label for="option4">' . $question["option_4"] . '</label>
                            </div>
                        </div><div class="col-lg-6 d-lg-none px-0">
                            <div class="option py-4" id="option4_mob" data-id="option4">
                                <input type="radio" id="option4" ' . (isset($getSelected) && $getSelected["answer"] == "OPT1" ? "checked" : "") . ' value="' . $question["option4"] . '" name="option">
                                <label for="option4">' . $question["option_4"] . '</label>
                            </div>
                        </div>';
                        }
                        ?>
                    </div>
                </form>
            </div>
            <div class="col-12 mt-4 col-lg-3 mx-auto text-center">
                <?php
                if ($qn != 1) {
                    echo '<a  href="question?action=back&qn=' . ($qn - 1) . '" class="nav-btn waves-effect waves-light"><i class="fa fa-3x fa-angle-left"></i></a>';
                }
                if ($qn != 26 && isset($_GET["action"]) && $_GET["action"] == "back") {
                    echo '
            <a href="question?qn=' . ($qn + 1) . '" class="nav-btn ml-4 waves-effect waves-light">
            <i class="fa fa-3x fa-angle-right"></i></a>';
                }
                ?>
            </div>
        </div>
    </div>
</section>
<footer class="page-footer  mt-5 center-on-small-only pt-0 mt-0" style="background-color: #fcc200;">
    <div class="container">
        <div class="row">
        <div class="col-md-6 col-12 d-flex align-items-center">
                <div class="mx-auto">
                    &copy;
                    <a href="undefined/">Mending Mind</a>
                </div>
            </div>
            <div class="col-md-6 col-12">
                <div class="mb-5 flex-center">
                    <a class="px-3" href="https://www.facebook.com/mendingmindfoundation/"><i
                            class="fa fa-facebook fa-lg white-text"></i></a>
                    <a class="px-3" href="https://www.instagram.com/mending__mind/"><i
                            class="fa fa-instagram fa-lg white-text"></i></a>
                    <a class="px-3" href="https://www.linkedin.com/company/mendingmind/"><i
                            class="fa fa-linkedin fa-lg white-text"></i></a>
                </div>
            </div>            
        </div>
</footer>
<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title font-weight-bold" id="exampleModalLabel">Hurrayyyy!</h5>
                <!-- <button type="button" class="bg-transparent border-0 btn-close" data-mdb-dismiss="modal"
                    aria-label="Close">X</button> -->
            </div>
            <div class="align-items-center text-center d-flex flex-column cong flex-lg-row modal-body">
            <!-- <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_8edlac32.json"  background="transparent"  speed="1" class="cong"  loop autoplay></lottie-player> -->
                <img src="./img/6ob.gif" alt="" class="mx-auto">
                <h5 class="font-weight-bolder h5-responsive mb-0">
                    <?php echo $msg; ?>
                </h5>
            </div>
        </div>
    </div>
</div>
<script src="scripts/main.js"></script>
<script>
    new WOW().init();
    // $("#i").click(function(){
    //     $("#exampleModal").modal('show');
    // })
</script>
<?php
if (isset($getMessage) && count($getMessage) > 0 && $question['id']!="Q26") {
    echo "
<script>$('#exampleModal').modal('show');</script>";
}
?>
</body>

</html>