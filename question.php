<?php
include("navbar.php");
if (isset($_GET["qn"])) {
    $qn = $_GET["qn"];
} else {
    $qn = "1";
}
//echo $qn;
if (isset($_GET["action"]) && $_GET["action"] == "back") {
    $getSelected = fetchData("all", "answers", "user_id='" . $_SESSION['id'] . "' AND q_id='Q" . $qn . "'");
    // print_r($getSelected);
}
$question = fetchData("id,question,option1,(select option_name from option_mapping where id=a.option1) as option_1,option2,(select option_name from option_mapping where id=a.option2) as option_2,option3,(select option_name from option_mapping where id=a.option3) as option_3,option4,(select option_name from option_mapping where id=a.option4) as option_4,show_message", "questions a", "id='Q" . $qn . "'");
//print_r($question["show_message"]);
// echo "status".strpos(strtolower($question["show_message"]), "m")!=false;
$msg = "";
if ($question["show_message"] != "no" && strpos(strtolower($question["show_message"]), "m") !== false) {
    //echo "hell";
    $getMessage = fetchData("message", "messages", "id='" . $question['show_message'] . "'");
    $msg = $getMessage["message"];
}
//print_r($question);
?>
<section class="question-page">
    <section class="align-items-center d-flex flex-column justify-content-between welcome-page">
        <div class="container">
            <div class="col-12 mt-4">
                <div class="progress-bar position-relative">
                    <span class="progress-done" style="--w:<?php echo $qn; ?>"></span>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row flex-column">
                <p class="mb-0 small font-weight-bold text-center question-number text-uppercase">question <?php echo sprintf("%02d", $qn); ?></p>
                <h5 class="text-center question font-weight-600 text-theme-dark">
                    <?php echo $question["question"]; ?>
                </h5>
            </div>
        </div>
        <div class="container">
            <div class="row">
                <?php
                if (strlen($msg) > 0) {
                    if (strpos($msg, "img") !== false) {
                        echo '<div class="col-6 mx-auto">
                            <img src="./' . $msg . '" class="img-fluid" alt="Question' . $qn . '">
                            </div>';
                    } else {
                        echo '<div class="col-11 text-center mx-auto">
                        <h5 class="font-weight-500 letter-spacing-1 text-center text-theme-dark">
                        ' . $msg . '
                        </h5>
                        </div>';
                    }
                }
                ?>
            </div>
        </div>
        </div>
        <div class="container">
            <form action="php/submitAnswer" method="POST" name="questions" id="questions" onsubmit="window.setTimeout(function () { document.getElementById('questions').submit(); }, 100); return false">
                <input type="text" name="id" id="id" hidden value="<?php echo $question['id']; ?>">
                <div class="row options">
                    <div class="col-9 mb-2  text-center px-0 mx-auto option" id="option1" data-id="option1">
                        <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT1" ? "checked" : ""; ?> value="<?php echo $question['option1']; ?>" name="option">
                        <label for="option1">
                            <?php echo $question['option_1']; ?>
                        </label>
                    </div>
                    <div class="col-9 mb-2 px-0  text-center mx-auto option" id="option2" data-id="option2">
                        <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT2" ? "checked" : ""; ?> value="<?php echo $question['option2']; ?>" name="option">
                        <label for="option2">
                            <?php echo $question['option_2']; ?>
                        </label>
                    </div>
                    <div class="col-9 mb-2 px-0  text-center mx-auto option" id="option3" data-id="option3">
                        <input type="radio" <?php echo isset($getSelected) && $getSelected["answer"] == "OPT3" ? "checked" : ""; ?> value="<?php echo $question['option3']; ?>" name="option">
                        <label for="option3">
                            <?php echo $question['option_3']; ?>
                        </label>
                    </div>
                    <?php
                    if (strlen($question["option4"]) > 0) {
                        echo '<div class="col-9 px-0  text-center mx-auto option" id="option4" data-id="option4">
                        <input type="radio"' . (isset($getSelected) && $getSelected["answer"] == "OPT4" ? "checked" : "") . ' value="' . $question['option4'] . '" name="option">
                        <label for="option4">
                            ' . $question['option_4'] . '
                        </label>
                    </div>';
                    }
                    ?>
                </div>
            </form>
        </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-9">
                    <?php
                    if ($qn != 1) {
                        echo '<a  href="question?action=back&qn=' . ($qn - 1) . '" class="px-0 btn d-flex align-items-center shadow-none"><span class="left-arrow left-back padding-less">></span></a>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </section>
</section>
<script type="text/javascript" src="scripts/jquery.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/popper.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/bootstrap.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/wow.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/mdb.min.js?ver=1.1.0"></script>
<script src="scripts/main.js"></script>
<script>
    new WOW().init();
</script>

</body>

</html>