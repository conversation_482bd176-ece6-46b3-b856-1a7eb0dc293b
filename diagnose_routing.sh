#!/bin/bash

# Diagnose Coolify routing issue and provide solutions
echo "🔍 Diagnosing Coolify routing issue for mindscan.mendingmind.org"
echo "================================================================="

# Check Apache status
echo ""
echo "📊 1. Apache Status Check"
echo "-------------------------"
pm2 status | grep mindscan-apache
if pm2 status | grep -q "mindscan-apache.*online"; then
    echo "✅ Apache is running via PM2"
else
    echo "❌ Apache is not running via PM2"
fi

# Check port 8081
echo ""
echo "🔌 2. Port 8081 Status"
echo "----------------------"
if ss -tulpn | grep -q ":8081"; then
    echo "✅ Port 8081 is listening"
    ss -tulpn | grep :8081
else
    echo "❌ Port 8081 is not listening"
fi

# Test local Apache
echo ""
echo "🧪 3. Local Apache Test"
echo "-----------------------"
if curl -s -o /dev/null -w "%{http_code}" http://localhost:8081 | grep -q "200\|30[0-9]"; then
    echo "✅ Apache responds locally on port 8081"
    echo "Response: $(curl -s -o /dev/null -w '%{http_code}' http://localhost:8081)"
else
    echo "❌ Apache not responding on port 8081"
    echo "Testing connection..."
    timeout 5 curl -v http://localhost:8081 2>&1 | head -10
fi

# Check DNS resolution
echo ""
echo "🌐 4. DNS Resolution Check"
echo "--------------------------"
DNS_IP=$(nslookup mindscan.mendingmind.org | grep -A1 "Name:" | tail -1 | awk '{print $2}' 2>/dev/null)
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || curl -s ip4.me/api/ 2>/dev/null || echo "Unable to get IP")

echo "Domain resolves to: $DNS_IP"
echo "Server IP: $SERVER_IP"

if [ "$DNS_IP" = "$SERVER_IP" ]; then
    echo "✅ DNS points to this server"
else
    echo "❌ DNS does not point to this server"
    echo "   You need to update DNS A record for mindscan.mendingmind.org"
fi

# Test domain access
echo ""
echo "🌍 5. Domain Access Test"
echo "------------------------"
echo "Testing HTTP access to domain..."
DOMAIN_RESPONSE=$(timeout 10 curl -s -o /dev/null -w "%{http_code}" http://mindscan.mendingmind.org 2>/dev/null || echo "timeout")
echo "HTTP Response: $DOMAIN_RESPONSE"

if [ "$DOMAIN_RESPONSE" = "200" ] || [[ "$DOMAIN_RESPONSE" =~ ^30[0-9]$ ]]; then
    echo "✅ Domain is accessible via HTTP"
elif [ "$DOMAIN_RESPONSE" = "timeout" ]; then
    echo "❌ Domain request timed out (DNS or routing issue)"
else
    echo "❌ Domain returned HTTP $DOMAIN_RESPONSE"
fi

# Check Coolify containers
echo ""
echo "🐳 6. Coolify Container Status"
echo "------------------------------"
if docker ps | grep -q coolify; then
    echo "✅ Coolify containers are running:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(coolify|traefik)" | head -5
else
    echo "❌ No Coolify containers found"
    echo "Available Docker containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | head -5
fi

# Check what's using port 80/443
echo ""
echo "🚪 7. Port 80/443 Usage"
echo "-----------------------"
echo "Port 80 usage:"
ss -tulpn | grep ":80 " || echo "Nothing on port 80"
echo "Port 443 usage:"
ss -tulpn | grep ":443 " || echo "Nothing on port 443"

# Provide specific solutions
echo ""
echo "🔧 8. SOLUTIONS"
echo "==============="

if [ "$DNS_IP" != "$SERVER_IP" ]; then
    echo "❗ PRIORITY 1: Fix DNS"
    echo "   Update your DNS A record for mindscan.mendingmind.org to point to $SERVER_IP"
    echo ""
fi

echo "🎯 Configure Coolify Proxy:"
echo ""
echo "Option A: Via Coolify Dashboard"
echo "1. Login to Coolify web interface"
echo "2. Go to Projects > Add New Resource"
echo "3. Choose 'Service' or 'Application'"
echo "4. Set:"
echo "   - Name: mindscan"
echo "   - Domain: mindscan.mendingmind.org"
echo "   - Port: 8081"
echo "   - Internal URL: http://localhost:8081"
echo ""

echo "Option B: Direct Traefik Labels (if Coolify uses Traefik)"
echo "Run this command to create a proxy service:"
cat << 'DOCKER_COMPOSE'

docker run -d \
  --name mindscan-proxy \
  --network coolify \
  --label "traefik.enable=true" \
  --label "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)" \
  --label "traefik.http.routers.mindscan.entrypoints=web,websecure" \
  --label "traefik.http.routers.mindscan.tls.certresolver=letsencrypt" \
  --label "traefik.http.services.mindscan.loadbalancer.server.port=8081" \
  --label "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081" \
  --add-host=host.docker.internal:host-gateway \
  nginx:alpine

DOCKER_COMPOSE

echo ""
echo "Option C: Manual Nginx Reverse Proxy"
echo "If Coolify doesn't support direct proxying, create a manual reverse proxy:"
echo ""
echo "# Install nginx"
echo "apt update && apt install nginx -y"
echo ""
echo "# Create nginx config"
echo "cat > /etc/nginx/sites-available/mindscan << 'EOF'"
cat << 'NGINX_CONFIG'
server {
    listen 9080;
    server_name mindscan.mendingmind.org;
    
    location / {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
NGINX_CONFIG
echo ""
echo "# Enable site and restart nginx"
echo "ln -s /etc/nginx/sites-available/mindscan /etc/nginx/sites-enabled/"
echo "nginx -t && systemctl restart nginx"
echo ""
echo "Then configure Coolify to proxy to port 9080 instead of 8081"

echo ""
echo "🧪 Test Commands After Configuration:"
echo "curl -I http://mindscan.mendingmind.org"
echo "curl -I https://mindscan.mendingmind.org"
echo ""
echo "💡 Need help? Share the output of this diagnostic script!"
