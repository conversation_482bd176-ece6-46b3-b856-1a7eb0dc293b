#!/bin/bash

# Quick fix for Apache ports configuration
echo "🔧 Fixing Apache ports configuration..."

# Stop PM2 Apache process
pm2 stop mindscan-apache 2>/dev/null || true
pm2 delete mindscan-apache 2>/dev/null || true

# Kill any running Apache processes
pkill apache2 || true
sleep 2

# Find an available port
echo "🔍 Finding available port..."
AVAILABLE_PORT=""
for port in 8081 8082 8083 8084 8085 9000 9001 9002 9003; do
    if ! ss -tulpn | grep -q ":$port "; then
        AVAILABLE_PORT=$port
        echo "✅ Found available port: $port"
        break
    else
        echo "❌ Port $port is in use"
    fi
done

if [ -z "$AVAILABLE_PORT" ]; then
    echo "❌ No available ports found! Using port 9500"
    AVAILABLE_PORT=9500
fi

# Create clean ports.conf
echo "🔧 Creating clean Apache ports configuration..."
cat > /etc/apache2/ports.conf << EOF
# If you just change the port or add more ports here, you will likely also
# have to change the VirtualHost statement in
# /etc/apache2/sites-enabled/000-default.conf

Listen $AVAILABLE_PORT

<IfModule ssl_module>
        Listen $((AVAILABLE_PORT + 443))
</IfModule>

<IfModule mod_gnutls.c>
        Listen $((AVAILABLE_PORT + 443))
</IfModule>
EOF

# Update virtual host configuration
echo "🔧 Updating virtual host for port $AVAILABLE_PORT..."
cat > /etc/apache2/sites-available/mindscan.conf << EOF
<VirtualHost *:$AVAILABLE_PORT>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog \${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog \${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>

<VirtualHost *:$((AVAILABLE_PORT + 443))>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org localhost
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog \${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog \${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Enable the site
a2ensite mindscan
a2dissite 000-default

# Update PM2 ecosystem configuration
echo "🔧 Updating PM2 configuration..."
cat > /var/www/html/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid',
      APACHE_LISTEN_PORT: '$AVAILABLE_PORT'
    }
  }]
};
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
if apache2ctl configtest; then
    echo "✅ Apache configuration is valid"
else
    echo "❌ Apache configuration has errors"
    exit 1
fi

# Start Apache with PM2
echo "🚀 Starting Apache on port $AVAILABLE_PORT..."
cd /var/www/html
pm2 start ecosystem.config.js
pm2 save

# Wait for Apache to start
sleep 5

# Test if Apache is running
echo "🧪 Testing Apache on port $AVAILABLE_PORT..."
if curl -s http://localhost:$AVAILABLE_PORT > /dev/null; then
    echo "✅ Apache is running on port $AVAILABLE_PORT"
else
    echo "❌ Apache failed to start on port $AVAILABLE_PORT"
    echo "Checking PM2 logs..."
    pm2 logs mindscan-apache --lines 10
fi

echo ""
echo "🎉 Apache configuration fixed!"
echo ""
echo "📋 Final Configuration:"
echo "- Apache HTTP port: $AVAILABLE_PORT"
echo "- Apache HTTPS port: $((AVAILABLE_PORT + 443))"
echo "- Document root: /var/www/html"
echo ""
echo "📊 PM2 Status:"
pm2 status
echo ""
echo "🔧 Coolify Configuration:"
echo "In Coolify, configure your application to proxy requests to:"
echo "http://localhost:$AVAILABLE_PORT"
echo ""
echo "🧪 Test locally:"
echo "curl http://localhost:$AVAILABLE_PORT"
echo ""
echo "✅ Your application should now work with Coolify!"
