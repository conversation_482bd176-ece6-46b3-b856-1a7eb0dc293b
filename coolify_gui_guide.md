# Coolify GUI Configuration Guide

## Step 1: Access Coolify Dashboard

1. **Open your web browser**
2. **Go to Coolify dashboard:**
   - URL: `http://*************:8000` 
   - Or: `https://*************:8000`
3. **Login** with your Coolify credentials

## Step 2: Add External Service (For Separately Hosted Applications)

Since your PHP application is hosted separately from Coolify, you need to create a **proxy/external service**:

### Method A: Create External/Proxy Service (Recommended)

1. **Click "Projects"** in the sidebar
2. **Select your project** (or create new one if needed)
3. **Click "+ New"** or **"Add Resource"**
4. **Choose one of these options:**
   - **"External Service"** 
   - **"Proxy Service"**
   - **"Custom Service"**
   - **"Docker Compose"** (if above options not available)

### Method B: Look for Proxy/Load Balancer Settings

1. **Go to "Settings"** or **"Proxy"** section
2. **Look for "Custom Rules"** or **"External Routing"**
3. **Add domain routing rule**

## Step 3: Configure External Service

### When creating External/Proxy Service:

1. **Service Configuration:**
   - **Service Name:** `mindscan`
   - **Service Type:** `External Service` or `Proxy`
   - **Description:** `PHP Application Proxy`

2. **Domain Configuration:**
   - **Domain/FQDN:** `mindscan.mendingmind.org`
   - **Protocol:** `HTTP` (can enable HTTPS later)

3. **Target Configuration:**
   - **Target URL:** `http://localhost:8081`
   - **Backend URL:** `http://127.0.0.1:8081`
   - **Internal Host:** `localhost`
   - **Internal Port:** `8081`

4. **Proxy Settings:**
   - **Proxy Type:** `HTTP Proxy` or `Reverse Proxy`
   - **Load Balancer:** Disabled (single target)
   - **Health Check URL:** `/` (optional)

### If using Docker Compose option:

```yaml
version: '3.8'
services:
  mindscan-proxy:
    image: nginx:alpine
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(`mindscan.mendingmind.org`)"
      - "traefik.http.routers.mindscan.entrypoints=web,websecure"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
      - "traefik.http.services.mindscan.loadbalancer.server.port=8081"
      - "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081"
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

## Step 4: Enable SSL (Optional but Recommended)

1. **In the same configuration:**
   - **Enable SSL:** Toggle ON
   - **SSL Type:** `Let's Encrypt` (automatic)
   - **Force HTTPS:** Enable after SSL works

## Step 5: Deploy/Save Configuration

1. **Click "Save"** or **"Deploy"**
2. **Wait for deployment** (usually 1-2 minutes)
3. **Check status** - should show "Running" or "Healthy"

## Alternative GUI Locations to Look For:

### If you see these sections in Coolify:

1. **"Proxy" or "Load Balancer" section:**
   - Add rule: `mindscan.mendingmind.org` → `localhost:8081`

2. **"Environment Variables" section:**
   - Add: `DOMAIN=mindscan.mendingmind.org`
   - Add: `PORT=8081`

3. **"Network" section:**
   - External Domain: `mindscan.mendingmind.org`
   - Internal Port: `8081`

4. **"Services" section:**
   - Service Type: `External Service`
   - Target: `http://localhost:8081`

## Step 6: Verify Configuration

After saving, check:

1. **Application Status:** Should be "Running"
2. **Domain Status:** Should show "Active" or similar
3. **SSL Status:** Should show "Issued" (if enabled)

## Step 7: Test Your Site

1. **Wait 2-3 minutes** for changes to take effect
2. **Visit:** `http://mindscan.mendingmind.org`
3. **Visit:** `https://mindscan.mendingmind.org` (if SSL enabled)

## Troubleshooting:

### If you can't find the exact options:

1. **Look for "Docker Compose" option:**
   - Create a simple proxy service using docker-compose

2. **Look for "Custom/External Service":**
   - This might be where you add external applications

3. **Check "Settings" → "Proxy" section:**
   - Global proxy rules might be here

### Common Coolify Section Names:
- Applications
- Services  
- Resources
- Proxy/Load Balancer
- Domains
- Environment
- Network

## Need Help?

If you can't find these options:
1. **Take a screenshot** of your Coolify dashboard
2. **Share the Coolify version** (usually in footer/header)
3. **Look for "Documentation" link** in Coolify interface

The key is to find where Coolify lets you:
- Add a domain: `mindscan.mendingmind.org`
- Point it to: `localhost:8081` or `127.0.0.1:8081`
