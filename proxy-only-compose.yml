version: '3.8'

services:
  mindscan-proxy:
    image: nginx:alpine
    container_name: mindscan-proxy-manual
    restart: always
    labels:
      # Coolify management labels
      - coolify.managed=true
      - coolify.type=proxy
      - coolify.name=mindscan-proxy-manual
      
      # Traefik routing labels
      - traefik.enable=true
      - traefik.http.middlewares.gzip.compress=true
      - traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https
      
      # HTTP router (redirects to HTTPS)
      - traefik.http.routers.http-mindscan.entryPoints=http
      - traefik.http.routers.http-mindscan.middlewares=redirect-to-https
      - traefik.http.routers.http-mindscan.rule=Host(`mindscan.mendingmind.org`)
      
      # HTTPS router (main traffic)
      - traefik.http.routers.https-mindscan.entryPoints=https
      - traefik.http.routers.https-mindscan.middlewares=gzip
      - traefik.http.routers.https-mindscan.rule=Host(`mindscan.mendingmind.org`)
      - traefik.http.routers.https-mindscan.tls.certresolver=letsencrypt
      - traefik.http.routers.https-mindscan.tls=true
      
      # Service configuration - proxy to your running container on port 8085
      - traefik.http.services.mindscan-service.loadbalancer.server.port=80
      - traefik.http.services.mindscan-service.loadbalancer.server.url=http://host.docker.internal:8085
      
      # Connect routers to service
      - traefik.http.routers.http-mindscan.service=mindscan-service
      - traefik.http.routers.https-mindscan.service=mindscan-service
    
    # Nginx configuration to proxy to your local container
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
    
    # Connect to Coolify network so Traefik can reach it
    networks:
      - coolify
    
    # Allow access to host network to reach your container on port 8085
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  coolify:
    external: true
