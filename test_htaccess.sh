#!/bin/bash

# Test and fix .htaccess routing issues
echo "🔧 Testing and fixing .htaccess routing issues"

# First, check if Apache rewrite module is enabled
echo "📋 Checking Apache modules..."
if apache2ctl -M | grep -q rewrite; then
    echo "✅ mod_rewrite is enabled"
else
    echo "❌ mod_rewrite not enabled, enabling..."
    a2enmod rewrite
    systemctl reload apache2
fi

# Check if AllowOverride is set correctly
echo "📋 Checking Apache virtual host configuration..."
if grep -q "AllowOverride All" /etc/apache2/sites-available/mindscan.conf; then
    echo "✅ AllowOverride All is set"
else
    echo "❌ AllowOverride not set properly"
fi

# Test different URL patterns
echo ""
echo "🧪 Testing different URL patterns..."

# Test direct .php file access
echo "Testing direct PHP file access:"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/index.php | head -1

# Test clean URL (without .php)
echo "Testing clean URL (index):"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/index | head -1

# Test login page
echo "Testing login page:"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/login | head -1

# Test with domain header
echo "Testing with domain header:"
curl -s -w "Status: %{http_code}\n" -H "Host: mindscan.mendingmind.org" http://localhost:8081/login | head -1

# Create a better .htaccess file
echo ""
echo "🔧 Creating improved .htaccess file..."
cat > /var/www/html/.htaccess << 'EOF'
RewriteEngine On

# Debug rewrite rules (remove in production)
# LogLevel alert rewrite:trace3

# Remove .php extension from URLs - external redirect
RewriteCond %{THE_REQUEST} \s/+([^.]+)\.php[\s?] [NC]
RewriteRule ^ /%1 [R=301,L]

# Internally add .php extension for existing files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{DOCUMENT_ROOT}/$1.php -f
RewriteRule ^([^/]+)/?$ $1.php [L]

# Handle subdirectories with clean URLs
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{DOCUMENT_ROOT}/$1/$2.php -f
RewriteRule ^([^/]+)/([^/]+)/?$ $1/$2.php [L]

# Default fallback to index.php for root
RewriteCond %{REQUEST_URI} ^/?$
RewriteRule ^$ index.php [L]

# Security: block access to sensitive files
RewriteRule ^(config|vendor|composer\.(json|lock))(/.*)?$ - [F,L]

# Set proper MIME types and headers
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
    <IfModule mod_expires.c>
        ExpiresActive On
        ExpiresDefault "access plus 1 month"
    </IfModule>
</FilesMatch>

<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
EOF

# Restart Apache to apply changes
echo "🔄 Restarting Apache to apply changes..."
pm2 restart mindscan-apache
sleep 3

# Test again after fix
echo ""
echo "🧪 Testing after .htaccess fix..."

echo "1. Root URL:"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/ | head -1

echo "2. Index without extension:"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/index | head -1

echo "3. Login without extension:"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/login | head -1

echo "4. Direct PHP file (should redirect):"
curl -s -w "Status: %{http_code}\n" http://localhost:8081/login.php | head -1

echo "5. With domain header:"
curl -s -w "Status: %{http_code}\n" -H "Host: mindscan.mendingmind.org" http://localhost:8081/index | head -1

# Check Apache error logs for rewrite issues
echo ""
echo "📊 Recent Apache errors (if any):"
tail -10 /var/log/apache2/mindscan_error.log 2>/dev/null | grep -i rewrite || echo "No rewrite errors found"

echo ""
echo "🎯 URL Testing Results:"
echo "======================"
echo "✅ If status is 200: URL works correctly"
echo "✅ If status is 301: Redirect working (good for .php removal)"
echo "❌ If status is 404: File not found (routing issue)"
echo "❌ If status is 500: Server error (check logs)"

echo ""
echo "🔧 If issues persist, check:"
echo "1. File permissions: ls -la /var/www/html/"
echo "2. Apache error log: tail -f /var/log/apache2/mindscan_error.log"
echo "3. Enable rewrite debugging by uncommenting LogLevel line in .htaccess"
