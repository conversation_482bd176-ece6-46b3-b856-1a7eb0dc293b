#!/bin/bash

# Simple fix for .htaccess routing
echo "🔧 Simple fix for .htaccess routing issues"

# Stop Apache first
echo "🛑 Stopping Apache..."
pm2 stop mindscan-apache

# Check what PHP files exist
echo "📁 Available PHP files in /var/www/html:"
ls -la /var/www/html/*.php

# Create a very simple .htaccess for testing
echo "🔧 Creating simple .htaccess for testing..."
cat > /var/www/html/.htaccess << 'EOF'
RewriteEngine On

# Simple rule: add .php to requests that don't have an extension
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}\.php -f
RewriteRule ^(.*)$ $1.php [L]

# Default to index.php for root
DirectoryIndex index.php

# Basic security
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
apache2ctl configtest

# Start Apache
echo "🚀 Starting Apache..."
pm2 start mindscan-apache
sleep 3

# Test basic functionality
echo ""
echo "🧪 Testing basic functionality..."

echo "1. Testing root (/):"
RESPONSE1=$(curl -s -w "%{http_code}" http://localhost:8081/ -o /dev/null)
echo "Status: $RESPONSE1"

echo "2. Testing index.php directly:"
RESPONSE2=$(curl -s -w "%{http_code}" http://localhost:8081/index.php -o /dev/null)
echo "Status: $RESPONSE2"

echo "3. Testing index (clean URL):"
RESPONSE3=$(curl -s -w "%{http_code}" http://localhost:8081/index -o /dev/null)
echo "Status: $RESPONSE3"

echo "4. Testing login (clean URL):"
RESPONSE4=$(curl -s -w "%{http_code}" http://localhost:8081/login -o /dev/null)
echo "Status: $RESPONSE4"

# If basic test fails, try without .htaccess
if [ "$RESPONSE2" != "200" ]; then
    echo ""
    echo "❌ Basic PHP access failing. Testing without .htaccess..."
    
    # Backup .htaccess and test without it
    mv /var/www/html/.htaccess /var/www/html/.htaccess.backup
    
    echo "5. Testing index.php without .htaccess:"
    RESPONSE5=$(curl -s -w "%{http_code}" http://localhost:8081/index.php -o /dev/null)
    echo "Status: $RESPONSE5"
    
    if [ "$RESPONSE5" = "200" ]; then
        echo "✅ PHP works without .htaccess - the issue is in rewrite rules"
    else
        echo "❌ PHP not working even without .htaccess - check Apache/PHP setup"
        echo "Checking PHP status..."
        php -v
        echo "Checking if PHP module is loaded in Apache..."
        apache2ctl -M | grep php
    fi
    
    # Restore .htaccess
    mv /var/www/html/.htaccess.backup /var/www/html/.htaccess
fi

# Check file permissions
echo ""
echo "📁 Checking file permissions:"
ls -la /var/www/html/ | head -10

# Check Apache error logs
echo ""
echo "📊 Recent Apache errors:"
tail -5 /var/log/apache2/mindscan_error.log 2>/dev/null || echo "No error log found"

echo ""
echo "🔧 Manual testing steps:"
echo "========================"
echo "1. Test direct PHP: curl -v http://localhost:8081/index.php"
echo "2. Test clean URL:  curl -v http://localhost:8081/index"
echo "3. Check in browser: http://*************:8081/"
echo "4. Check error log: tail -f /var/log/apache2/mindscan_error.log"

# Create a test PHP file to verify PHP processing
echo ""
echo "🧪 Creating test PHP file..."
cat > /var/www/html/test.php << 'EOF'
<?php
echo "PHP is working!\n";
echo "Server: " . $_SERVER['SERVER_NAME'] . "\n";
echo "Request URI: " . $_SERVER['REQUEST_URI'] . "\n";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
phpinfo();
?>
EOF

echo "6. Test PHP processing: curl http://localhost:8081/test.php"
echo "7. Test via clean URL:  curl http://localhost:8081/test"
