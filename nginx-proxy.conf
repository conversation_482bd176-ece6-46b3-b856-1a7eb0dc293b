events {
    worker_connections 1024;
}

http {
    upstream apache_backend {
        server host.docker.internal:8081;
    }

    server {
        listen 80;
        server_name mindscan.mendingmind.org;

        location / {
            proxy_pass http://apache_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Increase timeout for PHP applications
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # <PERSON><PERSON> redirects properly
            proxy_redirect off;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
