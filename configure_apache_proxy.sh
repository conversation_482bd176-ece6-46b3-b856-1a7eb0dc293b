#!/bin/bash

# Configure Apache to handle domain directly (no separate proxy needed)
echo "🔧 Configuring Apache to handle mindscan.mendingmind.org directly"

# Stop any proxy containers we created
echo "🛑 Cleaning up proxy containers..."
docker stop mindscan-proxy-xokcs0kk40gsw4okk4s8ss4k 2>/dev/null || true
docker rm mindscan-proxy-xokcs0kk40gsw4okk4s8ss4k 2>/dev/null || true

# Stop PM2 Apache to reconfigure
echo "🛑 Stopping Apache for reconfiguration..."
pm2 stop mindscan-apache
sleep 2

# Configure Apache to listen on port 80 (through Coolify proxy)
echo "🔧 Configuring Apache for direct domain handling..."

# Update Apache ports - we'll use port 8081 but configure virtual hosts properly
cat > /etc/apache2/ports.conf << 'EOF'
# Listen on port 8081 (behind Coolify proxy)
Listen 8081

<IfModule ssl_module>
    Listen 8524
</IfModule>

<IfModule mod_gnutls.c>
    Listen 8524
</IfModule>
EOF

# Create proper virtual host configuration
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
<VirtualHost *:8081>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    ServerAlias localhost
    ServerAlias *************
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    # Enable mod_rewrite
    <IfModule mod_rewrite.c>
        RewriteEngine On
        
        # Handle requests from proxy
        RewriteCond %{HTTP_HOST} ^(www\.)?mindscan\.mendingmind\.org$ [NC]
        RewriteRule ^(.*)$ /var/www/html/$1 [L]
    </IfModule>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set X-Forwarded-Proto "https"
    </IfModule>
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_access.log combined
    LogLevel info
</VirtualHost>

# SSL Virtual Host (for HTTPS via proxy)
<VirtualHost *:8524>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set X-Forwarded-Proto "https"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Enable required Apache modules
echo "🔧 Enabling Apache modules..."
a2enmod rewrite
a2enmod headers
a2enmod ssl

# Enable the site and disable default
echo "🔧 Enabling mindscan site..."
a2ensite mindscan
a2dissite 000-default

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
if apache2ctl configtest; then
    echo "✅ Apache configuration is valid"
else
    echo "❌ Apache configuration has errors"
    exit 1
fi

# Restart Apache via PM2
echo "🚀 Starting Apache..."
pm2 start mindscan-apache
pm2 save
sleep 3

# Test Apache locally
echo "🧪 Testing Apache locally..."
if curl -s http://localhost:8081 > /dev/null; then
    echo "✅ Apache responds on port 8081"
else
    echo "❌ Apache not responding"
    pm2 logs mindscan-apache --lines 10
    exit 1
fi

# Test with domain header
echo "🧪 Testing with domain header..."
if curl -s -H "Host: mindscan.mendingmind.org" http://localhost:8081 > /dev/null; then
    echo "✅ Apache responds to domain requests"
else
    echo "❌ Apache not responding to domain requests"
fi

# Now we need to configure Coolify to proxy to our Apache
echo ""
echo "🎯 NEXT STEP: Configure Coolify Proxy"
echo "====================================="
echo ""
echo "Now you need to configure Coolify to proxy mindscan.mendingmind.org to port 8081"
echo ""
echo "Option 1: Coolify Dashboard"
echo "1. Go to: http://*************:8000"
echo "2. Create new 'External Service' or 'Application'"
echo "3. Set domain: mindscan.mendingmind.org"
echo "4. Set target: http://localhost:8081"
echo ""
echo "Option 2: Simple Docker command (if Option 1 doesn't work)"
echo "Run this command:"
echo ""
cat << 'DOCKER_CMD'
docker run -d \
  --name mindscan-proxy \
  --network coolify \
  --label "traefik.enable=true" \
  --label "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)" \
  --label "traefik.http.routers.mindscan.entrypoints=web,websecure" \
  --label "traefik.http.routers.mindscan.tls.certresolver=letsencrypt" \
  --label "traefik.http.services.mindscan.loadbalancer.server.port=8081" \
  --label "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081" \
  --add-host=host.docker.internal:host-gateway \
  alpine:latest sleep 3600
DOCKER_CMD

echo ""
echo "📊 Current Status:"
echo "=================="
echo "✅ Apache configured for domain handling"
echo "✅ Apache running on port 8081"
echo "✅ Virtual hosts configured for mindscan.mendingmind.org"
echo "⏳ Waiting for Coolify proxy configuration"
echo ""
echo "🧪 Test commands:"
echo "curl -H 'Host: mindscan.mendingmind.org' http://localhost:8081"
