#!/bin/bash

# Streamlined Deployment Script for DevMindScan with External Database
# Uses PM2 for process management and Apache for web server

set -e  # Exit on any error

echo "🚀 Starting Deployment of DevMindScan Application..."
echo "Domain: mindscan.mendingmind.org"
echo "=================================================="

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (use sudo)"
    exit 1
fi

# Update system
echo "📦 Updating system packages..."
apt-get update -y

# Install Node.js and npm (for PM2)
echo "📦 Installing Node.js and PM2..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs
npm install -g pm2

# Add PHP repository
echo "📦 Adding PHP repository..."
apt-get install -y software-properties-common
add-apt-repository ppa:ondrej/php -y
apt-get update

# Install required packages
echo "📦 Installing required packages..."
apt-get install -y \
    apache2 \
    apache2-utils \
    php8.2 \
    php8.2-cli \
    php8.2-fpm \
    php8.2-mysql \
    php8.2-mysqli \
    php8.2-gd \
    php8.2-zip \
    php8.2-mbstring \
    php8.2-xml \
    php8.2-bcmath \
    php8.2-intl \
    php8.2-curl \
    libapache2-mod-php8.2 \
    mysql-client \
    wkhtmltopdf \
    composer \
    curl \
    wget \
    unzip

# Enable Apache modules
echo "🔧 Configuring Apache modules..."
a2enmod rewrite
a2enmod php8.2
a2enmod ssl
a2enmod headers
a2enmod proxy
a2enmod proxy_http

# Create application directory
echo "📁 Setting up application directory..."
APP_DIR="/var/www/html"
mkdir -p $APP_DIR
mkdir -p $APP_DIR/generated_reports
mkdir -p $APP_DIR/reports

# Copy application files (assuming script is run from app directory)
echo "📄 Copying application files..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cp -r $SCRIPT_DIR/* $APP_DIR/
# Don't copy deployment scripts to web directory
rm -f $APP_DIR/deploy.sh
rm -f $APP_DIR/nixpacks.toml
rm -f $APP_DIR/setup_database.sh

# Set permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 777 $APP_DIR/generated_reports
chmod -R 777 $APP_DIR/reports

# Configure Apache Virtual Host for domain
echo "🌐 Configuring Apache Virtual Host for mindscan.mendingmind.org..."
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
<VirtualHost *:80>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    # Enable clean URLs
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    # Enable clean URLs
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    # Security headers
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    # SSL Configuration (will be added by certbot)
    # SSLEngine on
    # SSLCertificateFile /etc/letsencrypt/live/mindscan.mendingmind.org/fullchain.pem
    # SSLCertificateKeyFile /etc/letsencrypt/live/mindscan.mendingmind.org/privkey.pem
    
    # Logging
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Enable the site
echo "✅ Enabling Apache site..."
a2ensite mindscan.conf
a2dissite 000-default.conf

# Install Composer dependencies
echo "📦 Installing Composer dependencies..."
cd $APP_DIR
if [ -f composer.json ]; then
    composer install --no-dev --optimize-autoloader
fi

# Configure PHP
echo "🐘 Configuring PHP..."
cat > /etc/php/8.2/apache2/conf.d/99-mindscan.ini << 'EOF'
; DevMindScan PHP Configuration
memory_limit = 256M
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
max_input_vars = 3000
date.timezone = UTC

; Enable required extensions
extension=mysqli
extension=gd
extension=zip
extension=mbstring
extension=xml
extension=bcmath
extension=intl
extension=curl
EOF

# Create PM2 ecosystem file
echo "⚙️ Creating PM2 configuration..."
cat > $APP_DIR/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid'
    }
  }]
};
EOF

# Test Apache configuration
echo "🧪 Testing Apache configuration..."
apache2ctl configtest

# Stop Apache if running (PM2 will manage it)
echo "🔄 Preparing services..."
systemctl stop apache2
systemctl disable apache2

# Start Apache with PM2
echo "🚀 Starting Apache with PM2..."
cd $APP_DIR
pm2 start ecosystem.config.js
pm2 save
pm2 startup

# Install SSL certificate
echo "� Setting up SSL certificate..."
apt-get install -y certbot python3-certbot-apache
echo "Run this command to get SSL certificate:"
echo "certbot --apache -d mindscan.mendingmind.org"

# Test wkhtmltopdf
echo "🧪 Testing wkhtmltopdf..."
wkhtmltopdf --version

# Test PHP
echo "🧪 Testing PHP..."
php -v
php -m | grep -E "(mysqli|gd|zip|mbstring|xml|bcmath|intl)"

# Create environment configuration template
echo "⚙️ Creating environment configuration template..."
cat > $APP_DIR/config/env_template.php << 'EOF'
<?php
// Environment Configuration Template
// Copy this to config.php and update with your external database values

// External Database Configuration
define('DB_HOST', 'your-external-db-host.com');  // Your external MySQL host
define('DB_USER', 'your_db_username');
define('DB_PASS', 'your_db_password');
define('DB_NAME', 'your_db_name');
define('DB_PORT', 3306);  // Default MySQL port

// Application Configuration
define('APP_URL', 'https://mindscan.mendingmind.org');
define('APP_ENV', 'production');

// Email Configuration (if needed)
define('SMTP_HOST', 'your-smtp-server.com');
define('SMTP_USER', '<EMAIL>');
define('SMTP_PASS', 'your-email-password');
define('SMTP_PORT', 587);

// PDF Configuration
define('WKHTMLTOPDF_PATH', '/usr/bin/wkhtmltopdf');

// Security
define('APP_KEY', 'generate-a-random-32-character-string');
?>
EOF

echo ""
echo "🎉 Deployment completed successfully!"
echo "=================================================="
echo ""
echo "📋 Next Steps:"
echo "1. Configure your application:"
echo "   - Copy: cp $APP_DIR/config/env_template.php $APP_DIR/config/config.php"
echo "   - Edit: nano $APP_DIR/config/config.php"
echo "   - Update with your EXTERNAL database credentials"
echo ""
echo "2. Point your domain to this server:"
echo "   - Add A record: mindscan.mendingmind.org -> $(curl -s ifconfig.me)"
echo ""
echo "3. Get SSL certificate:"
echo "   - Run: certbot --apache -d mindscan.mendingmind.org"
echo ""
echo "4. Test the application:"
echo "   - Visit: https://mindscan.mendingmind.org"
echo "   - Debug page: https://mindscan.mendingmind.org/debug.php"
echo ""
echo "📊 PM2 Status:"
pm2 status
echo ""
echo "🔧 PM2 Commands:"
echo "   - View logs: pm2 logs"
echo "   - Restart: pm2 restart mindscan-apache"
echo "   - Stop: pm2 stop mindscan-apache"
echo "   - Status: pm2 status"
echo ""
echo "📁 Application location: $APP_DIR"
echo "📝 Apache config: /etc/apache2/sites-available/mindscan.conf"
echo "📋 Logs: pm2 logs mindscan-apache"
echo ""
echo "🌐 Your server IP: $(curl -s ifconfig.me)"
echo "✅ Your application should be accessible once DNS is configured!"
