version: '3.8'

services:
  mindscan-proxy:
    image: nginx:alpine
    container_name: mindscan-proxy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.mindscan.rule=Host(`mindscan.mendingmind.org`)"
      - "traefik.http.routers.mindscan.entrypoints=web,websecure"
      - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt" 
      - "traefik.http.services.mindscan.loadbalancer.server.port=80"
      - "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - coolify
    depends_on: []

networks:
  coolify:
    external: true
