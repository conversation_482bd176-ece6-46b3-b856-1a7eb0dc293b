html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

#intro {
  background: url("../img/landing-image.jpg") no-repeat center center;
  background-size: cover;
}

.top-nav-collapse {
  background-color: #fff !important;
}

.navbar:not(.top-nav-collapse) {
  background: #fff !important;
}

@media (max-width: 768px) {
  .navbar:not(.top-nav-collapse) {
    background: #fff !important;
  }
  .border-right{border-right: 0px !important;}
}

#intro .h6 {
  font-weight: 300;
  line-height: 1.7;
}

.site-bg-img {
  height: 100vh;
  min-height: 480px;
  max-height: 1080px;
}

.hm-gradient .site-bg-img {
  background: rgba(42, 27, 161, 0.7);
  background: linear-gradient(45deg, rgb(0 0 0 / 95%), rgb(0 4 3 / 70%) 100%);
}

@media (max-width: 450px) {
  .margins {
    margin-right: 1rem;
    margin-left: 1rem;
  }

  .property-image {
    height: auto !important;
  }

  .catalogue {
    height: auto !important;
  }
}

#pricing .lead {
  opacity: 0.7;
}

#pricing .card-image {
  background: url("../img/architecture.jpg") no-repeat center center;
}

#pricing ul li {
  font-size: 1.1em;
}

.navbar-brand img {
  width: 150px;
  height: auto;
}

li.nav-item::after {
  content: "";
  display: block;
  position: absolute;
  width: 0%;
  height: 2px;
  background: black;
  transition: all 0.5s ease;
  left: 50%;
  transform: translate(-50%, 0);
}

li.nav-item:hover:after {
  width: 100%;
}

h2 span::after {
  content: "";
  width: 30%;
  height: 2px;
  background: inherit;
  position: absolute;
  bottom: -10px;
  left: 35%;
}

.underline-sm::after {
  width: 25%;
  left: 37%;
}

.btn-outline-black::after,.sliding-bg::after {
  content: "";
  width: 0%;
  height: 100%;
  display: block;
  background: black;
  left: 0px;
  top: 0px;
  position: absolute;
  transition: all 0.5s ease;
  z-index: 0;
}

a.btn-outline-black:hover,.sliding-bg::after {
  color: #fff !important;
}

.btn-outline-black:hover::after,.sliding-bg::after {
  width: 100%;
}

.property-image img {
  height: 100% !important;
  object-fit: cover;
}

.property-image {
  height: 100%;
}

.catalogue {
  transition: 0.3s;
  background: #f2f2f2;
  cursor: pointer;
  height: 100%;
}

.catalogue:hover {
  box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .30) !important;
}

blockquote {
  border: none;
  font-family: Georgia, "Times New Roman", Times, serif;
  margin-bottom: -30px;
  quotes: "\201C" "\201D" "\2018" "\2019";
}

blockquote h4:before {
  content: open-quote;
  font-weight: bold;
  font-size: 4rem;
  color: white;
}

blockquote h4:after {
  content: close-quote;
  font-weight: bold;
  font-size: 4rem;
  color: white;

}

.testimonial-item img {
  width: 100%;
}

.btn-close:focus, .btn-close:visited {
  border: none;
  outline: none;
}

ul.features-list li:after {
  content: "";
  width: 9px;
  height: 1px;
  background: black;
  position: absolute;
  bottom: 12px;
  left: -18px;
}

.no-hover::after{
  content: none !important;
}


*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  background: #ffffff;
}

.timeline {
  position: relative;
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;
  padding: 15px 0;
}

.timeline::after {
  content: '';
  position: absolute;
  width: 2px;
  background: #212121;
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -1px;
}

.timeline .container {
  padding: 15px 30px;
  position: relative;
  background: inherit;
  width: 50%;
}

.timeline .container.left {
  left: -25%;
}

.timeline .container.right {
  left: 25%;
}

.timeline .container::after {
  content: '';
  position: absolute;
  width: 90px;
  height: 90px;
  top: calc(50% - 48px);
  background: #ffffff;
  border: 2px solid #212121;
  border-radius: 50%;
  z-index: 1;
  right: -45px;
}

.timeline .container.right::after {
  left: -45px;
}

.timeline .container::before {
  content: '';
  position: absolute;
  width: 100px;
  height: 2px;
  top: calc(50% - 1px);
  right: 8px;
  background: #212121;
  z-index: 0;
}

.timeline .container.right::before {
  left: 8px;
}

.timeline .container .date {
  position: absolute;
  display: inline-block;
  top: calc(50% - 120px);
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  color: #006E51;
  text-transform: uppercase;
  letter-spacing: 1px;
  z-index: 1;
}

.timeline .container.left .date {
  right: -75px;
}

.timeline .container.right .date {
  left: -350px;
}

.timeline .container .icon {
  position: absolute;
  display: inline-block;
  width: 40px;
  height: 40px;
  padding: 9px 0;
  top: calc(50% - 20px);
  background: #F6D155;
  border: 2px solid #006E51;
  border-radius: 40px;
  text-align: center;
  font-size: 18px;
  color: #006E51;
  z-index: 1;
}

.timeline .container.left .icon {
  right: 56px;
}

.timeline .container.right .icon {
  left: 56px;
}

.timeline .container .content {
  padding: 30px 90px 30px 30px;
  background: #52ab98;
  position: relative;
  border-radius: 0 500px 500px 0;
  margin-right: 50px;
  color: #fff;
}

.timeline .container.right .content {
  padding: 30px 30px 30px 90px;
  border-radius: 500px 0 0 500px;
  margin-right: unset;
  margin-left: 50px;
  background-color: #006878;
}

.timeline .container .content h2 {
  margin: 0 0 10px 0;
  font-size: 18px;
  font-weight: normal;
  color: #fff;
}

.timeline .container .content p {
  margin: 0;
  font-size: 16px;
  line-height: 22px;
  color: #fff;
}

.timeline .number {
  width: 90px;
  height: 90px;
  position: absolute;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 50%;
  font-size: 44px;
  padding: 14px;
  top: calc(50% - 48px);
  left: -45px;
  z-index: 2;
  background-color: #2b6777;
  color: #fff;
}

.left .number {
  left: unset;
  right: -45px;
  background-color: #52ab98;
}

@media (max-width: 767.98px) {
  .timeline::after {
    left: 90px;
  }

  .timeline .container {
    width: 100%;
    padding-left: 120px;
    padding-right: 30px;
  }

  .timeline .container.right {
    left: 0%;
  }

  .timeline .container.left::after, 
  .timeline .container.right::after {
    left: 82px;
  }

  .timeline .container.left::before,
  .timeline .container.right::before {
    left: 100px;
    border-color: transparent #006E51 transparent transparent;
  }

  .timeline .container.left .date,
  .timeline .container.right .date {
    right: auto;
    left: 15px;
  }

  .timeline .container.left .icon,
  .timeline .container.right .icon {
    right: auto;
    left: 146px;
  }

  .timeline .container.left .content,
  .timeline .container.right .content {
    padding: 30px 30px 30px 90px;
    border-radius: 500px 0 0 500px;
  }
}