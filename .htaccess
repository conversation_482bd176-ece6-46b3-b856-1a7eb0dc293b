RewriteEngine On

# Remove .php extension from URLs (external redirect)
RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
RewriteRule ^ /%1 [NC,L,R=301]

# Add .php extension internally when file exists
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}.php -f
RewriteRule ^(.*)$ $1.php [NC,L]

# Handle root directory - serve index.php
RewriteCond %{REQUEST_URI} ^/$
RewriteRule ^$ index.php [NC,L]