ServerRoot "/usr"
Listen 80

LoadModule authz_core_module modules/mod_authz_core.so
LoadModule dir_module modules/mod_dir.so
LoadModule mime_module modules/mod_mime.so
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule php_module modules/libphp.so

<IfModule dir_module>
    DirectoryIndex index.php index.html
</IfModule>

<FilesMatch \.php$>
    SetHandler application/x-httpd-php
</FilesMatch>

DocumentRoot "/var/www/html"

<Directory "/var/www/html">
    AllowOverride All
    Require all granted
    
    # Enable clean URLs
    RewriteEngine On
    RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
    RewriteRule ^ /%1 [NC,L,R]
    
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteRule ^ %{REQUEST_URI}.php [NC,L]
</Directory>

# MIME types
TypesConfig /etc/mime.types

# Error and access logs
ErrorLog /dev/stderr
CustomLog /dev/stdout combined
