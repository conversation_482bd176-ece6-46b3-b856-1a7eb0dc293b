<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css"
    integrity="sha384-9aIt2nRpC12Uk9gS9baDl411NQApFmC26EwAOH8WgZl5MYYxFfc+NcPb1dKGj7Sk" crossorigin="anonymous">
<?php
include("common.php");
// print_r($_POST);
$fields = ["user_name", "phone", "gender", "language","marital-status","age","emailid","location","profession","dateOfAssessment"];
global $webUrl;
if (checkRequired($fields)) {
    $_POST = clearSqlInjection($_POST);
    if (checkUserPhone($_POST["phone"]) == "true") {
        $checkstatus = fetchData("id,status,language", "users", "phone='" . $_POST['phone'] . "'", "time_of_registration desc");
        //print_r($checkstatus);
        setSession($checkstatus["id"]);
        if (!isset($_SESSION['start'])) {
            $_SESSION['start'] = time();
        }
        setcookie("lang", $checkstatus["language"], time() + (86400 * 30), "/"); // 86400 = 1 day
        if ($checkstatus["status"] == '1' && strpos($_SERVER['REQUEST_URI'], "reports")==false) {
            echo "<script>window.location.href='" . $webUrl . "reports'</script>";
        } elseif ($checkstatus["status"] == '0' && strpos($_SERVER['REQUEST_URI'], "remember")== false) {
            echo "<script>window.location.href='" . $webUrl . "question?qn=1'</script>";
        }
    } else {
        setcookie("lang", $_POST["language"], time() + (86400 * 30), "/"); // 86400 = 1 day
        $id = "MS" . getRandomString(8);
        if (executeQuery("users", "id,username,email,phone,gender,language,age,location,marital_status,profession,test_date", "'" . $id . "','" . $_POST['user_name'] . "','" . $_POST['emailid'] . "','" . $_POST['phone'] . "','" . $_POST['gender'] . "','" . $_POST['language'] . "','" . $_POST['age'] . "','" . $_POST['location'] . "','" . $_POST['marital-status'] . "','" . $_POST['profession'] . "','" . $_POST['dateOfAssessment'] . "'")) {
            setSession($id);
            echo "<script>window.location.href='" . $webUrl . "question?qn=1';</script>";
        } else {
            $message = 'Sorry! Signup failed. Please try again. <br>' . $register;
            $action = '<a href="../index" class="btn btn-danger">Try again</a>';
        }
    }
} else {
    $message = 'Please fill all the required details!';
    $action = '<button class="btn btn-danger" type="button" onclick="window.history.back();">Back</button>';
}
echo '<div class="container"><div class="alert alert-danger mt-3" style="background:transparent;"><b>' . $message . '</b><br><hr>' . $action . '</div>';
?>