#!/bin/bash

# Quick fix to continue deployment from where it stopped
echo "🔧 Fixing PHP installation..."

# Add PHP repository
echo "📦 Adding PHP repository..."
apt-get install -y software-properties-common
add-apt-repository ppa:ondrej/php -y
apt-get update

# Install PHP and required packages
echo "📦 Installing PHP 8.2 and extensions..."
apt-get install -y \
    php8.2 \
    php8.2-cli \
    php8.2-fpm \
    php8.2-mysql \
    php8.2-mysqli \
    php8.2-gd \
    php8.2-zip \
    php8.2-mbstring \
    php8.2-xml \
    php8.2-bcmath \
    php8.2-intl \
    php8.2-curl \
    libapache2-mod-php8.2 \
    mysql-client \
    wkhtmltopdf \
    composer

# Enable Apache modules
echo "🔧 Configuring Apache modules..."
a2enmod rewrite
a2enmod php8.2
a2enmod ssl
a2enmod headers
a2enmod proxy
a2enmod proxy_http

# Set up application directory
APP_DIR="/var/www/html"
mkdir -p $APP_DIR/generated_reports
mkdir -p $APP_DIR/reports

# Copy application files
echo "📄 Copying application files..."
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cp -r $SCRIPT_DIR/* $APP_DIR/
rm -f $APP_DIR/deploy.sh
rm -f $APP_DIR/nixpacks.toml
rm -f $APP_DIR/fix_deployment.sh

# Set permissions
echo "🔐 Setting file permissions..."
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR
chmod -R 777 $APP_DIR/generated_reports
chmod -R 777 $APP_DIR/reports

# Configure Apache Virtual Host
echo "🌐 Configuring Apache Virtual Host..."
cat > /etc/apache2/sites-available/mindscan.conf << 'EOF'
<VirtualHost *:80>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName mindscan.mendingmind.org
    ServerAlias www.mindscan.mendingmind.org
    DocumentRoot /var/www/html
    
    <Directory /var/www/html>
        Options Indexes FollowSymLinks MultiViews
        AllowOverride All
        Require all granted
        DirectoryIndex index.php index.html
    </Directory>
    
    <IfModule mod_rewrite.c>
        RewriteEngine On
    </IfModule>
    
    <IfModule mod_headers.c>
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
    </IfModule>
    
    ErrorLog ${APACHE_LOG_DIR}/mindscan_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mindscan_ssl_access.log combined
</VirtualHost>
EOF

# Enable site
a2ensite mindscan.conf
a2dissite 000-default.conf

# Install Composer dependencies
echo "📦 Installing Composer dependencies..."
cd $APP_DIR
if [ -f composer.json ]; then
    composer install --no-dev --optimize-autoloader
fi

# Configure PHP
cat > /etc/php/8.2/apache2/conf.d/99-mindscan.ini << 'EOF'
memory_limit = 256M
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
max_input_vars = 3000
date.timezone = UTC
EOF

# Create PM2 ecosystem
cat > $APP_DIR/ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'mindscan-apache',
    script: '/usr/sbin/apache2ctl',
    args: '-D FOREGROUND',
    interpreter: 'none',
    exec_mode: 'fork',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      APACHE_RUN_USER: 'www-data',
      APACHE_RUN_GROUP: 'www-data',
      APACHE_LOG_DIR: '/var/log/apache2',
      APACHE_LOCK_DIR: '/var/lock/apache2',
      APACHE_PID_FILE: '/var/run/apache2/apache2.pid'
    }
  }]
};
EOF

# Test Apache config
apache2ctl configtest

# Stop Apache service (PM2 will manage it)
systemctl stop apache2 2>/dev/null || true
systemctl disable apache2 2>/dev/null || true

# Start with PM2
cd $APP_DIR
pm2 start ecosystem.config.js
pm2 save
pm2 startup

# Create config template
cat > $APP_DIR/config/env_template.php << 'EOF'
<?php
// External Database Configuration
define('DB_HOST', 'your-external-db-host.com');
define('DB_USER', 'your_db_username');
define('DB_PASS', 'your_db_password');
define('DB_NAME', 'your_db_name');
define('DB_PORT', 3306);

// Application Configuration
define('APP_URL', 'https://mindscan.mendingmind.org');
define('APP_ENV', 'production');

// PDF Configuration
define('WKHTMLTOPDF_PATH', '/usr/bin/wkhtmltopdf');

// Security
define('APP_KEY', 'generate-a-random-32-character-string');
?>
EOF

echo ""
echo "🎉 Deployment fix completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure database: cp $APP_DIR/config/env_template.php $APP_DIR/config/config.php"
echo "2. Edit config: nano $APP_DIR/config/config.php"
echo "3. Get SSL: certbot --apache -d mindscan.mendingmind.org"
echo ""
echo "📊 PM2 Status:"
pm2 status
echo ""
echo "🌐 Server IP: $(curl -s ifconfig.me)"
echo "✅ Site should be accessible at: https://mindscan.mendingmind.org"
