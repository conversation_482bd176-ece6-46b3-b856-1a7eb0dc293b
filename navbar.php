<?php
include("php/common.php");
// $_SESSION['id'] = "MSbwLuC53M";
//echo checkSession();
global $webUrl;


if (isset($_SESSION['start']) && (time() - $_SESSION['start'] > 900)) {
    echo "<script>window.location.href='" . $webUrl . "php/logout.php';</script>";
}
if (checkSession() == "true") {
    $user_data = fetchData("username,email,status,test_date,age,gender", "users", "id='" . $_SESSION['id'] . "'", "time_of_registration desc");
    //print_r( $user_data);
    if (count($user_data) > 1) {
        $user_name = $user_data["username"];
        $email = $user_data["email"];
        if ($user_data["status"] == '1' && (strpos($_SERVER['REQUEST_URI'], "reports") == false && strpos($_SERVER['REQUEST_URI'], "results") == false)) {
            echo "<script>window.location.href='" . $webUrl . "reports'</script>";
        }
        // elseif($user_data["status"] == '0' && !str_contains($_SERVER['REQUEST_URI'], "remember")){
        //     echo "<script>window.location.href='".$webUrl."remember'</script>";
        // }
    } else {
        session_destroy();
        echo "<script>window.location.href='" . $webUrl . "'</script>";
    }
} else {
    session_destroy();
    echo "<script>window.location.href='" . $webUrl . "'</script>";
}
echo '<!DOCTYPE html>
<html lang="en-US">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1,minimum-scale=1.0, maximum-scale=1.0" />
    <title> ' . ucfirst(explode("?", explode("/", $_SERVER['REQUEST_URI'])[count(explode("/", $_SERVER['REQUEST_URI'])) - 1])[0]) . (strpos($_SERVER['REQUEST_URI'], "question") ? ' ' . explode("=", $_SERVER['REQUEST_URI'])[1] : '') . ' | MindScan</title>
   <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link href="css/bootstrap.min.css?ver=1.1.0" rel="stylesheet" />
    <link href="css/mdb.css?ver=1.1.0" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css?family=Hind" rel="stylesheet">
    <link href="css/main.css?ver=1.1.0" rel="stylesheet" />
    <link href="css/styles.css" rel="stylesheet" />
    <link rel="icon" href="./img/favicon.png" type="image/png">';
if (isset($_COOKIE["lang"]) && $_COOKIE["lang"] == "hi") {
    echo '<script>
    function googleTranslateElementInit() {
        new google.translate.TranslateElement({
            pageLanguage: "en", 
            includedLanguages:"hi", 
            autoDisplay: false
        }, "google_translate_element");
        var a = document.querySelector("#google_translate_element select");
        a.selectedIndex=1;
        a.dispatchEvent(new Event("change"));
    }
</script>
<script src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script><style>#google_translate_element,.skiptranslate{display:none;}body{top:0!important;}</style>';
}
echo '
</head>';
echo '<body id="top">
   ';
