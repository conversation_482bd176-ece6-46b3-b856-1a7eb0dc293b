#!/bin/bash

# Get server IP and configure Coolify routing
echo "🔍 Getting server information..."

# Get IPv4 address
IPV4=$(curl -s -4 ifconfig.me 2>/dev/null || curl -s ip4.me/api/ 2>/dev/null || hostname -I | awk '{print $1}')
echo "Server IPv4: $IPV4"

echo ""
echo "🚨 IMMEDIATE ACTION REQUIRED:"
echo "============================="
echo ""
echo "1. UPDATE DNS RECORD:"
echo "   Go to your DNS provider (Cloudflare, etc.)"
echo "   Change A record for 'mindscan.mendingmind.org'"
echo "   FROM: *************"
echo "   TO: $IPV4"
echo ""
echo "2. CONFIGURE COOLIFY:"
echo "   Since Coolify is returning 404, you need to add your domain"
echo ""

# Check if we can access Coolify
echo "🔧 Coolify Configuration Steps:"
echo "==============================="
echo ""
echo "Method 1: Via Coolify Dashboard (Recommended)"
echo "1. Open Coolify web interface: http://$IPV4:8000 or https://$IPV4:8000"
echo "2. Login to your Coolify dashboard"
echo "3. Go to your project or create a new one"
echo "4. Add a new 'Service' or 'Application'"
echo "5. Configure:"
echo "   - Name: mindscan"
echo "   - Type: External/Proxy Service"
echo "   - Domain: mindscan.mendingmind.org" 
echo "   - Internal Port: 8081"
echo "   - Target URL: http://localhost:8081"
echo ""

echo "Method 2: Direct Docker Command (If Method 1 doesn't work)"
echo "This creates a proxy container that routes your domain to Apache:"
echo ""
cat << 'EOF'
docker run -d \
  --name mindscan-proxy \
  --network coolify \
  --label "traefik.enable=true" \
  --label "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)" \
  --label "traefik.http.routers.mindscan.entrypoints=web,websecure" \
  --label "traefik.http.routers.mindscan.tls.certresolver=letsencrypt" \
  --label "traefik.http.services.mindscan.loadbalancer.server.port=8081" \
  --label "traefik.http.services.mindscan.loadbalancer.server.url=http://host.docker.internal:8081" \
  --add-host=host.docker.internal:host-gateway \
  nginx:alpine
EOF

echo ""
echo "🧪 Testing Steps:"
echo "================="
echo "After DNS propagation (5-30 minutes):"
echo "1. Test DNS: nslookup mindscan.mendingmind.org"
echo "2. Test domain: curl -I http://mindscan.mendingmind.org"
echo "3. Test HTTPS: curl -I https://mindscan.mendingmind.org"
echo ""

echo "🚀 Quick Test Apache (should work now):"
echo "curl -H 'Host: mindscan.mendingmind.org' http://localhost:8081"
curl -H "Host: mindscan.mendingmind.org" http://localhost:8081 | head -20

echo ""
echo "📝 Summary:"
echo "==========="
echo "✅ Apache is running on port 8081"
echo "✅ Coolify proxy is running on ports 80/443"
echo "❌ DNS points to wrong IP (************* instead of $IPV4)"
echo "❌ Coolify doesn't know about mindscan.mendingmind.org domain"
echo ""
echo "💡 Once you fix DNS and configure Coolify, your site will work!"
