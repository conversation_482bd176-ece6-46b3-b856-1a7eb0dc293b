<?php
include("navbar.php");
$getuser = fetchData("all", "users", "id='" . $_SESSION['id'] . "'");
// print_r($getuser);
if ($getuser['status'] != "1") {
    echo "<script>window.location.href='" . $webUrl . "php/logout.php';</script>";
}
?>
<section class="align-items-center d-flex flex-column justify-content-between post-quiz">
    <div class="container">
        <div class="row">
            <div class="col-6 mt-4 mx-auto text-center">
                <img src="./img/Mind_Logo.png" class="img-fluid mx-auto" alt="Logo">
            </div>
        </div>
    </div>
    <div class="banner-wrapper position-relative">
        <div class="container">
            <div class="row">
                <div class="col-6 mx-auto">
                    <img src="./img/reports-banner.png" class="img-fluid" alt="Reports">
                </div>
                <div class="mt-2 mx-auto report-title">
                    <h1 class="text-uppercase mb-0 text-theme-dark font-weight-bold">
                        MindScan,
                    </h1>
                    <h6 class="mb-4 text-theme-dark font-weight-bold">
                        a mental health checkup camp
                    </h6>
                    <div class="divider my-3"></div>
                    <p>brought to you by Mending Mind.</p>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="report-bottom position-relative">
            <img src="./img/report-bottom.png" class="img-fluid" alt="bg">
            <div class="button-wrapper">
                <h5 class="mb-3 font-weight-bold text-theme-dark text-uppercase">Download your <br>report here</h5>
                <a class="m-0 py-2 w-100 btn btn-theme-yellow font-weight-boldbtn btn-theme-yellow font-weight-bold shadow-none waves-effect waves-light" href="<?php echo $webUrl; ?>results" target="_blank" rel="noopener noreferrer">
                    click here >
                </a>
                <img src="./img/curved-arrow.png" alt="">
            </div>
        </div>
    </div>
</section>
<script type="text/javascript" src="scripts/jquery.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/popper.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/bootstrap.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/wow.min.js?ver=1.1.0"></script>
<script type="text/javascript" src="scripts/mdb.min.js?ver=1.1.0"></script>
<script src="scripts/main.js"></script>
<script>
    new WOW().init();
</script>

</body>

</html>