# Coolify Configuration Guide for mindscan.mendingmind.org

## Current Status
✅ Apache is running on port 8081  
✅ Application works on `http://server-ip:8081`  
❌ Domain `mindscan.mendingmind.org` not routing to Apache  

## The Problem
Coolify is running on ports 80/443 and handling all incoming requests for your domain. We need to configure Coolify to proxy requests from `mindscan.mendingmind.org` to your Apache server on port 8081.

## Solution: Configure Coolify Proxy

### Option 1: Create a New Service in Coolify (Recommended)

1. **Login to Coolify Dashboard**
   - Go to your Coolify web interface
   - Navigate to your project

2. **Create a New Service**
   - Click "Add New Resource" or "Add Service"
   - Choose "External Service" or "Proxy Service"

3. **Configure the Service**
   - **Name**: `mindscan-apache`
   - **Domain**: `mindscan.mendingmind.org`
   - **Port**: `8081`
   - **Target**: `http://localhost:8081` or `http://127.0.0.1:8081`

### Option 2: Add Domain to Existing Application

1. **Go to Your Existing Application**
   - Find your application in Coolify dashboard

2. **Add Domain**
   - Go to "Domains" section
   - Add domain: `mindscan.mendingmind.org`
   - Set port: `8081`

### Option 3: Manual Traefik Configuration

If the above doesn't work, you may need to configure Traefik (Coolify's reverse proxy) manually:

1. **Create Traefik Labels File**
   ```bash
   # Create the configuration
   cat > /opt/coolify/compose/mindscan-labels.yml << 'EOF'
   version: '3.8'
   
   services:
     mindscan-proxy:
       image: traefik/whoami
       labels:
         - "traefik.enable=true"
         - "traefik.http.routers.mindscan.rule=Host(\`mindscan.mendingmind.org\`)"
         - "traefik.http.routers.mindscan.entrypoints=web,websecure"
         - "traefik.http.routers.mindscan.tls.certresolver=letsencrypt"
         - "traefik.http.services.mindscan.loadbalancer.server.port=8081"
         - "traefik.http.services.mindscan.loadbalancer.server.url=http://localhost:8081"
   EOF
   ```

2. **Apply the Configuration**
   ```bash
   docker-compose -f /opt/coolify/compose/mindscan-labels.yml up -d
   ```

## Verification Steps

1. **Test Local Apache**
   ```bash
   curl -H "Host: mindscan.mendingmind.org" http://localhost:8081
   ```

2. **Check DNS Resolution**
   ```bash
   nslookup mindscan.mendingmind.org
   dig mindscan.mendingmind.org
   ```

3. **Test Domain Access**
   ```bash
   curl -I http://mindscan.mendingmind.org
   curl -I https://mindscan.mendingmind.org
   ```

## Common Issues and Solutions

### Issue 1: DNS Not Pointing to Server
- Make sure `mindscan.mendingmind.org` A record points to your server IP
- Check with your DNS provider (Cloudflare, etc.)

### Issue 2: Coolify Not Recognizing the Domain
- Restart Coolify: `docker restart coolify`
- Check Coolify logs: `docker logs coolify`

### Issue 3: SSL Certificate Issues
- Let Coolify handle SSL automatically
- Or disable SSL initially for testing

### Issue 4: Port Not Accessible
- Make sure Apache is running: `pm2 status`
- Test locally: `curl http://localhost:8081`
- Check firewall: `ufw status`

## Quick Debug Commands

```bash
# Check Apache status
pm2 status
pm2 logs mindscan-apache

# Check what's listening on port 8081
ss -tulpn | grep :8081
netstat -tulpn | grep :8081

# Test Apache directly
curl -v http://localhost:8081

# Check Coolify containers
docker ps | grep coolify
docker logs coolify

# Check Traefik routing
docker logs traefik
```

## Final Steps After Coolify Configuration

1. **Update Application Configuration**
   - Make sure `config/config.php` has correct database settings
   - Test the PHP application functionality

2. **Enable SSL**
   - Let Coolify handle SSL certificate via Let's Encrypt
   - Or configure manually if needed

3. **Test Complete Workflow**
   - Visit `https://mindscan.mendingmind.org`
   - Test user registration/login
   - Test PDF generation
   - Test email functionality

## Need Help?

If you're still having issues:
1. Share your Coolify dashboard screenshot
2. Provide output of: `docker ps | grep coolify`
3. Provide output of: `curl -I http://mindscan.mendingmind.org`
4. Check Coolify documentation for your specific version
