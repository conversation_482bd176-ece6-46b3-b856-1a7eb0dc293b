-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: Mar 17, 2023 at 01:45 PM
-- Server version: 10.5.13-MariaDB-cll-lve
-- PHP Version: 7.2.34

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `u831135231_mm_831135231`
--

-- --------------------------------------------------------

--
-- Table structure for table `answers`
--

CREATE TABLE `answers` (
  `id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `q_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `answer` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `score` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `answers`
--

INSERT INTO `answers` (`id`, `user_id`, `q_id`, `answer`, `score`) VALUES
('ANS02kYMH', 'MSCJQ7km', 'Q7', 'OPT2', 1),
('ANS04zEYd', 'MSEwAQL0', 'Q13', 'OPT2', 1),
('ANS05DGlJ', 'MSrJv6lc', 'Q1', 'OPT1', 0),
('ANS06PWdt', 'MSD2CdU4', 'Q14', 'OPT2', 1),
('ANS06qQqM', 'MSrsK5M0', 'Q25', 'OPT5', 0),
('ANS09abWr', 'MSuq7kIE', 'Q13', 'OPT2', 1),
('ANS0AVOo1', 'MSHVECsr', 'Q13', 'OPT3', 2),
('ANS0AWAH1', 'MSCJQ7km', 'Q26', 'OPT7', 2),
('ANS0bcEaE', 'MSKrk0hq', 'Q1', 'OPT1', 0),
('ANS0BU9mN', 'MSXxuKfh', 'Q18', 'OPT2', 1),
('ANS0Bym2T', 'MSWCMBqh', 'Q21', 'OPT2', 1),
('ANS0C7Gld', 'MSwTXozI', 'Q4', 'OPT1', 0),
('ANS0cL35F', 'MSe8IKuy', 'Q9', 'OPT2', 1),
('ANS0d9NCT', 'MSEwAQL0', 'Q5', 'OPT4', 3),
('ANS0DHw5n', 'MS6zWUZX', 'Q15', 'OPT1', 0),
('ANS0Hh5eQ', 'MSHVECsr', 'Q5', 'OPT1', 0),
('ANS0hLVGg', 'MSwcZn3F', 'Q20', 'OPT2', 1),
('ANS0LuiM9', 'MS2GS0M4', 'Q7', 'OPT1', 0),
('ANS0n0r8X', 'MShC7NUw', 'Q5', 'OPT4', 3),
('ANS0O8szM', 'MSkUYFSU', 'Q10', 'OPT2', 1),
('ANS0p5uvJ', 'MSCJQ7km', 'Q2', 'OPT2', 1),
('ANS0PpUNW', 'MSYZvsiu', 'Q19', 'OPT1', 0),
('ANS0r5n3B', 'MSrsK5M0', 'Q17', 'OPT2', 1),
('ANS0rZiy2', 'MSC84et8', 'Q4', 'OPT2', 1),
('ANS0sy7pv', 'MS5oYfaH', 'Q21', 'OPT1', 0),
('ANS0t0qjL', 'MS6zWUZX', 'Q10', 'OPT2', 1),
('ANS0UsefI', 'MS8Ku3zf', 'Q17', 'OPT4', 3),
('ANS0uYkKi', 'MSYZvsiu', 'Q15', 'OPT2', 1),
('ANS0vCzyN', 'MSUY57sP', 'Q10', 'OPT1', 0),
('ANS0YGC5A', 'MS2GS0M4', 'Q24', 'OPT6', 1),
('ANS0yozf1', 'MSGlWdke', 'Q1', 'OPT2', 1),
('ANS0ZPFxF', 'MSD2CdU4', 'Q16', 'OPT2', 1),
('ANS10R1zF', 'MSS6eh4N', 'Q10', 'OPT2', 1),
('ANS11TLC8', 'MS8DLldu', 'Q12', 'OPT2', 1),
('ANS13jsWc', 'MSB9hlkR', 'Q4', 'OPT1', 0),
('ANS178dLP', 'MSB9hlkR', 'Q8', 'OPT1', 0),
('ANS1Bmv6m', 'MShEdYT5', 'Q2', 'OPT2', 1),
('ANS1BqQ7y', 'MSxafMEG', 'Q4', 'OPT2', 1),
('ANS1CttVJ', 'MSX9aTK5', 'Q24', 'OPT5', 0),
('ANS1EanY6', 'MSGlWdke', 'Q5', 'OPT4', 3),
('ANS1GA2kL', 'MSxAFfYH', 'Q9', 'OPT1', 0),
('ANS1hcfe9', 'MSWCMBqh', 'Q18', 'OPT2', 1),
('ANS1he5iw', 'MSCvjNTp', 'Q9', 'OPT2', 1),
('ANS1iFd27', 'MSdO9BfU', 'Q1', 'OPT1', 0),
('ANS1jaDO3', 'MSrsK5M0', 'Q1', 'OPT2', 1),
('ANS1k3leF', 'MSjIxFsI', 'Q25', 'OPT5', 0),
('ANS1LxnTQ', 'MSxafMEG', 'Q16', 'OPT1', 0),
('ANS1NFnef', 'MSqHuFsF', 'Q14', 'OPT4', 3),
('ANS1nLYU2', 'MSmLaR9I', 'Q1', 'OPT2', 1),
('ANS1NQPIW', 'MSQq566a', 'Q9', 'OPT2', 1),
('ANS1PZFg3', 'MSkUYFSU', 'Q6', 'OPT3', 2),
('ANS1QKpCB', 'MSxAFfYH', 'Q19', 'OPT1', 0),
('ANS1r4SfO', 'MSbHrzIU', 'Q24', 'OPT5', 0),
('ANS1THCwS', 'MSSbGTht', 'Q10', 'OPT1', 0),
('ANS1tq0Ia', 'MSjSGqfF', 'Q11', 'OPT3', 2),
('ANS1twYv9', 'MSOiRHyL', 'Q2', 'OPT1', 0),
('ANS1ufZjM', 'MSwcZn3F', 'Q1', 'OPT2', 1),
('ANS1uGeeG', 'MSkUYFSU', 'Q4', 'OPT2', 1),
('ANS1uNNKJ', 'MSHVECsr', 'Q8', 'OPT2', 1),
('ANS1vkXCL', 'MSqHuFsF', 'Q22', 'OPT6', 1),
('ANS1xaRdJ', 'MSOTfXXF', 'Q13', 'OPT2', 1),
('ANS1Y7gm0', 'MSWCMBqh', 'Q26', 'OPT6', 1),
('ANS1ZHkov', 'MS8Ku3zf', 'Q8', 'OPT4', 3),
('ANS206JiN', 'MSqHuFsF', 'Q18', 'OPT4', 3),
('ANS20E4ut', 'MSe8IKuy', 'Q25', 'OPT5', 0),
('ANS20lAVn', 'MStavaQ6', 'Q13', 'OPT1', 0),
('ANS21v2Sk', 'MSYkD4Nr', 'Q19', 'OPT1', 0),
('ANS222ggI', 'MS7T2bhr', 'Q18', 'OPT4', 3),
('ANS22rlkL', 'MSUJDtjy', 'Q10', 'OPT2', 1),
('ANS25wBSE', 'MSjSGqfF', 'Q12', 'OPT2', 1),
('ANS26fGUu', 'MS138aYF', 'Q15', 'OPT2', 1),
('ANS27zI3C', 'MS79deyJ', 'Q26', 'OPT6', 1),
('ANS28Qjui', 'MShKvqCr', 'Q9', 'OPT1', 0),
('ANS29T0IO', 'MSFQYvYL', 'Q9', 'OPT1', 0),
('ANS2Airtq', 'MST7153t', 'Q25', 'OPT7', 2),
('ANS2cbPR5', 'MShKvqCr', 'Q14', 'OPT1', 0),
('ANS2Cdttg', 'MSMSWRrW', 'Q2', 'OPT1', 0),
('ANS2cL5Xa', 'MSWCMBqh', 'Q24', 'OPT6', 1),
('ANS2clGpX', 'MS8DLldu', 'Q26', 'OPT6', 1),
('ANS2DouLs', 'MSUVdR45', 'Q16', 'OPT2', 1),
('ANS2fR9DR', 'MSMSWRrW', 'Q15', 'OPT4', 3),
('ANS2FsZ92', 'MSJCXCcq', 'Q2', 'OPT1', 0),
('ANS2fUVIv', 'MSLEeXDP', 'Q19', 'OPT2', 1),
('ANS2gcTAb', 'MSCJQ7km', 'Q4', 'OPT4', 3),
('ANS2iFctm', 'MSwzvNE9', 'Q6', 'OPT2', 1),
('ANS2IMPTT', 'MSUJDtjy', 'Q11', 'OPT4', 3),
('ANS2INgMk', 'MSLEeXDP', 'Q11', 'OPT2', 1),
('ANS2kV1wH', 'MS7f7XHX', 'Q14', 'OPT3', 2),
('ANS2LCDjb', 'MSAlECsL', 'Q25', 'OPT6', 1),
('ANS2Nqcea', 'MSMSWRrW', 'Q18', 'OPT3', 2),
('ANS2ORtiu', 'MSGNt66z', 'Q12', 'OPT2', 1),
('ANS2pF9nP', 'MS8Ku3zf', 'Q20', 'OPT3', 2),
('ANS2RJF97', 'MS7uU47p', 'Q16', 'OPT1', 0),
('ANS2Rt34u', 'MSwcZn3F', 'Q26', 'OPT5', 0),
('ANS2UHiDk', 'MSUJDtjy', 'Q6', 'OPT1', 0),
('ANS2uZ2X7', 'MSYkD4Nr', 'Q17', 'OPT1', 0),
('ANS2vhS4L', 'MS79deyJ', 'Q3', 'OPT3', 2),
('ANS2WqMIv', 'MSCvjNTp', 'Q13', 'OPT2', 1),
('ANS2wUxva', 'MS2S9Sh8', 'Q11', 'OPT2', 1),
('ANS2wvmPl', 'MST7153t', 'Q17', 'OPT2', 1),
('ANS30dh3f', 'MS7f7XHX', 'Q21', 'OPT2', 1),
('ANS36XMZ7', 'MSeqrFEz', 'Q15', 'OPT2', 1),
('ANS39CyLk', 'MSH2zQ4z', 'Q17', 'OPT2', 1),
('ANS3BBa8E', 'MSb1vGgr', 'Q9', 'OPT3', 2),
('ANS3bgbOL', 'MSYZvsiu', 'Q1', 'OPT2', 1),
('ANS3BNmJr', 'MSGNt66z', 'Q23', 'OPT5', 0),
('ANS3c4ig4', 'MSCvjNTp', 'Q24', 'OPT6', 1),
('ANS3D8UWU', 'MSGNt66z', 'Q16', 'OPT1', 0),
('ANS3durMG', 'MSYZvsiu', 'Q21', 'OPT2', 1),
('ANS3dXhqS', 'MSMzDgKm', 'Q12', 'OPT3', 2),
('ANS3eB1su', 'MSYkD4Nr', 'Q23', 'OPT5', 0),
('ANS3EcZ9l', 'MSB9hlkR', 'Q11', 'OPT1', 0),
('ANS3eTd1d', 'MSjIxFsI', 'Q3', 'OPT1', 0),
('ANS3EtuLP', 'MSB9hlkR', 'Q21', 'OPT1', 0),
('ANS3EuUYC', 'MS2S9Sh8', 'Q20', 'OPT2', 1),
('ANS3fV75H', 'MS2E6rGI', 'Q17', 'OPT3', 2),
('ANS3GlE3W', 'MST7153t', 'Q19', 'OPT1', 0),
('ANS3gq275', 'MSUVdR45', 'Q23', 'OPT6', 1),
('ANS3HKsjb', 'MS2E6rGI', 'Q5', 'OPT4', 3),
('ANS3iekRz', 'MStavaQ6', 'Q6', 'OPT4', 3),
('ANS3k0nHZ', 'MSCvjNTp', 'Q10', 'OPT1', 0),
('ANS3lBdaI', 'MSS6eh4N', 'Q11', 'OPT4', 3),
('ANS3LD2vO', 'MS8Ku3zf', 'Q23', 'OPT5', 0),
('ANS3lQ2CP', 'MSWzJzOu', 'Q6', 'OPT2', 1),
('ANS3Ltqpu', 'MS138aYF', 'Q10', 'OPT1', 0),
('ANS3m1rMe', 'MS5oYfaH', 'Q22', 'OPT6', 1),
('ANS3MFrWo', 'MSUVdR45', 'Q6', 'OPT3', 2),
('ANS3mpHAA', 'MSQq566a', 'Q25', 'OPT6', 1),
('ANS3mwW5i', 'MS138aYF', 'Q13', 'OPT2', 1),
('ANS3nhQVu', 'MSWCMBqh', 'Q19', 'OPT2', 1),
('ANS3OanWh', 'MS8Zetbr', 'Q3', 'OPT1', 0),
('ANS3p26O7', 'MSrql4yA', 'Q11', 'OPT4', 3),
('ANS3PfWG1', 'MSD2CdU4', 'Q21', 'OPT2', 1),
('ANS3Rhd6w', 'MS8DLldu', 'Q21', 'OPT2', 1),
('ANS3sFyJp', 'MSFQYvYL', 'Q17', 'OPT1', 0),
('ANS3SOHk0', 'MS8Ku3zf', 'Q7', 'OPT1', 0),
('ANS3SVCQD', 'MSe8IKuy', 'Q5', 'OPT1', 0),
('ANS3tcqZ2', 'MSxAFfYH', 'Q7', 'OPT1', 0),
('ANS3uXAvu', 'MSGlWdke', 'Q17', 'OPT4', 3),
('ANS3wHs1u', 'MSwcZn3F', 'Q19', 'OPT1', 0),
('ANS3xHpk6', 'MSbHrzIU', 'Q17', 'OPT1', 0),
('ANS3yQSRx', 'MSLEeXDP', 'Q25', 'OPT5', 0),
('ANS3zUtJt', 'MS1kbVC6', 'Q11', 'OPT3', 2),
('ANS424erZ', 'MSj5xUgK', 'Q22', 'OPT6', 1),
('ANS43Cpmi', 'MS7f7XHX', 'Q9', 'OPT1', 0),
('ANS43mLdL', 'MS5xzf68', 'Q14', 'OPT1', 0),
('ANS43zUj6', 'MSj5xUgK', 'Q9', 'OPT2', 1),
('ANS444s2X', 'MSHVECsr', 'Q18', 'OPT4', 3),
('ANS44yAXT', 'MS2wWZvm', 'Q25', 'OPT5', 0),
('ANS459QeC', 'MSe8IKuy', 'Q4', 'OPT1', 0),
('ANS46IVpC', 'MS2wWZvm', 'Q19', 'OPT1', 0),
('ANS47d85S', 'MSwzvNE9', 'Q4', 'OPT1', 0),
('ANS483yIg', 'MSZhKZEA', 'Q15', 'OPT4', 3),
('ANS49aQ9g', 'MSHVECsr', 'Q23', 'OPT5', 0),
('ANS4a1ruB', 'MShivwr1', 'Q25', 'OPT5', 0),
('ANS4A63Z0', 'MSjSGqfF', 'Q20', 'OPT2', 1),
('ANS4c0PuI', 'MSwTXozI', 'Q9', 'OPT1', 0),
('ANS4CFLmI', 'MSkUYFSU', 'Q13', 'OPT2', 1),
('ANS4DcmS7', 'MSCJQ7km', 'Q19', 'OPT4', 3),
('ANS4dRYHO', 'MST7153t', 'Q4', 'OPT1', 0),
('ANS4IhGwi', 'MSQq566a', 'Q15', 'OPT2', 1),
('ANS4iWV3U', 'MS53AquJ', 'Q10', 'OPT3', 2),
('ANS4m2SHK', 'MSeqrFEz', 'Q3', 'OPT1', 0),
('ANS4M984d', 'MSkUYFSU', 'Q16', 'OPT1', 0),
('ANS4mR4YM', 'MSkUYFSU', 'Q1', 'OPT2', 1),
('ANS4mr68F', 'MShKvqCr', 'Q23', 'OPT5', 0),
('ANS4MTbXm', 'MS8Ku3zf', 'Q1', 'OPT3', 2),
('ANS4nIPPn', 'MS1kbVC6', 'Q9', 'OPT2', 1),
('ANS4OSJbI', 'MSH2zQ4z', 'Q4', 'OPT1', 0),
('ANS4ppY9T', 'MSjSGqfF', 'Q22', 'OPT7', 2),
('ANS4R7sQj', 'MSC84et8', 'Q26', 'OPT6', 1),
('ANS4RyE9H', 'MSrJv6lc', 'Q18', 'OPT1', 0),
('ANS4t86Sf', 'MS2E6rGI', 'Q14', 'OPT4', 3),
('ANS4urNrd', 'MSWCMBqh', 'Q3', 'OPT1', 0),
('ANS4USqhU', 'MSe8IKuy', 'Q26', 'OPT6', 1),
('ANS4Ve09I', 'MS5aBjQf', 'Q23', 'OPT6', 1),
('ANS4whJDK', 'MShivwr1', 'Q19', 'OPT1', 0),
('ANS4wIjQ3', 'MSGTyAts', 'Q9', 'OPT3', 2),
('ANS4YajT9', 'MSEDOwl4', 'Q11', 'OPT4', 3),
('ANS51zrqI', 'MSOTfXXF', 'Q4', 'OPT1', 0),
('ANS544ArW', 'MS7T2bhr', 'Q23', 'OPT7', 2),
('ANS546BmE', 'MSB9hlkR', 'Q25', 'OPT5', 0),
('ANS54FwMA', 'MSwTXozI', 'Q19', 'OPT1', 0),
('ANS54PHoz', 'MSMzDgKm', 'Q21', 'OPT2', 1),
('ANS55mBcx', 'MSkUYFSU', 'Q5', 'OPT2', 1),
('ANS56th9r', 'MSFQYvYL', 'Q1', 'OPT2', 1),
('ANS59UgBn', 'MS7T2bhr', 'Q12', 'OPT4', 3),
('ANS5b7iss', 'MSSbGTht', 'Q2', 'OPT1', 0),
('ANS5bgrkk', 'MS1kbVC6', 'Q3', 'OPT4', 3),
('ANS5CIbWX', 'MSxafMEG', 'Q17', 'OPT1', 0),
('ANS5e5Mda', 'MSMSWRrW', 'Q10', 'OPT1', 0),
('ANS5e95jW', 'MSCvjNTp', 'Q18', 'OPT2', 1),
('ANS5eQK7X', 'MSwzvNE9', 'Q19', 'OPT1', 0),
('ANS5fb3qe', 'MS1kbVC6', 'Q23', 'OPT5', 0),
('ANS5fPbie', 'MSwzvNE9', 'Q26', 'OPT7', 2),
('ANS5GKVFO', 'MS2GS0M4', 'Q1', 'OPT2', 1),
('ANS5GS4Yy', 'MS5oYfaH', 'Q7', 'OPT2', 1),
('ANS5izPgJ', 'MSQq566a', 'Q26', 'OPT5', 0),
('ANS5k8VRK', 'MSJCXCcq', 'Q19', 'OPT1', 0),
('ANS5l7BzJ', 'MSGTyAts', 'Q7', 'OPT2', 1),
('ANS5lMGDl', 'MSuM6ldN', 'Q7', 'OPT3', 2),
('ANS5ltJnb', 'MSOTfXXF', 'Q11', 'OPT2', 1),
('ANS5M8ExP', 'MSHVECsr', 'Q15', 'OPT2', 1),
('ANS5nPAb3', 'MSqHuFsF', 'Q21', 'OPT3', 2),
('ANS5O3bsR', 'MSMSWRrW', 'Q8', 'OPT2', 1),
('ANS5omHgv', 'MSOTfXXF', 'Q6', 'OPT2', 1),
('ANS5p3Zxe', 'MShKvqCr', 'Q24', 'OPT6', 1),
('ANS5PotIA', 'MStavaQ6', 'Q9', 'OPT1', 0),
('ANS5uke1e', 'MSbHrzIU', 'Q22', 'OPT6', 1),
('ANS5uVNjN', 'MSxafMEG', 'Q6', 'OPT2', 1),
('ANS5wHBJ9', 'MS2E6rGI', 'Q19', 'OPT2', 1),
('ANS5wwRlu', 'MS5xzf68', 'Q18', 'OPT1', 0),
('ANS5xEwyb', 'MSOTfXXF', 'Q8', 'OPT2', 1),
('ANS5YL1MO', 'MSGlWdke', 'Q23', 'OPT5', 0),
('ANS5Ylp7X', 'MSmCH78J', 'Q2', 'OPT1', 0),
('ANS5yVCHp', 'MSkUYFSU', 'Q11', 'OPT1', 0),
('ANS60jH5c', 'MSB9hlkR', 'Q1', 'OPT1', 0),
('ANS6alHj9', 'MSKBVoiE', 'Q26', 'OPT5', 0),
('ANS6B57tR', 'MSXxuKfh', 'Q20', 'OPT4', 3),
('ANS6Dhd9B', 'MSFQYvYL', 'Q6', 'OPT2', 1),
('ANS6ePyPu', 'MS53AquJ', 'Q7', 'OPT1', 0),
('ANS6eSWeC', 'MSOiRHyL', 'Q23', 'OPT5', 0),
('ANS6EvD1s', 'MSLEeXDP', 'Q13', 'OPT2', 1),
('ANS6eWxvL', 'MSJCXCcq', 'Q18', 'OPT1', 0),
('ANS6EZ2RR', 'MSOiRHyL', 'Q10', 'OPT1', 0),
('ANS6F0hmJ', 'MShC7NUw', 'Q7', 'OPT1', 0),
('ANS6fEBVH', 'MSCvjNTp', 'Q12', 'OPT2', 1),
('ANS6G5rha', 'MS7T2bhr', 'Q21', 'OPT2', 1),
('ANS6h40Xy', 'MSxAFfYH', 'Q2', 'OPT2', 1),
('ANS6hdM70', 'MSwTXozI', 'Q17', 'OPT1', 0),
('ANS6jebsq', 'MSGNt66z', 'Q17', 'OPT1', 0),
('ANS6jHleg', 'MSmLaR9I', 'Q13', 'OPT2', 1),
('ANS6jnk2g', 'MSKrk0hq', 'Q9', 'OPT4', 3),
('ANS6KTyfj', 'MSUY57sP', 'Q14', 'OPT2', 1),
('ANS6KWEZh', 'MSLEeXDP', 'Q10', 'OPT2', 1),
('ANS6kwOFS', 'MS2S9Sh8', 'Q15', 'OPT1', 0),
('ANS6mQhPY', 'MStavaQ6', 'Q2', 'OPT1', 0),
('ANS6ppEkZ', 'MSGTyAts', 'Q15', 'OPT1', 0),
('ANS6QlbJ0', 'MSYkD4Nr', 'Q10', 'OPT2', 1),
('ANS6qWrwx', 'MSSbGTht', 'Q14', 'OPT2', 1),
('ANS6rSwab', 'MSbHrzIU', 'Q9', 'OPT2', 1),
('ANS6s3OIU', 'MSMzDgKm', 'Q22', 'OPT6', 1),
('ANS6vCrFa', 'MShKvqCr', 'Q18', 'OPT2', 1),
('ANS6w4URK', 'MSCcWA57', 'Q2', 'OPT1', 0),
('ANS6wfs8l', 'MSxAFfYH', 'Q1', 'OPT2', 1),
('ANS6WR3YT', 'MSwTXozI', 'Q14', 'OPT1', 0),
('ANS6WS7gA', 'MShivwr1', 'Q21', 'OPT1', 0),
('ANS6wWOpI', 'MS8DLldu', 'Q6', 'OPT4', 3),
('ANS6xjF0R', 'MSKrk0hq', 'Q7', 'OPT2', 1),
('ANS6zYJBL', 'MSJCXCcq', 'Q13', 'OPT2', 1),
('ANS71zICQ', 'MSQq566a', 'Q5', 'OPT2', 1),
('ANS7219dG', 'MS138aYF', 'Q25', 'OPT5', 0),
('ANS722QAZ', 'MS7T2bhr', 'Q19', 'OPT1', 0),
('ANS732aMR', 'MS2GS0M4', 'Q14', 'OPT1', 0),
('ANS73L7ct', 'MS1kbVC6', 'Q21', 'OPT1', 0),
('ANS74ASzo', 'MS7uU47p', 'Q21', 'OPT1', 0),
('ANS74FGmA', 'MSYs8YjY', 'Q21', 'OPT4', 3),
('ANS75eOFO', 'MS2GS0M4', 'Q3', 'OPT2', 1),
('ANS78DcFt', 'MSKrk0hq', 'Q18', 'OPT1', 0),
('ANS7a9Yyg', 'MS5oYfaH', 'Q20', 'OPT2', 1),
('ANS7CIRnk', 'MSEDOwl4', 'Q5', 'OPT1', 0),
('ANS7Cxt69', 'MSj941zD', 'Q10', 'OPT4', 3),
('ANS7DWyN4', 'MSD2CdU4', 'Q15', 'OPT1', 0),
('ANS7fnCEG', 'MS53AquJ', 'Q13', 'OPT3', 2),
('ANS7fSyGS', 'MSS6eh4N', 'Q1', 'OPT1', 0),
('ANS7gBcEV', 'MSAlECsL', 'Q17', 'OPT1', 0),
('ANS7GPIKm', 'MSjIxFsI', 'Q22', 'OPT5', 0),
('ANS7HaJoA', 'MSxafMEG', 'Q8', 'OPT1', 0),
('ANS7hrJ0n', 'MSCcWA57', 'Q18', 'OPT1', 0),
('ANS7ilvec', 'MSYs8YjY', 'Q6', 'OPT2', 1),
('ANS7iLxIp', 'MSWCMBqh', 'Q25', 'OPT7', 2),
('ANS7Iv8Fz', 'MS138aYF', 'Q24', 'OPT5', 0),
('ANS7iZroL', 'MS2E6rGI', 'Q3', 'OPT3', 2),
('ANS7jlFvO', 'MSbHrzIU', 'Q15', 'OPT2', 1),
('ANS7KtNhw', 'MShC7NUw', 'Q1', 'OPT4', 3),
('ANS7ktvW7', 'MS7uU47p', 'Q18', 'OPT1', 0),
('ANS7moO83', 'MSWzJzOu', 'Q13', 'OPT1', 0),
('ANS7MpFHx', 'MSYs8YjY', 'Q26', 'OPT5', 0),
('ANS7N2ssb', 'MST7153t', 'Q7', 'OPT1', 0),
('ANS7NceDY', 'MSrsK5M0', 'Q11', 'OPT2', 1),
('ANS7nQiUn', 'MSKrk0hq', 'Q2', 'OPT1', 0),
('ANS7ntwN6', 'MSKBVoiE', 'Q19', 'OPT1', 0),
('ANS7Oi1Ja', 'MSMzDgKm', 'Q1', 'OPT2', 1),
('ANS7OSTUj', 'MSuq7kIE', 'Q22', 'OPT5', 0),
('ANS7sT9CS', 'MST7153t', 'Q2', 'OPT1', 0),
('ANS7TRciL', 'MSX9aTK5', 'Q6', 'OPT3', 2),
('ANS7XWcEh', 'MSqHuFsF', 'Q26', 'OPT6', 1),
('ANS7zxxgf', 'MSuq7kIE', 'Q5', 'OPT2', 1),
('ANS85AAAs', 'MSmLaR9I', 'Q22', 'OPT6', 1),
('ANS87cmBR', 'MStavaQ6', 'Q1', 'OPT1', 0),
('ANS8aYF8T', 'MSWCMBqh', 'Q7', 'OPT2', 1),
('ANS8cSDv1', 'MSmLaR9I', 'Q9', 'OPT2', 1),
('ANS8DhFEe', 'MSrql4yA', 'Q12', 'OPT4', 3),
('ANS8DOWjH', 'MSrsK5M0', 'Q2', 'OPT2', 1),
('ANS8EEoz1', 'MSJCXCcq', 'Q15', 'OPT1', 0),
('ANS8Eevh3', 'MSYkD4Nr', 'Q13', 'OPT2', 1),
('ANS8FN7xR', 'MS7uU47p', 'Q9', 'OPT1', 0),
('ANS8Fqenz', 'MSj5xUgK', 'Q24', 'OPT5', 0),
('ANS8gsvzH', 'MSpuxB2A', 'Q4', 'OPT1', 0),
('ANS8Gwsbj', 'MSHVECsr', 'Q25', 'OPT5', 0),
('ANS8GyGJ0', 'MSC84et8', 'Q21', 'OPT1', 0),
('ANS8I1wc5', 'MSpuxB2A', 'Q10', 'OPT1', 0),
('ANS8is8Wa', 'MSrJv6lc', 'Q2', 'OPT1', 0),
('ANS8Jc97L', 'MS7T2bhr', 'Q26', 'OPT5', 0),
('ANS8JFvQT', 'MShC7NUw', 'Q12', 'OPT3', 2),
('ANS8JZTgR', 'MSB9hlkR', 'Q22', 'OPT5', 0),
('ANS8kLpgk', 'MS2wWZvm', 'Q8', 'OPT1', 0),
('ANS8LdbSo', 'MSkUYFSU', 'Q14', 'OPT1', 0),
('ANS8mp7J2', 'MSKrk0hq', 'Q25', 'OPT5', 0),
('ANS8NdI2D', 'MSFQYvYL', 'Q2', 'OPT1', 0),
('ANS8q1csK', 'MSb1vGgr', 'Q17', 'OPT4', 3),
('ANS8R7skR', 'MS6zWUZX', 'Q19', 'OPT2', 1),
('ANS8svzBW', 'MSUVdR45', 'Q3', 'OPT1', 0),
('ANS8umU6e', 'MSb1vGgr', 'Q10', 'OPT4', 3),
('ANS8uWld8', 'MSCvjNTp', 'Q20', 'OPT1', 0),
('ANS8uWut2', 'MS1kbVC6', 'Q8', 'OPT2', 1),
('ANS8VxbXz', 'MSWzJzOu', 'Q21', 'OPT2', 1),
('ANS8w7QXM', 'MSUVdR45', 'Q11', 'OPT2', 1),
('ANS8xPE5b', 'MSjIxFsI', 'Q23', 'OPT5', 0),
('ANS8y1nun', 'MSmLaR9I', 'Q17', 'OPT2', 1),
('ANS8yMI89', 'MS79deyJ', 'Q2', 'OPT2', 1),
('ANS8Z9uPx', 'MSJCXCcq', 'Q5', 'OPT1', 0),
('ANS8zRfiC', 'MSb1vGgr', 'Q22', 'OPT7', 2),
('ANS915PsV', 'MSYs8YjY', 'Q8', 'OPT3', 2),
('ANS91MaWt', 'MSB9hlkR', 'Q3', 'OPT1', 0),
('ANS94VaKB', 'MS8DLldu', 'Q19', 'OPT1', 0),
('ANS9AINfi', 'MS5xzf68', 'Q10', 'OPT1', 0),
('ANS9buwtP', 'MSrJv6lc', 'Q6', 'OPT2', 1),
('ANS9bYnCh', 'MSxAFfYH', 'Q3', 'OPT1', 0),
('ANS9dDdrk', 'MSn52ZdO', 'Q13', 'OPT4', 3),
('ANS9EmJmh', 'MSX9aTK5', 'Q25', 'OPT6', 1),
('ANS9GhEBA', 'MSKrk0hq', 'Q4', 'OPT4', 3),
('ANS9H5VDp', 'MSpuxB2A', 'Q7', 'OPT1', 0),
('ANS9I683C', 'MSpuxB2A', 'Q22', 'OPT5', 0),
('ANS9IJqgp', 'MSYkD4Nr', 'Q3', 'OPT2', 1),
('ANS9JMAZf', 'MST7153t', 'Q1', 'OPT2', 1),
('ANS9KClPT', 'MS138aYF', 'Q23', 'OPT5', 0),
('ANS9kgvFF', 'MSKBVoiE', 'Q15', 'OPT1', 0),
('ANS9lDk2e', 'MSuq7kIE', 'Q12', 'OPT1', 0),
('ANS9LEf0D', 'MS1kbVC6', 'Q7', 'OPT1', 0),
('ANS9LmyDb', 'MSH2zQ4z', 'Q23', 'OPT5', 0),
('ANS9miNLX', 'MShivwr1', 'Q6', 'OPT2', 1),
('ANS9NOhQ3', 'MS6zWUZX', 'Q13', 'OPT2', 1),
('ANS9ohOQT', 'MSQq566a', 'Q18', 'OPT3', 2),
('ANS9Ophzw', 'MSWzJzOu', 'Q11', 'OPT3', 2),
('ANS9oViGw', 'MSCvjNTp', 'Q26', 'OPT5', 0),
('ANS9oXWB6', 'MSj5xUgK', 'Q20', 'OPT1', 0),
('ANS9QwNzY', 'MSrsK5M0', 'Q9', 'OPT1', 0),
('ANS9rhSW0', 'MS5xzf68', 'Q6', 'OPT1', 0),
('ANS9RwqKH', 'MShEdYT5', 'Q17', 'OPT2', 1),
('ANS9SePwy', 'MSCvjNTp', 'Q22', 'OPT5', 0),
('ANS9SlmHm', 'MS2wWZvm', 'Q1', 'OPT1', 0),
('ANS9Sm9Wc', 'MSWzJzOu', 'Q10', 'OPT2', 1),
('ANS9ssxH2', 'MS138aYF', 'Q9', 'OPT1', 0),
('ANS9TdzTR', 'MSS6eh4N', 'Q8', 'OPT2', 1),
('ANS9tJkFL', 'MS7uU47p', 'Q13', 'OPT1', 0),
('ANS9toDkL', 'MSUY57sP', 'Q26', 'OPT6', 1),
('ANS9UyZp6', 'MSX9aTK5', 'Q11', 'OPT1', 0),
('ANS9vwRRY', 'MSUJDtjy', 'Q4', 'OPT4', 3),
('ANS9vYF9c', 'MSkUYFSU', 'Q22', 'OPT5', 0),
('ANS9WgKxL', 'MS5aBjQf', 'Q25', 'OPT7', 2),
('ANS9wPFkg', 'MSEwAQL0', 'Q19', 'OPT2', 1),
('ANS9X2kBS', 'MSrql4yA', 'Q17', 'OPT4', 3),
('ANS9Xae5J', 'MSFQYvYL', 'Q3', 'OPT1', 0),
('ANS9XJUKA', 'MS2E6rGI', 'Q1', 'OPT3', 2),
('ANS9XM2zy', 'MSe8IKuy', 'Q13', 'OPT2', 1),
('ANS9XsFFK', 'MSB9hlkR', 'Q12', 'OPT1', 0),
('ANS9Y9SQN', 'MShC7NUw', 'Q13', 'OPT3', 2),
('ANS9Ym1dk', 'MSwzvNE9', 'Q22', 'OPT7', 2),
('ANS9yMVcD', 'MSAlECsL', 'Q23', 'OPT7', 2),
('ANS9Z02CZ', 'MSqHuFsF', 'Q24', 'OPT7', 2),
('ANSA02U7n', 'MS2GS0M4', 'Q19', 'OPT1', 0),
('ANSa04NY3', 'MSB9hlkR', 'Q17', 'OPT1', 0),
('ANSA0keXY', 'MShC7NUw', 'Q21', 'OPT2', 1),
('ANSa0lW3A', 'MSHVECsr', 'Q9', 'OPT1', 0),
('ANSA1m9QB', 'MSuM6ldN', 'Q2', 'OPT1', 0),
('ANSa1RJpi', 'MSYkD4Nr', 'Q22', 'OPT6', 1),
('ANSa43MTj', 'MSxAFfYH', 'Q21', 'OPT1', 0),
('ANSA4zszj', 'MSwcZn3F', 'Q4', 'OPT1', 0),
('ANSa5s3pY', 'MST7153t', 'Q14', 'OPT3', 2),
('ANSa5sDCn', 'MSMzDgKm', 'Q23', 'OPT6', 1),
('ANSa7EwJS', 'MSOiRHyL', 'Q22', 'OPT5', 0),
('ANSa9J7ZL', 'MSrql4yA', 'Q14', 'OPT4', 3),
('ANSAa59pd', 'MSwcZn3F', 'Q25', 'OPT5', 0),
('ANSaayqvq', 'MSmLaR9I', 'Q19', 'OPT1', 0),
('ANSAB9z7J', 'MSwzvNE9', 'Q1', 'OPT1', 0),
('ANSAbbqaa', 'MSMzDgKm', 'Q15', 'OPT3', 2),
('ANSABi5kh', 'MS1kbVC6', 'Q15', 'OPT1', 0),
('ANSabkT2K', 'MSLEeXDP', 'Q8', 'OPT2', 1),
('ANSabQ4kb', 'MSb1vGgr', 'Q5', 'OPT3', 2),
('ANSabrdnY', 'MSeqrFEz', 'Q12', 'OPT2', 1),
('ANSaBTWRM', 'MS1kbVC6', 'Q14', 'OPT2', 1),
('ANSABUQaG', 'MSUVdR45', 'Q25', 'OPT7', 2),
('ANSaCNrGv', 'MS53AquJ', 'Q19', 'OPT2', 1),
('ANSaCskMC', 'MS8Ku3zf', 'Q24', 'OPT6', 1),
('ANSAd9QUg', 'MSwTXozI', 'Q11', 'OPT1', 0),
('ANSadBCUK', 'MSxAFfYH', 'Q25', 'OPT6', 1),
('ANSadeOvn', 'MSKBVoiE', 'Q11', 'OPT1', 0),
('ANSAdfJSd', 'MShivwr1', 'Q8', 'OPT1', 0),
('ANSAF5pta', 'MSe8IKuy', 'Q24', 'OPT5', 0),
('ANSAfodiR', 'MSwTXozI', 'Q16', 'OPT1', 0),
('ANSAgFpMI', 'MSOiRHyL', 'Q9', 'OPT1', 0),
('ANSaGJTSJ', 'MSGNt66z', 'Q5', 'OPT1', 0),
('ANSaGUOSH', 'MSuq7kIE', 'Q9', 'OPT4', 3),
('ANSahnf73', 'MSmLaR9I', 'Q8', 'OPT2', 1),
('ANSAhOH9X', 'MS7uU47p', 'Q3', 'OPT1', 0),
('ANSaIBum2', 'MSCJQ7km', 'Q1', 'OPT3', 2),
('ANSAIjHSl', 'MSqHuFsF', 'Q4', 'OPT2', 1),
('ANSAIp5zA', 'MSwzvNE9', 'Q17', 'OPT1', 0),
('ANSAiZENu', 'MSEDOwl4', 'Q26', 'OPT5', 0),
('ANSaJa0G2', 'MSeqrFEz', 'Q5', 'OPT1', 0),
('ANSAJN4qR', 'MSS6eh4N', 'Q19', 'OPT2', 1),
('ANSAkeKNv', 'MSYs8YjY', 'Q20', 'OPT3', 2),
('ANSAKErRa', 'MSpuxB2A', 'Q25', 'OPT5', 0),
('ANSAkrp83', 'MS2GS0M4', 'Q12', 'OPT2', 1),
('ANSAl0Gpx', 'MShKvqCr', 'Q16', 'OPT1', 0),
('ANSalrX5e', 'MSEDOwl4', 'Q21', 'OPT2', 1),
('ANSaMCxl6', 'MSEDOwl4', 'Q19', 'OPT2', 1),
('ANSaMdlFG', 'MShKvqCr', 'Q25', 'OPT5', 0),
('ANSamFChO', 'MSj941zD', 'Q4', 'OPT4', 3),
('ANSAmNITd', 'MSYs8YjY', 'Q15', 'OPT4', 3),
('ANSANWAEQ', 'MSCvjNTp', 'Q14', 'OPT1', 0),
('ANSanzPUV', 'MShKvqCr', 'Q5', 'OPT2', 1),
('ANSAO1UcW', 'MSB9hlkR', 'Q14', 'OPT1', 0),
('ANSaOCtsz', 'MShEdYT5', 'Q16', 'OPT2', 1),
('ANSApqe5D', 'MSKBVoiE', 'Q16', 'OPT1', 0),
('ANSAraccO', 'MSZhKZEA', 'Q25', 'OPT6', 1),
('ANSarGwXX', 'MShEdYT5', 'Q6', 'OPT2', 1),
('ANSArM3rD', 'MSS6eh4N', 'Q7', 'OPT4', 3),
('ANSARwlei', 'MSUY57sP', 'Q21', 'OPT1', 0),
('ANSaRwQbA', 'MSrzIxEi', 'Q21', 'OPT3', 2),
('ANSAS2WNW', 'MSCJQ7km', 'Q21', 'OPT3', 2),
('ANSAsDh4S', 'MS7uU47p', 'Q14', 'OPT1', 0),
('ANSasheT6', 'MSXxuKfh', 'Q4', 'OPT1', 0),
('ANSAsKeBX', 'MSQq566a', 'Q13', 'OPT3', 2),
('ANSASragn', 'MSn52ZdO', 'Q20', 'OPT1', 0),
('ANSAsUrAE', 'MSxafMEG', 'Q3', 'OPT2', 1),
('ANSaTl0Nf', 'MShKvqCr', 'Q13', 'OPT2', 1),
('ANSaV5Nom', 'MSrsK5M0', 'Q21', 'OPT1', 0),
('ANSAV8ofi', 'MSqHuFsF', 'Q9', 'OPT2', 1),
('ANSaVELuk', 'MSj5xUgK', 'Q11', 'OPT2', 1),
('ANSavf9NN', 'MSuq7kIE', 'Q1', 'OPT1', 0),
('ANSaVzm8G', 'MSEDOwl4', 'Q6', 'OPT3', 2),
('ANSaXap53', 'MShC7NUw', 'Q22', 'OPT6', 1),
('ANSaXhJMD', 'MSjSGqfF', 'Q18', 'OPT3', 2),
('ANSAXUwba', 'MSMSWRrW', 'Q22', 'OPT7', 2),
('ANSAYNUD0', 'MSCcWA57', 'Q20', 'OPT1', 0),
('ANSAyrHFr', 'MSUY57sP', 'Q8', 'OPT1', 0),
('ANSAyT1LW', 'MSAlECsL', 'Q14', 'OPT2', 1),
('ANSaZ3zAh', 'MS79deyJ', 'Q7', 'OPT2', 1),
('ANSazJtC9', 'MSKrk0hq', 'Q21', 'OPT4', 3),
('ANSAZntvp', 'MSLEeXDP', 'Q22', 'OPT5', 0),
('ANSb0lJAa', 'MSD2CdU4', 'Q17', 'OPT1', 0),
('ANSB1EvL5', 'MS138aYF', 'Q7', 'OPT4', 3),
('ANSB1g0X8', 'MSj5xUgK', 'Q1', 'OPT2', 1),
('ANSb3xQ13', 'MSMzDgKm', 'Q20', 'OPT2', 1),
('ANSB4oMnf', 'MS6zWUZX', 'Q2', 'OPT1', 0),
('ANSB4uX2z', 'MSjIxFsI', 'Q4', 'OPT1', 0),
('ANSb6GEVP', 'MSxafMEG', 'Q22', 'OPT6', 1),
('ANSb6TGvW', 'MSWzJzOu', 'Q22', 'OPT5', 0),
('ANSb7FtLi', 'MSKBVoiE', 'Q10', 'OPT1', 0),
('ANSB7tulg', 'MS5aBjQf', 'Q3', 'OPT2', 1),
('ANSB7WDAI', 'MSYZvsiu', 'Q14', 'OPT2', 1),
('ANSb9qgoj', 'MSrsK5M0', 'Q18', 'OPT2', 1),
('ANSbaCCqp', 'MSH2zQ4z', 'Q15', 'OPT1', 0),
('ANSbaIQ8I', 'MSj941zD', 'Q12', 'OPT4', 3),
('ANSBaX77Z', 'MS53AquJ', 'Q17', 'OPT3', 2),
('ANSBb3Kua', 'MS6Hgbj4', 'Q5', 'OPT2', 1),
('ANSbCap5P', 'MSj941zD', 'Q20', 'OPT4', 3),
('ANSbCf2y5', 'MSXxuKfh', 'Q21', 'OPT3', 2),
('ANSBCM3Bh', 'MSS6eh4N', 'Q12', 'OPT2', 1),
('ANSbCudKf', 'MSe8IKuy', 'Q16', 'OPT2', 1),
('ANSbd9FuF', 'MSCcWA57', 'Q7', 'OPT1', 0),
('ANSBDaOlT', 'MS138aYF', 'Q2', 'OPT1', 0),
('ANSbDCh3I', 'MS8Ku3zf', 'Q26', 'OPT7', 2),
('ANSbdd9Au', 'MS2S9Sh8', 'Q19', 'OPT2', 1),
('ANSbdQMmo', 'MSxafMEG', 'Q5', 'OPT1', 0),
('ANSbeAVfj', 'MSX9aTK5', 'Q7', 'OPT1', 0),
('ANSBEnj7M', 'MSwcZn3F', 'Q6', 'OPT1', 0),
('ANSBEZ3Tp', 'MSHVECsr', 'Q26', 'OPT5', 0),
('ANSbF54Eu', 'MSb1vGgr', 'Q21', 'OPT4', 3),
('ANSBfuHbk', 'MS5xzf68', 'Q17', 'OPT1', 0),
('ANSBfxJLF', 'MSOTfXXF', 'Q3', 'OPT2', 1),
('ANSBGDme3', 'MS7uU47p', 'Q1', 'OPT1', 0),
('ANSBgwSqp', 'MSMSWRrW', 'Q20', 'OPT3', 2),
('ANSbHPuMS', 'MS1kbVC6', 'Q16', 'OPT1', 0),
('ANSbhX9Em', 'MSjIxFsI', 'Q5', 'OPT2', 1),
('ANSBi3z8R', 'MSCcWA57', 'Q21', 'OPT1', 0),
('ANSbJszZB', 'MSUJDtjy', 'Q14', 'OPT1', 0),
('ANSbkMp7W', 'MSSbGTht', 'Q15', 'OPT1', 0),
('ANSbljdrl', 'MS6Hgbj4', 'Q17', 'OPT2', 1),
('ANSblSqgl', 'MS5aBjQf', 'Q9', 'OPT3', 2),
('ANSBmen0q', 'MSKrk0hq', 'Q22', 'OPT5', 0),
('ANSbMQokp', 'MSj5xUgK', 'Q17', 'OPT2', 1),
('ANSbmRuNZ', 'MSWzJzOu', 'Q20', 'OPT4', 3),
('ANSbMwAjN', 'MSb1vGgr', 'Q20', 'OPT4', 3),
('ANSbnFQhN', 'MS8DLldu', 'Q11', 'OPT4', 3),
('ANSBP5m2e', 'MSFQYvYL', 'Q11', 'OPT2', 1),
('ANSbpqmHY', 'MST7153t', 'Q6', 'OPT3', 2),
('ANSbqcH5R', 'MSZhKZEA', 'Q17', 'OPT4', 3),
('ANSbQebnU', 'MSj5xUgK', 'Q21', 'OPT2', 1),
('ANSBQMcbs', 'MSYs8YjY', 'Q25', 'OPT7', 2),
('ANSBQTEi8', 'MSYkD4Nr', 'Q4', 'OPT2', 1),
('ANSBR5i0S', 'MSbHrzIU', 'Q7', 'OPT1', 0),
('ANSbrGWU2', 'MS7f7XHX', 'Q23', 'OPT5', 0),
('ANSBS8gRy', 'MS2GS0M4', 'Q8', 'OPT2', 1),
('ANSBSBPQe', 'MSpuxB2A', 'Q17', 'OPT1', 0),
('ANSbshey0', 'MS2wWZvm', 'Q23', 'OPT5', 0),
('ANSbsXZC1', 'MSGNt66z', 'Q26', 'OPT6', 1),
('ANSbtdu8W', 'MS7uU47p', 'Q22', 'OPT5', 0),
('ANSBvDxXp', 'MS7f7XHX', 'Q5', 'OPT1', 0),
('ANSbvgrqf', 'MSpuxB2A', 'Q1', 'OPT1', 0),
('ANSbwhEps', 'MSXxuKfh', 'Q13', 'OPT4', 3),
('ANSBWKXCu', 'MSrJv6lc', 'Q25', 'OPT5', 0),
('ANSBwWDL1', 'MS2wWZvm', 'Q16', 'OPT1', 0),
('ANSbWWE3R', 'MS79deyJ', 'Q6', 'OPT2', 1),
('ANSbXpkPF', 'MShivwr1', 'Q26', 'OPT5', 0),
('ANSByaaI0', 'MSSbGTht', 'Q20', 'OPT1', 0),
('ANSbZ6fBi', 'MS6Hgbj4', 'Q2', 'OPT1', 0),
('ANSBzbpzK', 'MSxAFfYH', 'Q11', 'OPT1', 0),
('ANSbzPFzD', 'MS138aYF', 'Q3', 'OPT4', 3),
('ANSBzr15j', 'MST7153t', 'Q22', 'OPT6', 1),
('ANSc0wdTp', 'MSWCMBqh', 'Q8', 'OPT2', 1),
('ANSC0z9q3', 'MSqHuFsF', 'Q2', 'OPT4', 3),
('ANSC1QWe6', 'MSj5xUgK', 'Q7', 'OPT2', 1),
('ANSc4qm7o', 'MSEwAQL0', 'Q25', 'OPT5', 0),
('ANSc5SLPY', 'MSKrk0hq', 'Q12', 'OPT2', 1),
('ANSC6FLXW', 'MShC7NUw', 'Q20', 'OPT1', 0),
('ANSc7eEjT', 'MSFQYvYL', 'Q19', 'OPT1', 0),
('ANSc7SaLE', 'MS7f7XHX', 'Q22', 'OPT5', 0),
('ANSC8gIBz', 'MShEdYT5', 'Q21', 'OPT2', 1),
('ANScAnjVZ', 'MShivwr1', 'Q15', 'OPT1', 0),
('ANSCaZ4XU', 'MS6zWUZX', 'Q24', 'OPT5', 0),
('ANSCcaHtQ', 'MShC7NUw', 'Q25', 'OPT5', 0),
('ANScClwIE', 'MS2S9Sh8', 'Q22', 'OPT6', 1),
('ANSccOeqy', 'MSKrk0hq', 'Q24', 'OPT6', 1),
('ANSCCz1RM', 'MSD2CdU4', 'Q3', 'OPT2', 1),
('ANSCczOXJ', 'MSrzIxEi', 'Q9', 'OPT3', 2),
('ANSCDCUOq', 'MSjSGqfF', 'Q25', 'OPT6', 1),
('ANScdQdWL', 'MSH2zQ4z', 'Q25', 'OPT5', 0),
('ANScdS9jf', 'MSAlECsL', 'Q19', 'OPT1', 0),
('ANScDwk16', 'MSqHuFsF', 'Q25', 'OPT7', 2),
('ANSCeiF8Y', 'MS53AquJ', 'Q3', 'OPT2', 1),
('ANSCengPS', 'MSWzJzOu', 'Q24', 'OPT5', 0),
('ANScFdatL', 'MSGNt66z', 'Q18', 'OPT2', 1),
('ANScg8tOW', 'MSQq566a', 'Q8', 'OPT4', 3),
('ANScgk1JF', 'MSrql4yA', 'Q18', 'OPT4', 3),
('ANScGlABP', 'MSwcZn3F', 'Q13', 'OPT2', 1),
('ANSCGLF39', 'MSSbGTht', 'Q4', 'OPT2', 1),
('ANSCgm5xK', 'MSGNt66z', 'Q25', 'OPT5', 0),
('ANSCgo9Uq', 'MShEdYT5', 'Q8', 'OPT2', 1),
('ANScGVift', 'MSGTyAts', 'Q14', 'OPT1', 0),
('ANSciuXfG', 'MSj941zD', 'Q2', 'OPT2', 1),
('ANSCJy8bk', 'MS2GS0M4', 'Q4', 'OPT2', 1),
('ANScKxMyC', 'MSWCMBqh', 'Q17', 'OPT1', 0),
('ANSCLQzzI', 'MSOTfXXF', 'Q10', 'OPT1', 0),
('ANScluP1Z', 'MSwcZn3F', 'Q22', 'OPT6', 1),
('ANSCN8qWv', 'MSwzvNE9', 'Q3', 'OPT3', 2),
('ANScngT80', 'MST7153t', 'Q12', 'OPT2', 1),
('ANScnKto4', 'MSSbGTht', 'Q8', 'OPT2', 1),
('ANScoOitj', 'MSbHrzIU', 'Q23', 'OPT5', 0),
('ANSCoRRIc', 'MS8DLldu', 'Q24', 'OPT7', 2),
('ANScOTPTE', 'MS8DLldu', 'Q23', 'OPT7', 2),
('ANSCOXlKh', 'MS138aYF', 'Q21', 'OPT4', 3),
('ANSCPEjIy', 'MSHVECsr', 'Q19', 'OPT1', 0),
('ANSCpGDqG', 'MS6zWUZX', 'Q17', 'OPT4', 3),
('ANSCpiRC2', 'MSbHrzIU', 'Q14', 'OPT1', 0),
('ANSCpm7KM', 'MSwTXozI', 'Q15', 'OPT1', 0),
('ANSCpvluS', 'MSWCMBqh', 'Q20', 'OPT3', 2),
('ANScqE3vx', 'MS8Ku3zf', 'Q3', 'OPT4', 3),
('ANSCQk4Q0', 'MSEwAQL0', 'Q1', 'OPT2', 1),
('ANScqXoYR', 'MSOiRHyL', 'Q11', 'OPT2', 1),
('ANSCS7yVL', 'MS2S9Sh8', 'Q14', 'OPT2', 1),
('ANScsNLnb', 'MS53AquJ', 'Q2', 'OPT2', 1),
('ANSCtojE4', 'MS138aYF', 'Q12', 'OPT1', 0),
('ANSCToKCt', 'MS8DLldu', 'Q8', 'OPT1', 0),
('ANSctVPye', 'MSrJv6lc', 'Q11', 'OPT2', 1),
('ANSCtZzzH', 'MSD2CdU4', 'Q8', 'OPT2', 1),
('ANSCUa1dW', 'MSGTyAts', 'Q23', 'OPT5', 0),
('ANScUjZzk', 'MSrql4yA', 'Q5', 'OPT4', 3),
('ANScurRYD', 'MShKvqCr', 'Q12', 'OPT1', 0),
('ANSCv7EFp', 'MS7uU47p', 'Q20', 'OPT1', 0),
('ANSCvckjG', 'MSC84et8', 'Q18', 'OPT3', 2),
('ANSCvQnO0', 'MSCvjNTp', 'Q4', 'OPT1', 0),
('ANSCWnDns', 'MS7f7XHX', 'Q2', 'OPT1', 0),
('ANScwtI3l', 'MS8Ku3zf', 'Q2', 'OPT3', 2),
('ANSCxGMyT', 'MSeqrFEz', 'Q19', 'OPT2', 1),
('ANScXhGyS', 'MSj941zD', 'Q3', 'OPT4', 3),
('ANSCyOP2K', 'MSuq7kIE', 'Q8', 'OPT2', 1),
('ANSCYRvWq', 'MSwTXozI', 'Q26', 'OPT5', 0),
('ANSCYZsKy', 'MS2wWZvm', 'Q13', 'OPT2', 1),
('ANSd1FTWr', 'MSLEeXDP', 'Q21', 'OPT2', 1),
('ANSD1vG0m', 'MShEdYT5', 'Q3', 'OPT2', 1),
('ANSD24yWK', 'MSFQYvYL', 'Q12', 'OPT2', 1),
('ANSd3G9In', 'MS7T2bhr', 'Q17', 'OPT3', 2),
('ANSD3nhYZ', 'MSxafMEG', 'Q11', 'OPT2', 1),
('ANSd4ZyFE', 'MSmCH78J', 'Q20', 'OPT1', 0),
('ANSd5bplU', 'MSGlWdke', 'Q19', 'OPT1', 0),
('ANSd5Hp73', 'MSSbGTht', 'Q7', 'OPT2', 1),
('ANSD5ucWs', 'MSbHrzIU', 'Q2', 'OPT2', 1),
('ANSd6HZNW', 'MSrzIxEi', 'Q13', 'OPT3', 2),
('ANSd6KV5K', 'MS6zWUZX', 'Q25', 'OPT6', 1),
('ANSd6PgCz', 'MSEDOwl4', 'Q23', 'OPT6', 1),
('ANSd8lK8w', 'MS8Ku3zf', 'Q4', 'OPT2', 1),
('ANSD8uwvK', 'MSHVECsr', 'Q2', 'OPT1', 0),
('ANSD8vBiT', 'MSb1vGgr', 'Q7', 'OPT2', 1),
('ANSd9QsoX', 'MSuq7kIE', 'Q2', 'OPT1', 0),
('ANSD9SIN7', 'MSEDOwl4', 'Q17', 'OPT1', 0),
('ANSDAkFzJ', 'MS7T2bhr', 'Q16', 'OPT1', 0),
('ANSDamjpr', 'MS5oYfaH', 'Q17', 'OPT1', 0),
('ANSdAoW3E', 'MSwcZn3F', 'Q17', 'OPT1', 0),
('ANSDaYg2k', 'MSGNt66z', 'Q22', 'OPT5', 0),
('ANSdb1Sag', 'MS2S9Sh8', 'Q7', 'OPT1', 0),
('ANSDB363V', 'MSFQYvYL', 'Q22', 'OPT5', 0),
('ANSDB8xcU', 'MSLEeXDP', 'Q12', 'OPT2', 1),
('ANSdcacua', 'MS1kbVC6', 'Q26', 'OPT5', 0),
('ANSDcHTC0', 'MSFQYvYL', 'Q14', 'OPT2', 1),
('ANSdcMyYQ', 'MS5xzf68', 'Q8', 'OPT1', 0),
('ANSDCOsqG', 'MSKBVoiE', 'Q14', 'OPT2', 1),
('ANSDcuCdC', 'MSMSWRrW', 'Q23', 'OPT5', 0),
('ANSddoz5D', 'MSaqqKXP', 'Q1', 'OPT4', 3),
('ANSdeLa52', 'MS7T2bhr', 'Q11', 'OPT3', 2),
('ANSDeVdXu', 'MSKBVoiE', 'Q8', 'OPT1', 0),
('ANSdFKSEZ', 'MSojDjTA', 'Q1', 'OPT1', 0),
('ANSdfx0EH', 'MS53AquJ', 'Q20', 'OPT3', 2),
('ANSdg53zX', 'MSwcZn3F', 'Q12', 'OPT2', 1),
('ANSdgdQR3', 'MSOiRHyL', 'Q13', 'OPT2', 1),
('ANSDGfCdq', 'MSEDOwl4', 'Q13', 'OPT4', 3),
('ANSDhq7BS', 'MSCcWA57', 'Q19', 'OPT1', 0),
('ANSdHs9wf', 'MSB9hlkR', 'Q2', 'OPT1', 0),
('ANSDhwhZT', 'MS5oYfaH', 'Q18', 'OPT3', 2),
('ANSDIsVWm', 'MSrzIxEi', 'Q6', 'OPT3', 2),
('ANSDj01IP', 'MSbHrzIU', 'Q25', 'OPT7', 2),
('ANSdj1CpP', 'MSCcWA57', 'Q25', 'OPT5', 0),
('ANSDjaQgE', 'MS2S9Sh8', 'Q12', 'OPT4', 3),
('ANSDjJJ19', 'MS7f7XHX', 'Q25', 'OPT7', 2),
('ANSDjZ4wE', 'MSuq7kIE', 'Q20', 'OPT1', 0),
('ANSDKikAV', 'MSUJDtjy', 'Q1', 'OPT2', 1),
('ANSDKs0IW', 'MSMSWRrW', 'Q16', 'OPT1', 0),
('ANSDlz1Kn', 'MSSbGTht', 'Q1', 'OPT1', 0),
('ANSDLZt1s', 'MSuq7kIE', 'Q19', 'OPT1', 0),
('ANSDnjB1w', 'MS2S9Sh8', 'Q24', 'OPT6', 1),
('ANSDNQiRm', 'MSJCXCcq', 'Q9', 'OPT1', 0),
('ANSdNqtf1', 'MSjSGqfF', 'Q5', 'OPT4', 3),
('ANSDOHFgs', 'MSKrk0hq', 'Q8', 'OPT2', 1),
('ANSDpdMV9', 'MS8DLldu', 'Q2', 'OPT1', 0),
('ANSdPfzAi', 'MSYs8YjY', 'Q17', 'OPT2', 1),
('ANSdphgnm', 'MShEdYT5', 'Q14', 'OPT3', 2),
('ANSDpyXtZ', 'MSrzIxEi', 'Q1', 'OPT3', 2),
('ANSDQTRrS', 'MSeqrFEz', 'Q9', 'OPT1', 0),
('ANSdQUNma', 'MSUJDtjy', 'Q22', 'OPT6', 1),
('ANSdQzzsf', 'MS138aYF', 'Q11', 'OPT4', 3),
('ANSDsX1QC', 'MSKBVoiE', 'Q4', 'OPT1', 0),
('ANSdtfr85', 'MSCJQ7km', 'Q13', 'OPT3', 2),
('ANSdtZ980', 'MSX9aTK5', 'Q18', 'OPT2', 1),
('ANSDVaE0L', 'MSMSWRrW', 'Q19', 'OPT2', 1),
('ANSdVVW3h', 'MSJCXCcq', 'Q10', 'OPT1', 0),
('ANSdxbvWm', 'MSb1vGgr', 'Q4', 'OPT4', 3),
('ANSdXEd8C', 'MSe8IKuy', 'Q8', 'OPT1', 0),
('ANSdxJAo3', 'MSD2CdU4', 'Q13', 'OPT2', 1),
('ANSdxZEEy', 'MSLEeXDP', 'Q23', 'OPT5', 0),
('ANSdy19Gh', 'MSCcWA57', 'Q14', 'OPT1', 0),
('ANSdyJnaL', 'MShC7NUw', 'Q8', 'OPT3', 2),
('ANSDyJxkj', 'MSxafMEG', 'Q18', 'OPT2', 1),
('ANSDYxISX', 'MSxafMEG', 'Q25', 'OPT5', 0),
('ANSDzCShJ', 'MSWzJzOu', 'Q15', 'OPT1', 0),
('ANSDzFg7c', 'MSZhKZEA', 'Q18', 'OPT3', 2),
('ANSDZlVrg', 'MShC7NUw', 'Q14', 'OPT4', 3),
('ANSdzuMWV', 'MSZhKZEA', 'Q26', 'OPT5', 0),
('ANSDZww68', 'MS2S9Sh8', 'Q5', 'OPT4', 3),
('ANSe0sl3t', 'MS6Hgbj4', 'Q20', 'OPT1', 0),
('ANSE3kX80', 'MSH2zQ4z', 'Q12', 'OPT2', 1),
('ANSE5Z3ZL', 'MSMSWRrW', 'Q4', 'OPT1', 0),
('ANSE7jqCr', 'MShEdYT5', 'Q23', 'OPT6', 1),
('ANSE7nvlk', 'MSwcZn3F', 'Q10', 'OPT1', 0),
('ANSE7T579', 'MSMSWRrW', 'Q26', 'OPT6', 1),
('ANSe7tOzh', 'MSS6eh4N', 'Q14', 'OPT4', 3),
('ANSE8qF5a', 'MS138aYF', 'Q8', 'OPT4', 3),
('ANSE8sgNc', 'MSj5xUgK', 'Q12', 'OPT2', 1),
('ANSe91uSi', 'MSbHrzIU', 'Q8', 'OPT2', 1),
('ANSeaEdqE', 'MS53AquJ', 'Q6', 'OPT3', 2),
('ANSEahbkZ', 'MSUVdR45', 'Q17', 'OPT2', 1),
('ANSeaMpba', 'MS5xzf68', 'Q21', 'OPT1', 0),
('ANSEbEscD', 'MSrzIxEi', 'Q17', 'OPT3', 2),
('ANSEBF1d4', 'MSuq7kIE', 'Q11', 'OPT3', 2),
('ANSebJVcA', 'MSLEeXDP', 'Q17', 'OPT2', 1),
('ANSEBpj1X', 'MSCcWA57', 'Q12', 'OPT1', 0),
('ANSEcjsiy', 'MSHVECsr', 'Q10', 'OPT4', 3),
('ANSEcUFU7', 'MS7uU47p', 'Q12', 'OPT1', 0),
('ANSEDGSOs', 'MSuM6ldN', 'Q21', 'OPT2', 1),
('ANSedYUgs', 'MSb1vGgr', 'Q11', 'OPT4', 3),
('ANSEe4IEZ', 'MSwcZn3F', 'Q8', 'OPT2', 1),
('ANSeE90zE', 'MShEdYT5', 'Q10', 'OPT1', 0),
('ANSEeSwQp', 'MSjIxFsI', 'Q24', 'OPT6', 1),
('ANSeev3s4', 'MSwTXozI', 'Q7', 'OPT1', 0),
('ANSefaSEG', 'MSpuxB2A', 'Q18', 'OPT2', 1),
('ANSEFZfXY', 'MSFQYvYL', 'Q23', 'OPT6', 1),
('ANSEGzJ6O', 'MSxAFfYH', 'Q13', 'OPT1', 0),
('ANSEH1aEM', 'MSeqrFEz', 'Q11', 'OPT1', 0),
('ANSEHdcys', 'MShKvqCr', 'Q17', 'OPT1', 0),
('ANSEIBDWx', 'MSMSWRrW', 'Q7', 'OPT1', 0),
('ANSeIQMvT', 'MStavaQ6', 'Q25', 'OPT5', 0),
('ANSEiVxDA', 'MSHVECsr', 'Q22', 'OPT7', 2),
('ANSeK8smk', 'MS7uU47p', 'Q11', 'OPT1', 0),
('ANSelaMAB', 'MShC7NUw', 'Q3', 'OPT3', 2),
('ANSeLC4z5', 'MSCJQ7km', 'Q14', 'OPT3', 2),
('ANSem1O4d', 'MSLEeXDP', 'Q3', 'OPT1', 0),
('ANSemJEnK', 'MSmCH78J', 'Q7', 'OPT1', 0),
('ANSEmpekO', 'MSqHuFsF', 'Q19', 'OPT4', 3),
('ANSENe2rh', 'MSUJDtjy', 'Q20', 'OPT4', 3),
('ANSEOEesr', 'MS5oYfaH', 'Q3', 'OPT4', 3),
('ANSeogouS', 'MSb1vGgr', 'Q16', 'OPT4', 3),
('ANSePG9uA', 'MSrJv6lc', 'Q21', 'OPT1', 0),
('ANSePiqJe', 'MSrsK5M0', 'Q12', 'OPT2', 1),
('ANSEQ4iJX', 'MShKvqCr', 'Q3', 'OPT1', 0),
('ANSEqNVal', 'MS79deyJ', 'Q15', 'OPT2', 1),
('ANSErHdRp', 'MSMSWRrW', 'Q11', 'OPT2', 1),
('ANSeRmwSe', 'MShC7NUw', 'Q16', 'OPT2', 1),
('ANSERYFDW', 'MSFQYvYL', 'Q15', 'OPT1', 0),
('ANSeS5qyt', 'MST7153t', 'Q21', 'OPT2', 1),
('ANSESmUuo', 'MSrJv6lc', 'Q8', 'OPT2', 1),
('ANSeT3tPE', 'MSC84et8', 'Q7', 'OPT2', 1),
('ANSeuD05S', 'MSSbGTht', 'Q11', 'OPT2', 1),
('ANSEugA5U', 'MSMSWRrW', 'Q5', 'OPT3', 2),
('ANSEuXDVi', 'MSkUYFSU', 'Q24', 'OPT5', 0),
('ANSEUzBfY', 'MS2E6rGI', 'Q16', 'OPT2', 1),
('ANSeVaBkW', 'MSn52ZdO', 'Q15', 'OPT1', 0),
('ANSEvcI5s', 'MSKrk0hq', 'Q13', 'OPT4', 3),
('ANSEvdnsc', 'MS1kbVC6', 'Q17', 'OPT2', 1),
('ANSeVHAJX', 'MS7T2bhr', 'Q5', 'OPT3', 2),
('ANSEW27Jl', 'MSpuxB2A', 'Q21', 'OPT1', 0),
('ANSeWF38f', 'MS6zWUZX', 'Q5', 'OPT2', 1),
('ANSewznsO', 'MS2GS0M4', 'Q2', 'OPT1', 0),
('ANSeXASFK', 'MSjIxFsI', 'Q6', 'OPT2', 1),
('ANSeXfw9r', 'MSrzIxEi', 'Q2', 'OPT3', 2),
('ANSExl7B3', 'MSOiRHyL', 'Q25', 'OPT5', 0),
('ANSExy0Qq', 'MSbHrzIU', 'Q21', 'OPT1', 0),
('ANSEYC7nd', 'MSGTyAts', 'Q2', 'OPT1', 0),
('ANSEydAP7', 'MSn52ZdO', 'Q8', 'OPT4', 3),
('ANSeYL2jv', 'MS2GS0M4', 'Q15', 'OPT1', 0),
('ANSeyWbI9', 'MSGlWdke', 'Q22', 'OPT6', 1),
('ANSeYXL5X', 'MSYkD4Nr', 'Q8', 'OPT2', 1),
('ANSEz59xq', 'MSYkD4Nr', 'Q7', 'OPT2', 1),
('ANSezlE0x', 'MSmCH78J', 'Q24', 'OPT5', 0),
('ANSEZmpPw', 'MSYs8YjY', 'Q22', 'OPT5', 0),
('ANSf0Hndp', 'MSj5xUgK', 'Q8', 'OPT2', 1),
('ANSf2xpr9', 'MSj941zD', 'Q25', 'OPT6', 1),
('ANSF3ZlaA', 'MSX9aTK5', 'Q14', 'OPT1', 0),
('ANSf557Th', 'MS79deyJ', 'Q18', 'OPT3', 2),
('ANSF6p6Cg', 'MSFQYvYL', 'Q4', 'OPT1', 0),
('ANSf8jfts', 'MSMzDgKm', 'Q6', 'OPT3', 2),
('ANSF8mTPm', 'MSEwAQL0', 'Q16', 'OPT2', 1),
('ANSFAFxYS', 'MS7f7XHX', 'Q8', 'OPT1', 0),
('ANSFbL6vo', 'MSCJQ7km', 'Q9', 'OPT4', 3),
('ANSfccSP6', 'MS5oYfaH', 'Q6', 'OPT3', 2),
('ANSfcGU7o', 'MSj5xUgK', 'Q2', 'OPT1', 0),
('ANSfclRDo', 'MShKvqCr', 'Q2', 'OPT1', 0),
('ANSFcT2bE', 'MSn52ZdO', 'Q1', 'OPT1', 0),
('ANSFcUHsd', 'MSUVdR45', 'Q2', 'OPT4', 3),
('ANSfcZgKs', 'MSGTyAts', 'Q17', 'OPT3', 2),
('ANSfD5pY8', 'MS79deyJ', 'Q1', 'OPT2', 1),
('ANSFdfe2w', 'MST7153t', 'Q16', 'OPT2', 1),
('ANSFDKTmI', 'MSJCXCcq', 'Q17', 'OPT1', 0),
('ANSfEiSgS', 'MSpuxB2A', 'Q23', 'OPT5', 0),
('ANSFEiYf7', 'MSe8IKuy', 'Q10', 'OPT1', 0),
('ANSferR4p', 'MSWCMBqh', 'Q16', 'OPT2', 1),
('ANSFEUf4q', 'MS2E6rGI', 'Q15', 'OPT4', 3),
('ANSfFQ7Nq', 'MSGTyAts', 'Q3', 'OPT1', 0),
('ANSFfRkJi', 'MS2E6rGI', 'Q2', 'OPT3', 2),
('ANSffWAFE', 'MS7T2bhr', 'Q25', 'OPT5', 0),
('ANSffXDbl', 'MSEDOwl4', 'Q18', 'OPT2', 1),
('ANSfg2kRr', 'MSC84et8', 'Q23', 'OPT7', 2),
('ANSFgCfoo', 'MSeqrFEz', 'Q6', 'OPT2', 1),
('ANSFgj90N', 'MSCcWA57', 'Q5', 'OPT1', 0),
('ANSFGOieN', 'MS5aBjQf', 'Q21', 'OPT2', 1),
('ANSfhxBV0', 'MStavaQ6', 'Q24', 'OPT5', 0),
('ANSFIo6m4', 'MSB9hlkR', 'Q10', 'OPT1', 0),
('ANSfJKZSW', 'MSMSWRrW', 'Q13', 'OPT2', 1),
('ANSFKX30I', 'MSAlECsL', 'Q8', 'OPT2', 1),
('ANSFkYuDI', 'MS2E6rGI', 'Q23', 'OPT6', 1),
('ANSFli7GI', 'MSwTXozI', 'Q10', 'OPT1', 0),
('ANSFmv0Zu', 'MSSbGTht', 'Q5', 'OPT4', 3),
('ANSFMW7C5', 'MSwcZn3F', 'Q14', 'OPT2', 1),
('ANSFNvlXB', 'MS6Hgbj4', 'Q1', 'OPT2', 1),
('ANSfOeCN2', 'MSEwAQL0', 'Q11', 'OPT3', 2),
('ANSfOkKDB', 'MSj5xUgK', 'Q25', 'OPT7', 2),
('ANSfOSGlY', 'MS2wWZvm', 'Q7', 'OPT1', 0),
('ANSfpBuiG', 'MSGlWdke', 'Q4', 'OPT2', 1),
('ANSfpJHjG', 'MSOiRHyL', 'Q17', 'OPT1', 0),
('ANSfPlGq3', 'MShivwr1', 'Q20', 'OPT1', 0),
('ANSfqCSvh', 'MSAlECsL', 'Q21', 'OPT1', 0),
('ANSfqHOCi', 'MSOTfXXF', 'Q12', 'OPT2', 1),
('ANSfqjWIP', 'MShEdYT5', 'Q13', 'OPT3', 2),
('ANSfranuE', 'MSUJDtjy', 'Q13', 'OPT4', 3),
('ANSFrgb5G', 'MS5oYfaH', 'Q16', 'OPT1', 0),
('ANSfRI66v', 'MSmCH78J', 'Q18', 'OPT2', 1),
('ANSfrvz93', 'MS7T2bhr', 'Q20', 'OPT3', 2),
('ANSFsrBuz', 'MSj5xUgK', 'Q10', 'OPT2', 1),
('ANSFSrKK0', 'MSQq566a', 'Q7', 'OPT1', 0),
('ANSFt430T', 'MSAlECsL', 'Q11', 'OPT1', 0),
('ANSfTOHIJ', 'MSYZvsiu', 'Q7', 'OPT1', 0),
('ANSfui1FB', 'MS8DLldu', 'Q22', 'OPT6', 1),
('ANSfVQvnP', 'MSrzIxEi', 'Q24', 'OPT6', 1),
('ANSfVY5wW', 'MSKrk0hq', 'Q17', 'OPT2', 1),
('ANSfwG2Ad', 'MSQq566a', 'Q1', 'OPT2', 1),
('ANSFwGaiv', 'MSYs8YjY', 'Q10', 'OPT1', 0),
('ANSFwvI7k', 'MS2wWZvm', 'Q10', 'OPT1', 0),
('ANSFXad9n', 'MShEdYT5', 'Q5', 'OPT2', 1),
('ANSfXwidn', 'MSeqrFEz', 'Q2', 'OPT3', 2),
('ANSfyNysB', 'MSSbGTht', 'Q19', 'OPT1', 0),
('ANSFysxGw', 'MShEdYT5', 'Q18', 'OPT3', 2),
('ANSFYTaNH', 'MSWCMBqh', 'Q11', 'OPT1', 0),
('ANSfyysRQ', 'MSJCXCcq', 'Q6', 'OPT2', 1),
('ANSfZ2wWs', 'MS5xzf68', 'Q25', 'OPT5', 0),
('ANSfZ8g6d', 'MSCJQ7km', 'Q8', 'OPT4', 3),
('ANSfzI9Pr', 'MSeqrFEz', 'Q7', 'OPT2', 1),
('ANSfZimu1', 'MSbHrzIU', 'Q12', 'OPT2', 1),
('ANSfZWyNH', 'MSZhKZEA', 'Q13', 'OPT4', 3),
('ANSg0tr2Q', 'MSxAFfYH', 'Q4', 'OPT1', 0),
('ANSG1KyF8', 'MS8Ku3zf', 'Q18', 'OPT3', 2),
('ANSg3eAlX', 'MSX9aTK5', 'Q5', 'OPT4', 3),
('ANSG3O3qa', 'MSAlECsL', 'Q9', 'OPT2', 1),
('ANSG42tzM', 'MSUY57sP', 'Q24', 'OPT5', 0),
('ANSg5ANkw', 'MSEwAQL0', 'Q3', 'OPT3', 2),
('ANSG5faF5', 'MSaqqKXP', 'Q2', 'OPT4', 3),
('ANSg5p00X', 'MSKBVoiE', 'Q17', 'OPT1', 0),
('ANSg8elVR', 'MS6zWUZX', 'Q4', 'OPT1', 0),
('ANSg8Pl4E', 'MS6zWUZX', 'Q23', 'OPT5', 0),
('ANSG9uRqK', 'MSrzIxEi', 'Q15', 'OPT3', 2),
('ANSgaXfH1', 'MShC7NUw', 'Q24', 'OPT6', 1),
('ANSGb2LMt', 'MSeqrFEz', 'Q26', 'OPT5', 0),
('ANSgBmktE', 'MSb1vGgr', 'Q25', 'OPT6', 1),
('ANSgbUuDB', 'MSn52ZdO', 'Q18', 'OPT4', 3),
('ANSGCsHCS', 'MSQq566a', 'Q17', 'OPT2', 1),
('ANSgcsodb', 'MSn52ZdO', 'Q4', 'OPT2', 1),
('ANSgCTenD', 'MSH2zQ4z', 'Q21', 'OPT1', 0),
('ANSGd0SDN', 'MSLEeXDP', 'Q24', 'OPT5', 0),
('ANSGdbnU3', 'MSbHrzIU', 'Q18', 'OPT2', 1),
('ANSGEeHfX', 'MSuM6ldN', 'Q3', 'OPT3', 2),
('ANSGepeMj', 'MSEwAQL0', 'Q12', 'OPT4', 3),
('ANSGErpc8', 'MS53AquJ', 'Q25', 'OPT6', 1),
('ANSgeV9Oo', 'MS79deyJ', 'Q24', 'OPT6', 1),
('ANSGF1N57', 'MSX9aTK5', 'Q3', 'OPT2', 1),
('ANSggL7lZ', 'MSMSWRrW', 'Q14', 'OPT1', 0),
('ANSgGqwzL', 'MSrzIxEi', 'Q11', 'OPT3', 2),
('ANSgh24bh', 'MSxafMEG', 'Q20', 'OPT1', 0),
('ANSgHEKOZ', 'MSEwAQL0', 'Q9', 'OPT2', 1),
('ANSGhm7R0', 'MSCvjNTp', 'Q1', 'OPT2', 1),
('ANSgHXiwO', 'MSKBVoiE', 'Q9', 'OPT1', 0),
('ANSGi4ONM', 'MSC84et8', 'Q10', 'OPT1', 0),
('ANSgIhEj1', 'MS8Ku3zf', 'Q25', 'OPT6', 1),
('ANSGJ6pDu', 'MSCJQ7km', 'Q22', 'OPT7', 2),
('ANSgjjSUT', 'MSeqrFEz', 'Q18', 'OPT2', 1),
('ANSGjLTNL', 'MSrql4yA', 'Q10', 'OPT4', 3),
('ANSGjr2XN', 'MSYs8YjY', 'Q9', 'OPT4', 3),
('ANSGkkcvC', 'MSCcWA57', 'Q6', 'OPT1', 0),
('ANSgLbNO1', 'MSSbGTht', 'Q18', 'OPT2', 1),
('ANSglJmZk', 'MSH2zQ4z', 'Q19', 'OPT1', 0),
('ANSgm058S', 'MS2GS0M4', 'Q17', 'OPT1', 0),
('ANSgMC70I', 'MSjSGqfF', 'Q26', 'OPT7', 2),
('ANSgmiKZb', 'MShivwr1', 'Q10', 'OPT1', 0),
('ANSGMnVWs', 'MSGTyAts', 'Q11', 'OPT1', 0),
('ANSGmv9jA', 'MST7153t', 'Q10', 'OPT1', 0),
('ANSgNYNZo', 'MSeqrFEz', 'Q22', 'OPT5', 0),
('ANSGnZ7a1', 'MSS6eh4N', 'Q22', 'OPT7', 2),
('ANSGNzMdC', 'MSXxuKfh', 'Q22', 'OPT5', 0),
('ANSGOlyBh', 'MStavaQ6', 'Q8', 'OPT1', 0),
('ANSGOzqlw', 'MS7T2bhr', 'Q3', 'OPT4', 3),
('ANSGp8Igy', 'MSCcWA57', 'Q1', 'OPT1', 0),
('ANSGPQRiH', 'MSn52ZdO', 'Q3', 'OPT4', 3),
('ANSGQxDCQ', 'MSpuxB2A', 'Q5', 'OPT2', 1),
('ANSgrEU9b', 'MSLEeXDP', 'Q14', 'OPT2', 1),
('ANSgsYjqV', 'MS5aBjQf', 'Q4', 'OPT3', 2),
('ANSGSZLpq', 'MS5xzf68', 'Q13', 'OPT1', 0),
('ANSGt1B9f', 'MSuq7kIE', 'Q7', 'OPT2', 1),
('ANSGtNo7K', 'MSxafMEG', 'Q21', 'OPT1', 0),
('ANSGTXftu', 'MShivwr1', 'Q16', 'OPT3', 2),
('ANSgu3XpW', 'MShivwr1', 'Q1', 'OPT2', 1),
('ANSGuFPgN', 'MSS6eh4N', 'Q21', 'OPT2', 1),
('ANSgUJbyN', 'MSCJQ7km', 'Q24', 'OPT6', 1),
('ANSguM5ES', 'MSrql4yA', 'Q4', 'OPT4', 3),
('ANSGuUmYU', 'MSqHuFsF', 'Q17', 'OPT4', 3),
('ANSGUXBWd', 'MSYkD4Nr', 'Q1', 'OPT2', 1),
('ANSGV7uCp', 'MSjSGqfF', 'Q24', 'OPT6', 1),
('ANSGvBwAc', 'MSxafMEG', 'Q7', 'OPT1', 0),
('ANSGVhgZr', 'MSAlECsL', 'Q1', 'OPT2', 1),
('ANSgvSTha', 'MS6Hgbj4', 'Q8', 'OPT2', 1),
('ANSGW9IOF', 'MS6Hgbj4', 'Q15', 'OPT1', 0),
('ANSGWrYH8', 'MSZhKZEA', 'Q21', 'OPT4', 3),
('ANSGxeeaD', 'MSHVECsr', 'Q17', 'OPT1', 0),
('ANSgxn2aJ', 'MS5aBjQf', 'Q18', 'OPT2', 1),
('ANSgxszg1', 'MSuq7kIE', 'Q15', 'OPT1', 0),
('ANSgYeZc0', 'MSYZvsiu', 'Q6', 'OPT2', 1),
('ANSGyfL45', 'MS7f7XHX', 'Q4', 'OPT2', 1),
('ANSGYFP5f', 'MSrzIxEi', 'Q16', 'OPT3', 2),
('ANSGySmsz', 'MS7uU47p', 'Q23', 'OPT5', 0),
('ANSGZ1BS5', 'MSD2CdU4', 'Q25', 'OPT6', 1),
('ANSgZA64V', 'MST7153t', 'Q9', 'OPT1', 0),
('ANSGzksGc', 'MSCcWA57', 'Q16', 'OPT1', 0),
('ANSgZN2OI', 'MSkUYFSU', 'Q21', 'OPT1', 0),
('ANSGZUd95', 'MSCJQ7km', 'Q10', 'OPT3', 2),
('ANSH0FYsv', 'MSX9aTK5', 'Q2', 'OPT3', 2),
('ANSH11boa', 'MSCJQ7km', 'Q11', 'OPT3', 2),
('ANSH1jgKL', 'MS6Hgbj4', 'Q22', 'OPT6', 1),
('ANSh1roeF', 'MSj5xUgK', 'Q18', 'OPT1', 0),
('ANSH1tYX9', 'MSQq566a', 'Q3', 'OPT4', 3),
('ANSh2nCaD', 'MShEdYT5', 'Q24', 'OPT5', 0),
('ANSh35umy', 'MSGNt66z', 'Q11', 'OPT1', 0),
('ANSh4MEPR', 'MSYkD4Nr', 'Q20', 'OPT1', 0),
('ANSh5an7G', 'MS2wWZvm', 'Q3', 'OPT1', 0),
('ANSH7kK7K', 'MShC7NUw', 'Q23', 'OPT6', 1),
('ANSH9E10A', 'MSXxuKfh', 'Q9', 'OPT2', 1),
('ANSHag8da', 'MSAlECsL', 'Q15', 'OPT3', 2),
('ANSHaNJSt', 'MSrzIxEi', 'Q26', 'OPT6', 1),
('ANShAqItR', 'MS2GS0M4', 'Q23', 'OPT5', 0),
('ANSharU2V', 'MSEDOwl4', 'Q12', 'OPT3', 2),
('ANShaZo8w', 'MSQq566a', 'Q12', 'OPT4', 3),
('ANSHb6DAl', 'MSQq566a', 'Q19', 'OPT2', 1),
('ANSHBkVl7', 'MSWCMBqh', 'Q1', 'OPT2', 1),
('ANSHbOGAg', 'MS2GS0M4', 'Q5', 'OPT2', 1),
('ANShBrval', 'MSAlECsL', 'Q6', 'OPT2', 1),
('ANShc20Kn', 'MSFQYvYL', 'Q18', 'OPT1', 0),
('ANShcEjyo', 'MSYkD4Nr', 'Q6', 'OPT3', 2),
('ANSHCx3w6', 'MSbHrzIU', 'Q19', 'OPT2', 1),
('ANSHDaq3T', 'MSWzJzOu', 'Q23', 'OPT5', 0),
('ANShdp6tO', 'MSGlWdke', 'Q16', 'OPT1', 0),
('ANShdvBXZ', 'MSMSWRrW', 'Q12', 'OPT3', 2),
('ANSHe9gGZ', 'MS7uU47p', 'Q6', 'OPT1', 0),
('ANSHECez7', 'MS7uU47p', 'Q17', 'OPT1', 0),
('ANShEiq4f', 'MSb1vGgr', 'Q24', 'OPT6', 1),
('ANShem8x0', 'MS5aBjQf', 'Q20', 'OPT3', 2),
('ANShfB33x', 'MSUJDtjy', 'Q25', 'OPT6', 1),
('ANSHflSmD', 'MS5oYfaH', 'Q23', 'OPT5', 0),
('ANShfMuew', 'MSQq566a', 'Q2', 'OPT2', 1),
('ANShGFw30', 'MS2E6rGI', 'Q18', 'OPT3', 2),
('ANShhbcPm', 'MSOiRHyL', 'Q5', 'OPT2', 1),
('ANSHhBK1R', 'MS8Zetbr', 'Q6', 'OPT2', 1),
('ANSHhEoce', 'MSe8IKuy', 'Q15', 'OPT1', 0),
('ANSHHO260', 'MSuM6ldN', 'Q26', 'OPT5', 0),
('ANSHI5e2o', 'MSX9aTK5', 'Q19', 'OPT1', 0),
('ANShIjBSO', 'MSH2zQ4z', 'Q7', 'OPT2', 1),
('ANSHiMX3g', 'MSpuxB2A', 'Q9', 'OPT2', 1),
('ANShiQnz2', 'MSjIxFsI', 'Q2', 'OPT1', 0),
('ANSHIyuBc', 'MSWCMBqh', 'Q4', 'OPT2', 1),
('ANSHjhE9D', 'MSSbGTht', 'Q16', 'OPT1', 0),
('ANShK8aWo', 'MSGNt66z', 'Q8', 'OPT2', 1),
('ANShkAPJJ', 'MSAlECsL', 'Q2', 'OPT2', 1),
('ANSHKx6gC', 'MSeqrFEz', 'Q23', 'OPT5', 0),
('ANSHlp9py', 'MSH2zQ4z', 'Q8', 'OPT2', 1),
('ANShLVGfI', 'MS2E6rGI', 'Q24', 'OPT7', 2),
('ANSHMflFN', 'MSj941zD', 'Q17', 'OPT4', 3),
('ANShMjSZi', 'MSEDOwl4', 'Q14', 'OPT3', 2),
('ANSHmr3BO', 'MSEDOwl4', 'Q2', 'OPT2', 1),
('ANShmznZX', 'MS7T2bhr', 'Q24', 'OPT5', 0),
('ANSHnrG49', 'MShEdYT5', 'Q1', 'OPT2', 1),
('ANShOk9ol', 'MS2GS0M4', 'Q6', 'OPT2', 1),
('ANShOMZ7z', 'MSjIxFsI', 'Q7', 'OPT1', 0),
('ANShP93LN', 'MSYkD4Nr', 'Q25', 'OPT5', 0),
('ANShPDbmv', 'MSbHrzIU', 'Q16', 'OPT2', 1),
('ANShSAkiI', 'MS7T2bhr', 'Q22', 'OPT6', 1),
('ANSHSt6q5', 'MSD2CdU4', 'Q22', 'OPT6', 1),
('ANSHThdsE', 'MSYs8YjY', 'Q23', 'OPT6', 1),
('ANSHuAsAX', 'MShivwr1', 'Q14', 'OPT2', 1),
('ANShUbWkE', 'MSrql4yA', 'Q21', 'OPT1', 0),
('ANShVM6VB', 'MSKBVoiE', 'Q25', 'OPT5', 0),
('ANShvNG8N', 'MSxAFfYH', 'Q5', 'OPT1', 0),
('ANSHWP3jp', 'MStavaQ6', 'Q12', 'OPT2', 1),
('ANShx5xKX', 'MSrsK5M0', 'Q26', 'OPT5', 0),
('ANShxbPg5', 'MSwcZn3F', 'Q23', 'OPT5', 0),
('ANSHxEdJy', 'MSC84et8', 'Q24', 'OPT6', 1),
('ANShxrDFE', 'MSMzDgKm', 'Q10', 'OPT3', 2),
('ANShXuIlh', 'MSLEeXDP', 'Q16', 'OPT2', 1),
('ANSHXuyNI', 'MSLEeXDP', 'Q2', 'OPT1', 0),
('ANShYRxvu', 'MSC84et8', 'Q22', 'OPT6', 1),
('ANSHzopIi', 'MSB9hlkR', 'Q18', 'OPT1', 0),
('ANShzSTBr', 'MSmCH78J', 'Q3', 'OPT1', 0),
('ANSI03bRs', 'MSrql4yA', 'Q20', 'OPT4', 3),
('ANSI0bfZF', 'MSAlECsL', 'Q4', 'OPT1', 0),
('ANSI0bpPL', 'MSGlWdke', 'Q25', 'OPT7', 2),
('ANSi0oy5t', 'MSqHuFsF', 'Q23', 'OPT6', 1),
('ANSI2sd8M', 'MSwzvNE9', 'Q2', 'OPT2', 1),
('ANSi5mQ2x', 'MS2wWZvm', 'Q9', 'OPT1', 0),
('ANSI6lR1M', 'MSrzIxEi', 'Q4', 'OPT3', 2),
('ANSi7Ieob', 'MS7uU47p', 'Q19', 'OPT1', 0),
('ANSi7Zj05', 'MSkUYFSU', 'Q15', 'OPT2', 1),
('ANSiBp068', 'MST7153t', 'Q8', 'OPT2', 1),
('ANSICu0Xw', 'MSC84et8', 'Q16', 'OPT2', 1),
('ANSId0dvC', 'MS53AquJ', 'Q26', 'OPT7', 2),
('ANSiDZtMk', 'MSUVdR45', 'Q1', 'OPT2', 1),
('ANSIEkHXM', 'MSX9aTK5', 'Q4', 'OPT1', 0),
('ANSIfoCgp', 'MSB9hlkR', 'Q9', 'OPT1', 0),
('ANSiGJQwt', 'MShivwr1', 'Q9', 'OPT1', 0),
('ANSIgon9h', 'MS5oYfaH', 'Q12', 'OPT2', 1),
('ANSiGTLFa', 'MSH2zQ4z', 'Q18', 'OPT1', 0),
('ANSiHCZsA', 'MSKrk0hq', 'Q3', 'OPT4', 3),
('ANSiiK12v', 'MSB9hlkR', 'Q6', 'OPT1', 0),
('ANSijEsay', 'MSe8IKuy', 'Q3', 'OPT2', 1),
('ANSIjf40T', 'MSqHuFsF', 'Q11', 'OPT2', 1),
('ANSiJv4Nd', 'MS8Ku3zf', 'Q16', 'OPT3', 2),
('ANSIkA8Pn', 'MSmLaR9I', 'Q20', 'OPT1', 0),
('ANSiKHlmu', 'MSYZvsiu', 'Q2', 'OPT1', 0),
('ANSIKMcil', 'MSrsK5M0', 'Q20', 'OPT2', 1),
('ANSIkSmFg', 'MShivwr1', 'Q23', 'OPT6', 1),
('ANSil0p3g', 'MS7T2bhr', 'Q2', 'OPT1', 0),
('ANSiLFX07', 'MSxafMEG', 'Q13', 'OPT2', 1),
('ANSiLMbPk', 'MS7f7XHX', 'Q11', 'OPT3', 2),
('ANSIlnhij', 'MS2GS0M4', 'Q10', 'OPT1', 0),
('ANSIlpBu0', 'MS8DLldu', 'Q1', 'OPT1', 0),
('ANSiLvpaC', 'MSOiRHyL', 'Q3', 'OPT2', 1),
('ANSImHrEi', 'MShC7NUw', 'Q15', 'OPT4', 3),
('ANSimNhXz', 'MSOiRHyL', 'Q6', 'OPT4', 3),
('ANSiN6WyX', 'MS2wWZvm', 'Q26', 'OPT5', 0),
('ANSINSEQh', 'MSrJv6lc', 'Q3', 'OPT1', 0),
('ANSIo6jEi', 'MSmCH78J', 'Q26', 'OPT7', 2),
('ANSIOtVL3', 'MSkUYFSU', 'Q18', 'OPT1', 0),
('ANSioXJkM', 'MShC7NUw', 'Q6', 'OPT2', 1),
('ANSipEniF', 'MShKvqCr', 'Q11', 'OPT1', 0),
('ANSIpglyg', 'MS53AquJ', 'Q14', 'OPT1', 0),
('ANSipyAA7', 'MSGNt66z', 'Q20', 'OPT1', 0),
('ANSIqbIFu', 'MS7uU47p', 'Q7', 'OPT1', 0),
('ANSIqqghK', 'MShC7NUw', 'Q4', 'OPT3', 2),
('ANSiRXpG7', 'MSKBVoiE', 'Q20', 'OPT1', 0),
('ANSismw4l', 'MS2wWZvm', 'Q15', 'OPT1', 0),
('ANSistmQk', 'MS79deyJ', 'Q23', 'OPT5', 0),
('ANSIu9ap7', 'MS8Ku3zf', 'Q10', 'OPT4', 3),
('ANSIVlVqe', 'MSD2CdU4', 'Q19', 'OPT1', 0),
('ANSiwDrsb', 'MStavaQ6', 'Q20', 'OPT1', 0),
('ANSiWj3jH', 'MS5aBjQf', 'Q24', 'OPT6', 1),
('ANSiwnWYn', 'MSXxuKfh', 'Q11', 'OPT2', 1),
('ANSiwZwJ8', 'MSrql4yA', 'Q16', 'OPT4', 3),
('ANSIX1Inz', 'MST7153t', 'Q13', 'OPT2', 1),
('ANSixlAut', 'MSmCH78J', 'Q14', 'OPT1', 0),
('ANSixR4tp', 'MS7f7XHX', 'Q16', 'OPT1', 0),
('ANSIxtOnz', 'MShivwr1', 'Q3', 'OPT1', 0),
('ANSIY9FX5', 'MSQq566a', 'Q11', 'OPT4', 3),
('ANSiYDYvY', 'MSXxuKfh', 'Q3', 'OPT4', 3),
('ANSiYGRhx', 'MS138aYF', 'Q4', 'OPT4', 3),
('ANSIYHA4l', 'MSS6eh4N', 'Q13', 'OPT4', 3),
('ANSIyigp7', 'MS5oYfaH', 'Q11', 'OPT1', 0),
('ANSiyrzmx', 'MS2E6rGI', 'Q7', 'OPT3', 2),
('ANSiz4D4U', 'MS2E6rGI', 'Q4', 'OPT4', 3),
('ANSIz56dV', 'MSj941zD', 'Q24', 'OPT6', 1),
('ANSIZ5lue', 'MSKrk0hq', 'Q20', 'OPT4', 3),
('ANSiZbuZQ', 'MSrql4yA', 'Q2', 'OPT4', 3),
('ANSIZmKHE', 'MSqHuFsF', 'Q7', 'OPT4', 3),
('ANSj09H1J', 'MSjSGqfF', 'Q21', 'OPT3', 2),
('ANSj0dYTz', 'MSxAFfYH', 'Q17', 'OPT1', 0),
('ANSj2m2xI', 'MSn52ZdO', 'Q24', 'OPT5', 0),
('ANSJ2yoOl', 'MSkUYFSU', 'Q2', 'OPT2', 1),
('ANSJ2yXXm', 'MSWC6Gxp', 'Q1', 'OPT2', 1),
('ANSj5W03M', 'MSKrk0hq', 'Q6', 'OPT3', 2),
('ANSj6IIki', 'MSjIxFsI', 'Q14', 'OPT2', 1),
('ANSj6X77c', 'MSqHuFsF', 'Q3', 'OPT2', 1),
('ANSJ8ASwU', 'MSX9aTK5', 'Q20', 'OPT1', 0),
('ANSJ8ckF5', 'MS5oYfaH', 'Q24', 'OPT6', 1),
('ANSJACuin', 'MSrsK5M0', 'Q16', 'OPT1', 0),
('ANSjApr7o', 'MSH2zQ4z', 'Q6', 'OPT1', 0),
('ANSjbcEnB', 'MSEwAQL0', 'Q6', 'OPT4', 3),
('ANSJBE9pk', 'MSMSWRrW', 'Q25', 'OPT5', 0),
('ANSjbEKgX', 'MSe8IKuy', 'Q11', 'OPT2', 1),
('ANSjBf4b7', 'MSB9hlkR', 'Q5', 'OPT1', 0),
('ANSjBG4Bd', 'MSj941zD', 'Q6', 'OPT4', 3),
('ANSJbJw5a', 'MSMzDgKm', 'Q7', 'OPT1', 0),
('ANSjbspwV', 'MSEDOwl4', 'Q20', 'OPT1', 0),
('ANSJbw8Z4', 'MSHVECsr', 'Q3', 'OPT1', 0),
('ANSjBX48F', 'MSSbGTht', 'Q26', 'OPT6', 1),
('ANSJdACPs', 'MSYZvsiu', 'Q9', 'OPT2', 1),
('ANSJdCocf', 'MSWzJzOu', 'Q14', 'OPT1', 0),
('ANSjdEdkq', 'MSwzvNE9', 'Q8', 'OPT3', 2),
('ANSjDMb8e', 'MSn52ZdO', 'Q10', 'OPT2', 1),
('ANSJdwkMJ', 'MSGlWdke', 'Q14', 'OPT3', 2),
('ANSJeKfMF', 'MSUVdR45', 'Q5', 'OPT2', 1),
('ANSjf2pBR', 'MSGlWdke', 'Q7', 'OPT1', 0),
('ANSjF8qWq', 'MS2S9Sh8', 'Q8', 'OPT4', 3),
('ANSJfaXrd', 'MSCcWA57', 'Q8', 'OPT1', 0),
('ANSJGFhac', 'MSLEeXDP', 'Q26', 'OPT6', 1),
('ANSJgn8Z5', 'MSEwAQL0', 'Q21', 'OPT1', 0),
('ANSjHMImA', 'MSCJQ7km', 'Q18', 'OPT3', 2),
('ANSjhslux', 'MSHVECsr', 'Q20', 'OPT1', 0),
('ANSjJ9Qa2', 'MSWCMBqh', 'Q10', 'OPT2', 1),
('ANSjJbJ9q', 'MS7f7XHX', 'Q18', 'OPT2', 1),
('ANSJjsaKi', 'MSCvjNTp', 'Q2', 'OPT2', 1),
('ANSJjwyLW', 'MSGTyAts', 'Q4', 'OPT1', 0),
('ANSJk3oyF', 'MSGNt66z', 'Q15', 'OPT1', 0),
('ANSJKXEVF', 'MSWC6Gxp', 'Q2', 'OPT3', 2),
('ANSJMsCeK', 'MSn52ZdO', 'Q6', 'OPT2', 1),
('ANSjNrECc', 'MSwTXozI', 'Q12', 'OPT1', 0),
('ANSJnWq3Q', 'MSWCMBqh', 'Q23', 'OPT5', 0),
('ANSjNzNIQ', 'MSZhKZEA', 'Q16', 'OPT3', 2),
('ANSJO1bQq', 'MSrsK5M0', 'Q5', 'OPT2', 1),
('ANSJoAnOL', 'MSCcWA57', 'Q3', 'OPT1', 0),
('ANSJoDutt', 'MSwTXozI', 'Q24', 'OPT5', 0),
('ANSJomu2R', 'MSYs8YjY', 'Q5', 'OPT1', 0),
('ANSJOw3G2', 'MSD2CdU4', 'Q6', 'OPT2', 1),
('ANSJPi5EF', 'MSHVECsr', 'Q24', 'OPT5', 0),
('ANSjQEZlz', 'MShEdYT5', 'Q11', 'OPT2', 1),
('ANSjR7FH5', 'MSpuxB2A', 'Q8', 'OPT2', 1),
('ANSjREXbQ', 'MSSbGTht', 'Q17', 'OPT1', 0),
('ANSJRPzmc', 'MSFQYvYL', 'Q10', 'OPT1', 0),
('ANSJRQ02O', 'MSFQYvYL', 'Q25', 'OPT5', 0),
('ANSjsLeMd', 'MSGlWdke', 'Q15', 'OPT2', 1),
('ANSJT4edF', 'MS5oYfaH', 'Q13', 'OPT3', 2),
('ANSJTjOSY', 'MSjSGqfF', 'Q9', 'OPT2', 1),
('ANSjUOLJw', 'MSJCXCcq', 'Q16', 'OPT1', 0),
('ANSJvo2tk', 'MSuM6ldN', 'Q14', 'OPT4', 3),
('ANSjwc3j8', 'MSe8IKuy', 'Q22', 'OPT5', 0),
('ANSjWu48C', 'MSC84et8', 'Q12', 'OPT3', 2),
('ANSjX9LdV', 'MS7uU47p', 'Q8', 'OPT1', 0),
('ANSjxkF6i', 'MSSbGTht', 'Q21', 'OPT1', 0),
('ANSjy8uMs', 'MSUVdR45', 'Q21', 'OPT3', 2),
('ANSjYCnTL', 'MS8Ku3zf', 'Q9', 'OPT3', 2),
('ANSJyQMEe', 'MS7f7XHX', 'Q20', 'OPT1', 0),
('ANSjzCnTX', 'MSSbGTht', 'Q24', 'OPT5', 0),
('ANSjZLfYw', 'MSLEeXDP', 'Q20', 'OPT2', 1),
('ANSK2VOky', 'MSmLaR9I', 'Q4', 'OPT1', 0),
('ANSk4mvHH', 'MSpuxB2A', 'Q12', 'OPT1', 0),
('ANSk66WhD', 'MS2wWZvm', 'Q21', 'OPT1', 0),
('ANSk6fOYk', 'MSuM6ldN', 'Q10', 'OPT2', 1),
('ANSK6h77W', 'MSn52ZdO', 'Q23', 'OPT5', 0),
('ANSk72rFw', 'MS2E6rGI', 'Q26', 'OPT6', 1),
('ANSk74NBF', 'MSH2zQ4z', 'Q10', 'OPT2', 1),
('ANSk83ms2', 'MSJCXCcq', 'Q1', 'OPT1', 0),
('ANSk8GMVH', 'MShEdYT5', 'Q7', 'OPT1', 0),
('ANSK8JLo7', 'MSQUX6eO', 'Q1', 'OPT4', 3),
('ANSk9LUL6', 'MSwzvNE9', 'Q24', 'OPT6', 1),
('ANSKAYH1g', 'MSD2CdU4', 'Q4', 'OPT1', 0),
('ANSkb4NYi', 'MSCvjNTp', 'Q11', 'OPT2', 1),
('ANSKC77sz', 'MSUVdR45', 'Q18', 'OPT2', 1),
('ANSkDTl2G', 'MS5aBjQf', 'Q19', 'OPT2', 1),
('ANSKEcu4t', 'MS6Hgbj4', 'Q13', 'OPT1', 0),
('ANSKFGp8i', 'MSCJQ7km', 'Q5', 'OPT4', 3),
('ANSKgOrE2', 'MSC84et8', 'Q9', 'OPT2', 1),
('ANSkGpLZH', 'MSUY57sP', 'Q11', 'OPT2', 1),
('ANSkGxf8j', 'MSXxuKfh', 'Q8', 'OPT1', 0),
('ANSkh5jm5', 'MSYZvsiu', 'Q11', 'OPT2', 1),
('ANSkhJIeC', 'MSX9aTK5', 'Q22', 'OPT6', 1),
('ANSKIsoCw', 'MSj941zD', 'Q22', 'OPT7', 2),
('ANSkiuNK5', 'MShEdYT5', 'Q19', 'OPT2', 1),
('ANSkIWZ45', 'MSwzvNE9', 'Q11', 'OPT4', 3),
('ANSkJbq5s', 'MSuq7kIE', 'Q26', 'OPT5', 0),
('ANSKJf1uX', 'MSCcWA57', 'Q24', 'OPT5', 0),
('ANSkjZOlV', 'MSeqrFEz', 'Q21', 'OPT1', 0),
('ANSKKimqc', 'MS2GS0M4', 'Q26', 'OPT6', 1),
('ANSKKs3sR', 'MS79deyJ', 'Q10', 'OPT1', 0),
('ANSkKs45d', 'MSGTyAts', 'Q6', 'OPT2', 1),
('ANSkKub0I', 'MSMSWRrW', 'Q3', 'OPT1', 0),
('ANSKlkRJT', 'MSwTXozI', 'Q23', 'OPT5', 0),
('ANSKLkwmC', 'MSS6eh4N', 'Q9', 'OPT4', 3),
('ANSkM5zLl', 'MSxafMEG', 'Q1', 'OPT1', 0),
('ANSkMnHxz', 'MSZhKZEA', 'Q5', 'OPT4', 3),
('ANSkMWGhl', 'MSe8IKuy', 'Q14', 'OPT2', 1),
('ANSkmykWa', 'MSH2zQ4z', 'Q2', 'OPT2', 1),
('ANSKNGED5', 'MSS6eh4N', 'Q16', 'OPT2', 1),
('ANSKNgUim', 'MSqHuFsF', 'Q6', 'OPT2', 1),
('ANSKNjm0B', 'MSmCH78J', 'Q12', 'OPT2', 1),
('ANSKnnoOa', 'MSYs8YjY', 'Q24', 'OPT7', 2),
('ANSKosbhf', 'MS6Hgbj4', 'Q26', 'OPT6', 1),
('ANSkoT9lm', 'MSjSGqfF', 'Q13', 'OPT2', 1),
('ANSkP5NXL', 'MShKvqCr', 'Q6', 'OPT1', 0),
('ANSKpj4x2', 'MSD2CdU4', 'Q18', 'OPT2', 1),
('ANSkqW1O3', 'MSrsK5M0', 'Q10', 'OPT2', 1),
('ANSkQXpk8', 'MSwcZn3F', 'Q18', 'OPT1', 0),
('ANSKRmRAC', 'MSmCH78J', 'Q25', 'OPT6', 1),
('ANSKS95eO', 'MSCvjNTp', 'Q16', 'OPT1', 0),
('ANSks9zem', 'MSrsK5M0', 'Q22', 'OPT5', 0),
('ANSKT6xWI', 'MS2S9Sh8', 'Q13', 'OPT1', 0),
('ANSkTbx7p', 'MS5aBjQf', 'Q11', 'OPT2', 1),
('ANSkTjw90', 'MSrJv6lc', 'Q7', 'OPT1', 0),
('ANSKuLbWN', 'MSuM6ldN', 'Q12', 'OPT3', 2),
('ANSkulIDe', 'MS2E6rGI', 'Q20', 'OPT3', 2);
INSERT INTO `answers` (`id`, `user_id`, `q_id`, `answer`, `score`) VALUES
('ANSKUpdER', 'MSEDOwl4', 'Q10', 'OPT1', 0),
('ANSKUYrvD', 'MS138aYF', 'Q26', 'OPT5', 0),
('ANSKVdFZW', 'MSCJQ7km', 'Q12', 'OPT2', 1),
('ANSkVEmYg', 'MS7T2bhr', 'Q6', 'OPT4', 3),
('ANSKWFrNn', 'MS8Ku3zf', 'Q14', 'OPT2', 1),
('ANSkwhd0t', 'MS53AquJ', 'Q12', 'OPT3', 2),
('ANSKXAxZw', 'MSUJDtjy', 'Q17', 'OPT2', 1),
('ANSkXBqee', 'MSuq7kIE', 'Q14', 'OPT2', 1),
('ANSkXFYnZ', 'MSmLaR9I', 'Q16', 'OPT1', 0),
('ANSKXNZ89', 'MSC84et8', 'Q1', 'OPT2', 1),
('ANSkY8JOH', 'MSD2CdU4', 'Q10', 'OPT2', 1),
('ANSKZVdq3', 'MS6zWUZX', 'Q18', 'OPT2', 1),
('ANSL0F4cf', 'MS6Hgbj4', 'Q23', 'OPT7', 2),
('ANSl0r7Hh', 'MSjSGqfF', 'Q16', 'OPT1', 0),
('ANSl2Trms', 'MSrzIxEi', 'Q25', 'OPT6', 1),
('ANSL3givx', 'MS7T2bhr', 'Q13', 'OPT3', 2),
('ANSL4GLgx', 'MSYs8YjY', 'Q2', 'OPT4', 3),
('ANSl4lXHQ', 'MSQq566a', 'Q24', 'OPT5', 0),
('ANSl4ok3R', 'MSH2zQ4z', 'Q20', 'OPT1', 0),
('ANSL4sNUg', 'MSGlWdke', 'Q11', 'OPT4', 3),
('ANSL5Ab4v', 'MSGlWdke', 'Q3', 'OPT2', 1),
('ANSl5OMEp', 'MSeqrFEz', 'Q8', 'OPT2', 1),
('ANSL5Xgtb', 'MSUJDtjy', 'Q7', 'OPT4', 3),
('ANSL8DXny', 'MSjSGqfF', 'Q17', 'OPT3', 2),
('ANSL9plyt', 'MSGTyAts', 'Q12', 'OPT1', 0),
('ANSLap8Rm', 'MSUY57sP', 'Q18', 'OPT1', 0),
('ANSlbf1xe', 'MST7153t', 'Q15', 'OPT1', 0),
('ANSLBKlIh', 'MStavaQ6', 'Q10', 'OPT1', 0),
('ANSLCOFTa', 'MSGTyAts', 'Q25', 'OPT5', 0),
('ANSLdI2RL', 'MSUY57sP', 'Q13', 'OPT1', 0),
('ANSLdi5cu', 'MS6zWUZX', 'Q7', 'OPT1', 0),
('ANSLdXORc', 'MSYs8YjY', 'Q11', 'OPT3', 2),
('ANSlE4EQv', 'MSCvjNTp', 'Q3', 'OPT1', 0),
('ANSle4zAQ', 'MS2wWZvm', 'Q4', 'OPT1', 0),
('ANSleaJXW', 'MShivwr1', 'Q11', 'OPT2', 1),
('ANSLEjdOd', 'MSn52ZdO', 'Q14', 'OPT4', 3),
('ANSlekSyJ', 'MSxAFfYH', 'Q18', 'OPT1', 0),
('ANSlfBtT8', 'MSHVECsr', 'Q16', 'OPT4', 3),
('ANSlgXTr7', 'MSB9hlkR', 'Q16', 'OPT1', 0),
('ANSlhfNwi', 'MSMSWRrW', 'Q1', 'OPT2', 1),
('ANSlHuoRK', 'MSD2CdU4', 'Q12', 'OPT2', 1),
('ANSLHvEs1', 'MSxAFfYH', 'Q22', 'OPT7', 2),
('ANSLieiD3', 'MSe8IKuy', 'Q7', 'OPT1', 0),
('ANSLjBqIS', 'MSrsK5M0', 'Q23', 'OPT5', 0),
('ANSLjRaV9', 'MS7uU47p', 'Q24', 'OPT5', 0),
('ANSLKbgOq', 'MSWzJzOu', 'Q4', 'OPT2', 1),
('ANSlKLy4j', 'MSOiRHyL', 'Q15', 'OPT1', 0),
('ANSLkTqki', 'MSOTfXXF', 'Q2', 'OPT2', 1),
('ANSLL678h', 'MSjIxFsI', 'Q9', 'OPT2', 1),
('ANSLlAHzL', 'MS8DLldu', 'Q25', 'OPT7', 2),
('ANSLlNcAJ', 'MSHVECsr', 'Q1', 'OPT1', 0),
('ANSlLVHyY', 'MSEDOwl4', 'Q15', 'OPT2', 1),
('ANSlLYdWr', 'MSkUYFSU', 'Q3', 'OPT2', 1),
('ANSLLypP3', 'MSuM6ldN', 'Q15', 'OPT3', 2),
('ANSlMbr30', 'MSmCH78J', 'Q19', 'OPT1', 0),
('ANSlMWQTc', 'MSCcWA57', 'Q13', 'OPT2', 1),
('ANSlmXRHT', 'MSOiRHyL', 'Q8', 'OPT1', 0),
('ANSLNb6Mq', 'MSmLaR9I', 'Q10', 'OPT1', 0),
('ANSLNZObp', 'MStavaQ6', 'Q7', 'OPT1', 0),
('ANSlOoznW', 'MShC7NUw', 'Q19', 'OPT3', 2),
('ANSLOqrLs', 'MSjIxFsI', 'Q8', 'OPT1', 0),
('ANSloSXa2', 'MSEDOwl4', 'Q4', 'OPT2', 1),
('ANSlP6rHV', 'MSpuxB2A', 'Q19', 'OPT2', 1),
('ANSlpXnD1', 'MSwTXozI', 'Q8', 'OPT1', 0),
('ANSLQlcsn', 'MSwTXozI', 'Q21', 'OPT1', 0),
('ANSLQZD0N', 'MSKBVoiE', 'Q18', 'OPT1', 0),
('ANSLRKGXd', 'MSb1vGgr', 'Q13', 'OPT4', 3),
('ANSlSnoas', 'MS8Ku3zf', 'Q22', 'OPT6', 1),
('ANSLSokhX', 'MSXxuKfh', 'Q15', 'OPT2', 1),
('ANSlteZ2M', 'MS53AquJ', 'Q15', 'OPT2', 1),
('ANSLvecBb', 'MSH2zQ4z', 'Q11', 'OPT1', 0),
('ANSlvveBC', 'MS6zWUZX', 'Q6', 'OPT2', 1),
('ANSlW3GCi', 'MS5aBjQf', 'Q12', 'OPT2', 1),
('ANSlwDxt1', 'MSSbGTht', 'Q25', 'OPT5', 0),
('ANSlwLzFS', 'MS2wWZvm', 'Q22', 'OPT5', 0),
('ANSlXA6FU', 'MS5oYfaH', 'Q5', 'OPT2', 1),
('ANSLXdZbZ', 'MSjIxFsI', 'Q21', 'OPT1', 0),
('ANSlxt6mN', 'MSCvjNTp', 'Q19', 'OPT1', 0),
('ANSLXXJph', 'MShKvqCr', 'Q20', 'OPT1', 0),
('ANSlYeMrJ', 'MS2E6rGI', 'Q13', 'OPT3', 2),
('ANSLYRGTm', 'MSOiRHyL', 'Q18', 'OPT4', 3),
('ANSlz8LfU', 'MSCJQ7km', 'Q17', 'OPT3', 2),
('ANSm0vyoZ', 'MS6zWUZX', 'Q26', 'OPT5', 0),
('ANSm1p1rK', 'MSEDOwl4', 'Q9', 'OPT1', 0),
('ANSM1w3Zn', 'MSCvjNTp', 'Q6', 'OPT2', 1),
('ANSm2pzWH', 'MSYs8YjY', 'Q14', 'OPT2', 1),
('ANSM4LdDQ', 'MSMSWRrW', 'Q6', 'OPT2', 1),
('ANSM6lqW1', 'MSwzvNE9', 'Q23', 'OPT5', 0),
('ANSm7kTZc', 'MS8Zetbr', 'Q2', 'OPT1', 0),
('ANSM91dhT', 'MSmLaR9I', 'Q3', 'OPT2', 1),
('ANSma5VNP', 'MSrsK5M0', 'Q6', 'OPT2', 1),
('ANSmaDVfs', 'MSJCXCcq', 'Q8', 'OPT1', 0),
('ANSMbf9q8', 'MSrzIxEi', 'Q7', 'OPT1', 0),
('ANSMbh4Pk', 'MS7uU47p', 'Q15', 'OPT1', 0),
('ANSmbKOEB', 'MS2wWZvm', 'Q14', 'OPT1', 0),
('ANSmBkpPQ', 'MS2wWZvm', 'Q6', 'OPT1', 0),
('ANSmC8s3D', 'MSCvjNTp', 'Q21', 'OPT1', 0),
('ANSMCuzUd', 'MShC7NUw', 'Q9', 'OPT3', 2),
('ANSmDeWNZ', 'MSAlECsL', 'Q13', 'OPT2', 1),
('ANSMdruF5', 'MSSbGTht', 'Q6', 'OPT3', 2),
('ANSmeBIn4', 'MSuM6ldN', 'Q24', 'OPT6', 1),
('ANSMEBiPy', 'MS2S9Sh8', 'Q21', 'OPT2', 1),
('ANSmfSexz', 'MS7T2bhr', 'Q1', 'OPT3', 2),
('ANSmfwylb', 'MSmLaR9I', 'Q15', 'OPT1', 0),
('ANSMg4RG3', 'MS5aBjQf', 'Q10', 'OPT2', 1),
('ANSmGf9Pt', 'MSjIxFsI', 'Q12', 'OPT1', 0),
('ANSmgnQfM', 'MSXxuKfh', 'Q5', 'OPT3', 2),
('ANSMGrD3h', 'MSC84et8', 'Q3', 'OPT1', 0),
('ANSMHArRG', 'MSwzvNE9', 'Q13', 'OPT4', 3),
('ANSmhfbH1', 'MSJCXCcq', 'Q21', 'OPT1', 0),
('ANSMhK085', 'MST7153t', 'Q24', 'OPT6', 1),
('ANSMIEWJs', 'MShivwr1', 'Q2', 'OPT3', 2),
('ANSmIKMkm', 'MSHVECsr', 'Q7', 'OPT1', 0),
('ANSMIlMEL', 'MSrsK5M0', 'Q7', 'OPT2', 1),
('ANSmiy7tH', 'MSwzvNE9', 'Q9', 'OPT4', 3),
('ANSMiYqmr', 'MSrJv6lc', 'Q23', 'OPT5', 0),
('ANSMJBCfq', 'MSUY57sP', 'Q22', 'OPT5', 0),
('ANSMJkWAw', 'MSGNt66z', 'Q19', 'OPT1', 0),
('ANSmJPpKt', 'MSEDOwl4', 'Q8', 'OPT2', 1),
('ANSMJsBZn', 'MSYZvsiu', 'Q22', 'OPT5', 0),
('ANSMkx1IT', 'MSB9hlkR', 'Q23', 'OPT5', 0),
('ANSMKZFZ3', 'MSuq7kIE', 'Q24', 'OPT5', 0),
('ANSmMmvHd', 'MSZhKZEA', 'Q8', 'OPT3', 2),
('ANSMN2Bs7', 'MSZhKZEA', 'Q3', 'OPT3', 2),
('ANSMN6nk4', 'MSbHrzIU', 'Q13', 'OPT2', 1),
('ANSMnpkNX', 'MSWzJzOu', 'Q8', 'OPT1', 0),
('ANSMnVBCw', 'MSeqrFEz', 'Q4', 'OPT2', 1),
('ANSmOPSNl', 'MSYZvsiu', 'Q26', 'OPT5', 0),
('ANSMPEkJX', 'MS6zWUZX', 'Q12', 'OPT4', 3),
('ANSmpMiIx', 'MSrsK5M0', 'Q24', 'OPT5', 0),
('ANSMq2qqW', 'MSxAFfYH', 'Q12', 'OPT2', 1),
('ANSMq6hg7', 'MSC84et8', 'Q19', 'OPT2', 1),
('ANSMR58kd', 'MSwcZn3F', 'Q5', 'OPT2', 1),
('ANSMrGRJO', 'MSqHuFsF', 'Q16', 'OPT3', 2),
('ANSmrx08r', 'MSKrk0hq', 'Q19', 'OPT1', 0),
('ANSmTah3U', 'MSwTXozI', 'Q20', 'OPT1', 0),
('ANSmtqoGL', 'MSMzDgKm', 'Q14', 'OPT3', 2),
('ANSMTtQp7', 'MSS6eh4N', 'Q5', 'OPT2', 1),
('ANSmTVGX2', 'MSS6eh4N', 'Q23', 'OPT6', 1),
('ANSmUJ8Va', 'MS5xzf68', 'Q22', 'OPT5', 0),
('ANSmUmuQj', 'MS5aBjQf', 'Q7', 'OPT1', 0),
('ANSmuua7k', 'MS8Ku3zf', 'Q11', 'OPT4', 3),
('ANSMVr1zm', 'MSXxuKfh', 'Q25', 'OPT5', 0),
('ANSMw3Z7p', 'MSwcZn3F', 'Q15', 'OPT1', 0),
('ANSMwcIav', 'MSZhKZEA', 'Q2', 'OPT2', 1),
('ANSMwmqfl', 'MSEwAQL0', 'Q2', 'OPT2', 1),
('ANSMWq0s3', 'MSGTyAts', 'Q8', 'OPT1', 0),
('ANSmwSaL0', 'MSjSGqfF', 'Q15', 'OPT2', 1),
('ANSMwzclf', 'MSj941zD', 'Q5', 'OPT4', 3),
('ANSMxk9bN', 'MSB9hlkR', 'Q19', 'OPT1', 0),
('ANSMXUGGN', 'MSJCXCcq', 'Q22', 'OPT5', 0),
('ANSmXwB8M', 'MSSbGTht', 'Q22', 'OPT6', 1),
('ANSMXYRlW', 'MS2GS0M4', 'Q13', 'OPT2', 1),
('ANSMY9P1l', 'MSkUYFSU', 'Q20', 'OPT1', 0),
('ANSmZGLTo', 'MSYZvsiu', 'Q18', 'OPT2', 1),
('ANSn0pY2g', 'MS6zWUZX', 'Q9', 'OPT2', 1),
('ANSN2Tj5x', 'MS79deyJ', 'Q12', 'OPT2', 1),
('ANSN47MzV', 'MSYZvsiu', 'Q4', 'OPT1', 0),
('ANSN5ljOo', 'MShEdYT5', 'Q4', 'OPT4', 3),
('ANSn5MUpu', 'MSUY57sP', 'Q17', 'OPT1', 0),
('ANSN5qyFE', 'MS5aBjQf', 'Q8', 'OPT2', 1),
('ANSN6d6Zl', 'MShEdYT5', 'Q22', 'OPT6', 1),
('ANSn6IVNi', 'MSYZvsiu', 'Q16', 'OPT2', 1),
('ANSn8Fy7i', 'MSmLaR9I', 'Q23', 'OPT5', 0),
('ANSn8H5XB', 'MS8DLldu', 'Q14', 'OPT3', 2),
('ANSn9cU24', 'MS7uU47p', 'Q25', 'OPT5', 0),
('ANSNAUJ1H', 'MS8DLldu', 'Q18', 'OPT3', 2),
('ANSnAwNCM', 'MS6zWUZX', 'Q1', 'OPT2', 1),
('ANSnBZOdZ', 'MS6Hgbj4', 'Q14', 'OPT2', 1),
('ANSNc1Jb6', 'MS6Hgbj4', 'Q25', 'OPT5', 0),
('ANSnCVRZN', 'MS79deyJ', 'Q11', 'OPT2', 1),
('ANSndBLjp', 'MShKvqCr', 'Q21', 'OPT1', 0),
('ANSndUG3a', 'MS2GS0M4', 'Q22', 'OPT6', 1),
('ANSNf1a6T', 'MSX9aTK5', 'Q15', 'OPT1', 0),
('ANSnfRV3T', 'MSCJQ7km', 'Q15', 'OPT3', 2),
('ANSnfU0FF', 'MS5oYfaH', 'Q9', 'OPT3', 2),
('ANSNH5JGt', 'MS7f7XHX', 'Q15', 'OPT1', 0),
('ANSnhfyfc', 'MSb1vGgr', 'Q19', 'OPT3', 2),
('ANSnhHEsj', 'MSYkD4Nr', 'Q9', 'OPT2', 1),
('ANSNhWzGa', 'MSUY57sP', 'Q20', 'OPT1', 0),
('ANSNiBprY', 'MSWCMBqh', 'Q22', 'OPT6', 1),
('ANSniM8vb', 'MSKBVoiE', 'Q13', 'OPT1', 0),
('ANSNIszjc', 'MShC7NUw', 'Q26', 'OPT6', 1),
('ANSNivfAH', 'MSjIxFsI', 'Q1', 'OPT1', 0),
('ANSNJfDO7', 'MSOiRHyL', 'Q26', 'OPT6', 1),
('ANSnL8j1Y', 'MS138aYF', 'Q22', 'OPT5', 0),
('ANSnltBcX', 'MSHVECsr', 'Q21', 'OPT1', 0),
('ANSNLyexA', 'MS79deyJ', 'Q16', 'OPT1', 0),
('ANSnMBah0', 'MSHVECsr', 'Q12', 'OPT1', 0),
('ANSnmlxHS', 'MSGlWdke', 'Q10', 'OPT4', 3),
('ANSnMWLEy', 'MSGTyAts', 'Q22', 'OPT7', 2),
('ANSNnABjP', 'MSmLaR9I', 'Q24', 'OPT5', 0),
('ANSNoJ7HN', 'MSrsK5M0', 'Q13', 'OPT2', 1),
('ANSnPTQHU', 'MShKvqCr', 'Q10', 'OPT1', 0),
('ANSnQ81Dp', 'MSD2CdU4', 'Q1', 'OPT2', 1),
('ANSNrbaqO', 'MSFQYvYL', 'Q24', 'OPT5', 0),
('ANSnRtSco', 'MSKrk0hq', 'Q5', 'OPT4', 3),
('ANSnRvHi3', 'MSZhKZEA', 'Q11', 'OPT2', 1),
('ANSnstosH', 'MSeqrFEz', 'Q20', 'OPT1', 0),
('ANSNtLK9r', 'MS2GS0M4', 'Q9', 'OPT2', 1),
('ANSnUWBHH', 'MSrsK5M0', 'Q4', 'OPT1', 0),
('ANSNvi2pa', 'MSYkD4Nr', 'Q16', 'OPT2', 1),
('ANSNXgLia', 'MSGNt66z', 'Q10', 'OPT1', 0),
('ANSNyaAgP', 'MSWzJzOu', 'Q26', 'OPT5', 0),
('ANSNyake9', 'MSjIxFsI', 'Q11', 'OPT4', 3),
('ANSNznFG3', 'MSwcZn3F', 'Q3', 'OPT1', 0),
('ANSO0arSM', 'MSUVdR45', 'Q19', 'OPT2', 1),
('ANSO0U4TC', 'MShivwr1', 'Q4', 'OPT3', 2),
('ANSo4nxDT', 'MShKvqCr', 'Q15', 'OPT1', 0),
('ANSO4pCCG', 'MSxAFfYH', 'Q15', 'OPT2', 1),
('ANSO5P2sm', 'MSGTyAts', 'Q19', 'OPT4', 3),
('ANSO5ZThI', 'MSeqrFEz', 'Q16', 'OPT1', 0),
('ANSO67oVR', 'MSXxuKfh', 'Q16', 'OPT4', 3),
('ANSO6t38z', 'MSWzJzOu', 'Q7', 'OPT1', 0),
('ANSo7SN9a', 'MSxafMEG', 'Q9', 'OPT1', 0),
('ANSo7UNnA', 'MStavaQ6', 'Q4', 'OPT1', 0),
('ANSO8eCAK', 'MS5xzf68', 'Q12', 'OPT1', 0),
('ANSO8kg3n', 'MSWzJzOu', 'Q16', 'OPT1', 0),
('ANSo91ONb', 'MSxAFfYH', 'Q14', 'OPT1', 0),
('ANSo9JIc4', 'MS6Hgbj4', 'Q7', 'OPT1', 0),
('ANSOa0zNj', 'MShEdYT5', 'Q20', 'OPT2', 1),
('ANSOA3x0j', 'MS79deyJ', 'Q8', 'OPT2', 1),
('ANSoaZytJ', 'MSe8IKuy', 'Q6', 'OPT3', 2),
('ANSOB6Inb', 'MSuq7kIE', 'Q21', 'OPT1', 0),
('ANSOBaU2G', 'MSCvjNTp', 'Q8', 'OPT2', 1),
('ANSOCmW26', 'MSEDOwl4', 'Q25', 'OPT5', 0),
('ANSodxUUs', 'MSCJQ7km', 'Q16', 'OPT3', 2),
('ANSOF06eO', 'MSYZvsiu', 'Q25', 'OPT6', 1),
('ANSofsVFI', 'MSn52ZdO', 'Q17', 'OPT4', 3),
('ANSofz6Zz', 'MSMzDgKm', 'Q2', 'OPT1', 0),
('ANSOg8Inb', 'MS138aYF', 'Q19', 'OPT1', 0),
('ANSoGWgSZ', 'MS5aBjQf', 'Q5', 'OPT3', 2),
('ANSOgy8T6', 'MSj941zD', 'Q13', 'OPT3', 2),
('ANSoir9Qn', 'MSj5xUgK', 'Q5', 'OPT2', 1),
('ANSojOrxb', 'MS2S9Sh8', 'Q2', 'OPT4', 3),
('ANSOK8uGS', 'MSGNt66z', 'Q1', 'OPT2', 1),
('ANSolQONw', 'MSKBVoiE', 'Q7', 'OPT1', 0),
('ANSoLR17M', 'MS8Ku3zf', 'Q13', 'OPT4', 3),
('ANSoMPhv9', 'MS53AquJ', 'Q1', 'OPT4', 3),
('ANSonApze', 'MSGNt66z', 'Q6', 'OPT2', 1),
('ANSoneCJV', 'MSGlWdke', 'Q18', 'OPT4', 3),
('ANSooyVjI', 'MS8DLldu', 'Q9', 'OPT2', 1),
('ANSoPImSv', 'MShivwr1', 'Q18', 'OPT1', 0),
('ANSoPLKIH', 'MSmLaR9I', 'Q7', 'OPT1', 0),
('ANSOPuV9H', 'MSS6eh4N', 'Q20', 'OPT4', 3),
('ANSOq7eiB', 'MS5aBjQf', 'Q13', 'OPT2', 1),
('ANSOR9mgS', 'MSuM6ldN', 'Q16', 'OPT3', 2),
('ANSORGE92', 'MS2wWZvm', 'Q20', 'OPT1', 0),
('ANSOrJ3ea', 'MSYZvsiu', 'Q17', 'OPT2', 1),
('ANSOrr7Bc', 'MSj5xUgK', 'Q15', 'OPT2', 1),
('ANSoSHsKR', 'MSrzIxEi', 'Q8', 'OPT3', 2),
('ANSOTNL1V', 'MS53AquJ', 'Q16', 'OPT3', 2),
('ANSotwp9l', 'MSAlECsL', 'Q18', 'OPT1', 0),
('ANSoV0dBa', 'MS8Ku3zf', 'Q6', 'OPT4', 3),
('ANSov2Jtk', 'MS6Hgbj4', 'Q9', 'OPT2', 1),
('ANSOv4k72', 'MSrJv6lc', 'Q9', 'OPT1', 0),
('ANSOvher1', 'MSpuxB2A', 'Q6', 'OPT1', 0),
('ANSoVKQBw', 'MShEdYT5', 'Q25', 'OPT6', 1),
('ANSOvoEfW', 'MS7f7XHX', 'Q7', 'OPT1', 0),
('ANSow1hcj', 'MSMzDgKm', 'Q13', 'OPT3', 2),
('ANSoWGIjp', 'MSmLaR9I', 'Q5', 'OPT2', 1),
('ANSOx4s4O', 'MSuM6ldN', 'Q18', 'OPT4', 3),
('ANSoxjh1y', 'MSMSWRrW', 'Q21', 'OPT3', 2),
('ANSOXMcuL', 'MSJCXCcq', 'Q24', 'OPT5', 0),
('ANSOxtbe6', 'MSmLaR9I', 'Q12', 'OPT2', 1),
('ANSoxTXSP', 'MS79deyJ', 'Q19', 'OPT2', 1),
('ANSoXxzGD', 'MSD2CdU4', 'Q24', 'OPT6', 1),
('ANSOyqGk8', 'MS53AquJ', 'Q18', 'OPT2', 1),
('ANSOYqubz', 'MSwTXozI', 'Q25', 'OPT5', 0),
('ANSOZS0WN', 'MSXxuKfh', 'Q24', 'OPT7', 2),
('ANSp0eGAu', 'MSCcWA57', 'Q4', 'OPT2', 1),
('ANSP0sauC', 'MSUVdR45', 'Q12', 'OPT2', 1),
('ANSp0SOaF', 'MSUVdR45', 'Q14', 'OPT2', 1),
('ANSp14hHU', 'MSD2CdU4', 'Q20', 'OPT1', 0),
('ANSP1MWqT', 'MSKBVoiE', 'Q3', 'OPT1', 0),
('ANSp1t8bS', 'MSpuxB2A', 'Q26', 'OPT5', 0),
('ANSp297xZ', 'MS2wWZvm', 'Q24', 'OPT5', 0),
('ANSp2ZmoG', 'MSj941zD', 'Q18', 'OPT3', 2),
('ANSp3p1Vp', 'MSQq566a', 'Q4', 'OPT1', 0),
('ANSP45C1C', 'MSwTXozI', 'Q2', 'OPT1', 0),
('ANSp4l9z6', 'MSOiRHyL', 'Q24', 'OPT6', 1),
('ANSp6YtLX', 'MSFQYvYL', 'Q8', 'OPT1', 0),
('ANSp7062t', 'MS8DLldu', 'Q20', 'OPT1', 0),
('ANSp7OZmQ', 'MSJCXCcq', 'Q14', 'OPT1', 0),
('ANSp8ORLY', 'MSEwAQL0', 'Q10', 'OPT2', 1),
('ANSP8rA9B', 'MSS6eh4N', 'Q4', 'OPT3', 2),
('ANSP8RcYM', 'MS1kbVC6', 'Q22', 'OPT5', 0),
('ANSp96MYV', 'MSrJv6lc', 'Q4', 'OPT2', 1),
('ANSp9aqaH', 'MSGNt66z', 'Q21', 'OPT1', 0),
('ANSp9eEXO', 'MSJCXCcq', 'Q25', 'OPT5', 0),
('ANSPa1xzQ', 'MSSbGTht', 'Q9', 'OPT3', 2),
('ANSpaKn8U', 'MSj941zD', 'Q11', 'OPT4', 3),
('ANSpAQTjB', 'MSeqrFEz', 'Q14', 'OPT1', 0),
('ANSpawQZh', 'MSb1vGgr', 'Q2', 'OPT4', 3),
('ANSPax1ud', 'MSGTyAts', 'Q16', 'OPT2', 1),
('ANSpbJh0D', 'MSUY57sP', 'Q12', 'OPT1', 0),
('ANSPbQJCX', 'MSrql4yA', 'Q6', 'OPT4', 3),
('ANSPCCq96', 'MSEwAQL0', 'Q15', 'OPT2', 1),
('ANSPCZE8c', 'MS7T2bhr', 'Q4', 'OPT2', 1),
('ANSPDkFsd', 'MSFQYvYL', 'Q13', 'OPT1', 0),
('ANSpDm2r5', 'MSXxuKfh', 'Q17', 'OPT1', 0),
('ANSpe96UR', 'MSZhKZEA', 'Q9', 'OPT3', 2),
('ANSPeE1RA', 'MSC84et8', 'Q5', 'OPT3', 2),
('ANSpef6tn', 'MSMSWRrW', 'Q9', 'OPT2', 1),
('ANSpEL316', 'MSJCXCcq', 'Q3', 'OPT1', 0),
('ANSpel3Kr', 'MS7uU47p', 'Q2', 'OPT1', 0),
('ANSpEvZ3J', 'MSCJQ7km', 'Q23', 'OPT6', 1),
('ANSpfpsyH', 'MSjIxFsI', 'Q16', 'OPT2', 1),
('ANSPFv4BZ', 'MSKrk0hq', 'Q16', 'OPT4', 3),
('ANSPg4JXA', 'MSQq566a', 'Q16', 'OPT1', 0),
('ANSPg9ymX', 'MSYs8YjY', 'Q4', 'OPT1', 0),
('ANSPGkIor', 'MSWCMBqh', 'Q5', 'OPT1', 0),
('ANSPGprqd', 'MSb1vGgr', 'Q18', 'OPT4', 3),
('ANSPh6FFy', 'MSrsK5M0', 'Q3', 'OPT2', 1),
('ANSPHEsqE', 'MSZhKZEA', 'Q10', 'OPT2', 1),
('ANSpIngql', 'MS2GS0M4', 'Q11', 'OPT1', 0),
('ANSPiZo7U', 'MS1kbVC6', 'Q4', 'OPT2', 1),
('ANSPJhCOQ', 'MSFQYvYL', 'Q7', 'OPT2', 1),
('ANSPjxbeC', 'MSFQYvYL', 'Q5', 'OPT2', 1),
('ANSPjXTb6', 'MSmCH78J', 'Q9', 'OPT1', 0),
('ANSPKjwBh', 'MS2E6rGI', 'Q8', 'OPT3', 2),
('ANSpkmOeu', 'MSbHrzIU', 'Q10', 'OPT1', 0),
('ANSPlaA3Y', 'MS53AquJ', 'Q8', 'OPT3', 2),
('ANSPlhDTu', 'MSGTyAts', 'Q18', 'OPT1', 0),
('ANSplIM9E', 'MSe8IKuy', 'Q20', 'OPT3', 2),
('ANSplWWpN', 'MSWCMBqh', 'Q2', 'OPT2', 1),
('ANSpM2xjS', 'MSrJv6lc', 'Q5', 'OPT1', 0),
('ANSpMMmta', 'MSUVdR45', 'Q9', 'OPT1', 0),
('ANSpNS0Az', 'MSkUYFSU', 'Q8', 'OPT1', 0),
('ANSPnT5sI', 'MSAlECsL', 'Q22', 'OPT6', 1),
('ANSpOci7p', 'MSUY57sP', 'Q7', 'OPT1', 0),
('ANSPprxbs', 'MSuq7kIE', 'Q23', 'OPT5', 0),
('ANSPPwfQl', 'MS5aBjQf', 'Q16', 'OPT2', 1),
('ANSPQq3tI', 'MSb1vGgr', 'Q15', 'OPT4', 3),
('ANSPQxoLW', 'MS79deyJ', 'Q5', 'OPT2', 1),
('ANSPR9LdL', 'MSj941zD', 'Q1', 'OPT4', 3),
('ANSpRbmqd', 'MSUJDtjy', 'Q8', 'OPT2', 1),
('ANSPrYtDf', 'MSwTXozI', 'Q22', 'OPT5', 0),
('ANSpsYEXg', 'MSrJv6lc', 'Q10', 'OPT1', 0),
('ANSpTCoe4', 'MStavaQ6', 'Q11', 'OPT2', 1),
('ANSptGNI2', 'MS2GS0M4', 'Q18', 'OPT1', 0),
('ANSPtGWQo', 'MSS6eh4N', 'Q26', 'OPT5', 0),
('ANSpTiNRv', 'MSXxuKfh', 'Q19', 'OPT4', 3),
('ANSpUwFFy', 'MSAlECsL', 'Q26', 'OPT6', 1),
('ANSpvG6ex', 'MSxAFfYH', 'Q24', 'OPT5', 0),
('ANSPw6eEv', 'MS8DLldu', 'Q13', 'OPT2', 1),
('ANSpW7VwI', 'MSmCH78J', 'Q22', 'OPT6', 1),
('ANSPwTbbS', 'MSMzDgKm', 'Q4', 'OPT1', 0),
('ANSpWWaMA', 'MSmLaR9I', 'Q26', 'OPT5', 0),
('ANSPXomA8', 'MSGlWdke', 'Q13', 'OPT2', 1),
('ANSpxPrmc', 'MSb1vGgr', 'Q14', 'OPT4', 3),
('ANSpxwXHq', 'MSX9aTK5', 'Q1', 'OPT2', 1),
('ANSPYmvSx', 'MSmLaR9I', 'Q2', 'OPT1', 0),
('ANSpZ4SD6', 'MSbHrzIU', 'Q20', 'OPT2', 1),
('ANSpZAEAx', 'MSe8IKuy', 'Q23', 'OPT5', 0),
('ANSpZLxjM', 'MSuM6ldN', 'Q23', 'OPT6', 1),
('ANSQ0wsjI', 'MSYs8YjY', 'Q7', 'OPT3', 2),
('ANSq1TY8C', 'MSMzDgKm', 'Q25', 'OPT6', 1),
('ANSQ333i4', 'MSUY57sP', 'Q3', 'OPT2', 1),
('ANSq3A8jQ', 'MSn52ZdO', 'Q22', 'OPT5', 0),
('ANSQ3HJ0W', 'MS2S9Sh8', 'Q23', 'OPT5', 0),
('ANSQ4ajfA', 'MSYZvsiu', 'Q10', 'OPT2', 1),
('ANSQ6AN2Q', 'MSMSWRrW', 'Q17', 'OPT3', 2),
('ANSQ6NZcv', 'MSS6eh4N', 'Q18', 'OPT1', 0),
('ANSq6tevB', 'MSGTyAts', 'Q21', 'OPT1', 0),
('ANSq7BzAz', 'MSOiRHyL', 'Q21', 'OPT1', 0),
('ANSQ7GkJv', 'MS7T2bhr', 'Q9', 'OPT1', 0),
('ANSq8fsNV', 'MSrzIxEi', 'Q5', 'OPT3', 2),
('ANSq8jBRK', 'MSqHuFsF', 'Q8', 'OPT3', 2),
('ANSq95f1C', 'MSj5xUgK', 'Q19', 'OPT2', 1),
('ANSQ9Pisv', 'MS6Hgbj4', 'Q21', 'OPT2', 1),
('ANSQBOpU9', 'MSxAFfYH', 'Q6', 'OPT2', 1),
('ANSqBZR4t', 'MStavaQ6', 'Q19', 'OPT1', 0),
('ANSqCK7MD', 'MSGlWdke', 'Q2', 'OPT4', 3),
('ANSQCMEN6', 'MSCcWA57', 'Q9', 'OPT1', 0),
('ANSQcnKZE', 'MSGTyAts', 'Q5', 'OPT1', 0),
('ANSQe4uT5', 'MSUJDtjy', 'Q9', 'OPT1', 0),
('ANSqeCFUm', 'MSEwAQL0', 'Q7', 'OPT3', 2),
('ANSQeGhWE', 'MSj5xUgK', 'Q26', 'OPT5', 0),
('ANSqfjAvn', 'MSb1vGgr', 'Q3', 'OPT4', 3),
('ANSQfuTX5', 'MS53AquJ', 'Q23', 'OPT6', 1),
('ANSqGIG2i', 'MShC7NUw', 'Q10', 'OPT4', 3),
('ANSqHFj5U', 'MS8Zetbr', 'Q5', 'OPT1', 0),
('ANSQhnflC', 'MSCJQ7km', 'Q3', 'OPT3', 2),
('ANSQHs1xy', 'MS5aBjQf', 'Q22', 'OPT6', 1),
('ANSQhVwiy', 'MS6zWUZX', 'Q21', 'OPT2', 1),
('ANSQHYsyi', 'MStavaQ6', 'Q3', 'OPT1', 0),
('ANSQI9eiv', 'MSxafMEG', 'Q15', 'OPT1', 0),
('ANSqiPfLi', 'MS5xzf68', 'Q15', 'OPT1', 0),
('ANSQj7OiE', 'MSrzIxEi', 'Q20', 'OPT3', 2),
('ANSQJfNG2', 'MSrzIxEi', 'Q12', 'OPT3', 2),
('ANSQjgUz0', 'MS6Hgbj4', 'Q4', 'OPT1', 0),
('ANSqjjeTI', 'MStavaQ6', 'Q5', 'OPT4', 3),
('ANSqKbyu6', 'MStavaQ6', 'Q16', 'OPT1', 0),
('ANSqkETyH', 'MS8Ku3zf', 'Q12', 'OPT3', 2),
('ANSQkmNZs', 'MSwzvNE9', 'Q15', 'OPT1', 0),
('ANSqkz511', 'MSOiRHyL', 'Q19', 'OPT1', 0),
('ANSqLb8bC', 'MSjSGqfF', 'Q7', 'OPT1', 0),
('ANSqlXKnr', 'MS6Hgbj4', 'Q12', 'OPT2', 1),
('ANSQM0Mxt', 'MSuq7kIE', 'Q17', 'OPT1', 0),
('ANSQMpf0F', 'MShEdYT5', 'Q9', 'OPT2', 1),
('ANSqn2Yz3', 'MSH2zQ4z', 'Q9', 'OPT2', 1),
('ANSqnDTtN', 'MSSbGTht', 'Q13', 'OPT2', 1),
('ANSqNS1F7', 'MS2wWZvm', 'Q12', 'OPT1', 0),
('ANSqnXvzj', 'MSwzvNE9', 'Q12', 'OPT4', 3),
('ANSQoz25l', 'MS2wWZvm', 'Q17', 'OPT1', 0),
('ANSQpgcb1', 'MSb1vGgr', 'Q26', 'OPT5', 0),
('ANSQr2HVQ', 'MSX9aTK5', 'Q16', 'OPT1', 0),
('ANSQrVI8z', 'MS8DLldu', 'Q3', 'OPT4', 3),
('ANSQSGeoy', 'MSKBVoiE', 'Q24', 'OPT5', 0),
('ANSqSWcwP', 'MShivwr1', 'Q22', 'OPT5', 0),
('ANSQtDJSJ', 'MSrsK5M0', 'Q8', 'OPT2', 1),
('ANSqtfmGl', 'MSX9aTK5', 'Q26', 'OPT5', 0),
('ANSQtH8nw', 'MSGTyAts', 'Q13', 'OPT4', 3),
('ANSQu2hTd', 'MSqHuFsF', 'Q12', 'OPT3', 2),
('ANSquGSoc', 'MSYs8YjY', 'Q3', 'OPT2', 1),
('ANSQUmwgW', 'MSB9hlkR', 'Q24', 'OPT5', 0),
('ANSqUTpQU', 'MSEwAQL0', 'Q4', 'OPT1', 0),
('ANSQWIL6Y', 'MSJCXCcq', 'Q4', 'OPT1', 0),
('ANSqwNx2I', 'MSS6eh4N', 'Q6', 'OPT4', 3),
('ANSQwu8t3', 'MSUY57sP', 'Q16', 'OPT2', 1),
('ANSqX8Jdj', 'MSZhKZEA', 'Q23', 'OPT6', 1),
('ANSQxOAKj', 'MSQq566a', 'Q6', 'OPT3', 2),
('ANSQXOeOQ', 'MSH2zQ4z', 'Q5', 'OPT2', 1),
('ANSqYkHmH', 'MSGlWdke', 'Q21', 'OPT4', 3),
('ANSQYMW0A', 'MS79deyJ', 'Q17', 'OPT1', 0),
('ANSqYpsei', 'MSe8IKuy', 'Q2', 'OPT2', 1),
('ANSqzhyUH', 'MS6zWUZX', 'Q11', 'OPT1', 0),
('ANSqzjxxr', 'MSHVECsr', 'Q11', 'OPT4', 3),
('ANSQZqqwh', 'MSGlWdke', 'Q6', 'OPT4', 3),
('ANSR01UAa', 'MSKBVoiE', 'Q6', 'OPT2', 1),
('ANSR1U874', 'MS2S9Sh8', 'Q6', 'OPT2', 1),
('ANSr1Xhvx', 'MSOiRHyL', 'Q20', 'OPT1', 0),
('ANSr223OJ', 'MS2S9Sh8', 'Q3', 'OPT2', 1),
('ANSR2z5rX', 'MSX9aTK5', 'Q13', 'OPT2', 1),
('ANSR3fBvt', 'MS2S9Sh8', 'Q4', 'OPT2', 1),
('ANSR3veSJ', 'MS2S9Sh8', 'Q10', 'OPT1', 0),
('ANSr4HUCB', 'MSXxuKfh', 'Q23', 'OPT6', 1),
('ANSr4PEn7', 'MSB9hlkR', 'Q7', 'OPT1', 0),
('ANSR5jBAQ', 'MSn52ZdO', 'Q9', 'OPT1', 0),
('ANSR5YTuv', 'MS8DLldu', 'Q7', 'OPT1', 0),
('ANSR6RdRw', 'MSYZvsiu', 'Q13', 'OPT2', 1),
('ANSr6txBK', 'MSuM6ldN', 'Q5', 'OPT4', 3),
('ANSr7M1Xf', 'MSpuxB2A', 'Q14', 'OPT1', 0),
('ANSR7xSOi', 'MSj941zD', 'Q7', 'OPT4', 3),
('ANSR8P9Az', 'MS5oYfaH', 'Q26', 'OPT5', 0),
('ANSR8RIqQ', 'MSSbGTht', 'Q23', 'OPT5', 0),
('ANSR9gEnf', 'MS2wWZvm', 'Q18', 'OPT1', 0),
('ANSRa0DLK', 'MS53AquJ', 'Q24', 'OPT6', 1),
('ANSrasDB2', 'MSMzDgKm', 'Q5', 'OPT3', 2),
('ANSraWXnP', 'MShivwr1', 'Q24', 'OPT5', 0),
('ANSrBu7gC', 'MSJCXCcq', 'Q26', 'OPT5', 0),
('ANSrCb8qh', 'MSUJDtjy', 'Q2', 'OPT1', 0),
('ANSRd2Vb3', 'MSjIxFsI', 'Q17', 'OPT1', 0),
('ANSrdw6gq', 'MSj941zD', 'Q9', 'OPT4', 3),
('ANSRFmLsF', 'MS53AquJ', 'Q9', 'OPT2', 1),
('ANSrGQ1Q6', 'MS2E6rGI', 'Q9', 'OPT4', 3),
('ANSRgVVmt', 'MS53AquJ', 'Q4', 'OPT1', 0),
('ANSRH1cJD', 'MSYZvsiu', 'Q24', 'OPT5', 0),
('ANSrhizyY', 'MSj941zD', 'Q16', 'OPT4', 3),
('ANSRhoqUV', 'MSXxuKfh', 'Q7', 'OPT4', 3),
('ANSricG5Q', 'MSH2zQ4z', 'Q16', 'OPT2', 1),
('ANSriHMG3', 'MSj941zD', 'Q15', 'OPT3', 2),
('ANSRIqx22', 'MSMzDgKm', 'Q17', 'OPT4', 3),
('ANSRIWzeZ', 'MSAlECsL', 'Q10', 'OPT2', 1),
('ANSRjfupH', 'MSrql4yA', 'Q9', 'OPT4', 3),
('ANSrjkByk', 'MSEwAQL0', 'Q18', 'OPT4', 3),
('ANSrkAdt2', 'MSwcZn3F', 'Q24', 'OPT6', 1),
('ANSrkBUyN', 'MSYZvsiu', 'Q23', 'OPT5', 0),
('ANSrkxvUY', 'MSUVdR45', 'Q8', 'OPT2', 1),
('ANSRleuR5', 'MSGlWdke', 'Q26', 'OPT5', 0),
('ANSRLLmXl', 'MSGNt66z', 'Q2', 'OPT2', 1),
('ANSrLwknu', 'MSGlWdke', 'Q12', 'OPT4', 3),
('ANSRO07Vk', 'MSOiRHyL', 'Q4', 'OPT1', 0),
('ANSrOetTl', 'MSwzvNE9', 'Q20', 'OPT4', 3),
('ANSRoI3YU', 'MS5xzf68', 'Q24', 'OPT5', 0),
('ANSROKaym', 'MS5aBjQf', 'Q6', 'OPT2', 1),
('ANSroTvDd', 'MSC84et8', 'Q2', 'OPT1', 0),
('ANSrpwBg0', 'MS7f7XHX', 'Q1', 'OPT1', 0),
('ANSRqi77P', 'MSWzJzOu', 'Q1', 'OPT4', 3),
('ANSRRBytB', 'MStavaQ6', 'Q26', 'OPT6', 1),
('ANSRReqm2', 'MSS6eh4N', 'Q17', 'OPT3', 2),
('ANSRRVbb8', 'MSrJv6lc', 'Q14', 'OPT1', 0),
('ANSRS7mKv', 'MS2wWZvm', 'Q2', 'OPT1', 0),
('ANSrshndV', 'MS7uU47p', 'Q10', 'OPT1', 0),
('ANSRSxDBP', 'MSAlECsL', 'Q16', 'OPT3', 2),
('ANSrtLLvS', 'MS79deyJ', 'Q13', 'OPT2', 1),
('ANSrU8LWk', 'MS5xzf68', 'Q26', 'OPT5', 0),
('ANSruhBMX', 'MS2GS0M4', 'Q20', 'OPT1', 0),
('ANSRvz34U', 'MSH2zQ4z', 'Q14', 'OPT1', 0),
('ANSRvZS0W', 'MS6zWUZX', 'Q14', 'OPT2', 1),
('ANSRwShHr', 'MSHVECsr', 'Q6', 'OPT3', 2),
('ANSrX7dF4', 'MSmCH78J', 'Q8', 'OPT1', 0),
('ANSRXbMvv', 'MSZhKZEA', 'Q7', 'OPT2', 1),
('ANSRxiBMh', 'MSSbGTht', 'Q3', 'OPT2', 1),
('ANSrXuQmP', 'MSCvjNTp', 'Q17', 'OPT1', 0),
('ANSryLvza', 'MSmCH78J', 'Q16', 'OPT1', 0),
('ANSryMOeG', 'MSb1vGgr', 'Q8', 'OPT4', 3),
('ANSRyU7XB', 'MSUY57sP', 'Q23', 'OPT5', 0),
('ANSRz0u85', 'MSe8IKuy', 'Q1', 'OPT2', 1),
('ANSRz3LSI', 'MSWzJzOu', 'Q3', 'OPT4', 3),
('ANSRZ3xie', 'MS5xzf68', 'Q20', 'OPT1', 0),
('ANSRZ8D4h', 'MShKvqCr', 'Q22', 'OPT5', 0),
('ANSrzGuOm', 'MSeqrFEz', 'Q13', 'OPT2', 1),
('ANSRZHm5w', 'MSwzvNE9', 'Q5', 'OPT4', 3),
('ANSrzKK4z', 'MSuq7kIE', 'Q18', 'OPT1', 0),
('ANSs0bFau', 'MShC7NUw', 'Q2', 'OPT2', 1),
('ANSs10L71', 'MSYs8YjY', 'Q18', 'OPT3', 2),
('ANSS1D6GA', 'MSn52ZdO', 'Q16', 'OPT1', 0),
('ANSs3qkBv', 'MSjSGqfF', 'Q23', 'OPT5', 0),
('ANSs3qnwH', 'MSCcWA57', 'Q22', 'OPT5', 0),
('ANSs7PM56', 'MSAlECsL', 'Q24', 'OPT6', 1),
('ANSS7UOls', 'MSSbGTht', 'Q12', 'OPT1', 0),
('ANSs8XBG7', 'MSFQYvYL', 'Q20', 'OPT1', 0),
('ANSs8YbSb', 'MSAlECsL', 'Q12', 'OPT2', 1),
('ANSS9WMry', 'MS53AquJ', 'Q5', 'OPT2', 1),
('ANSSa86YY', 'MSS6eh4N', 'Q3', 'OPT2', 1),
('ANSsAbpB2', 'MS5xzf68', 'Q16', 'OPT1', 0),
('ANSsBiRzZ', 'MSYkD4Nr', 'Q18', 'OPT2', 1),
('ANSSbLhl7', 'MS8DLldu', 'Q10', 'OPT1', 0),
('ANSSbxWFn', 'MSD2CdU4', 'Q7', 'OPT1', 0),
('ANSScBNau', 'MS5oYfaH', 'Q8', 'OPT1', 0),
('ANSSCs2nT', 'MSXxuKfh', 'Q12', 'OPT1', 0),
('ANSSd4YJn', 'MS138aYF', 'Q20', 'OPT1', 0),
('ANSSdLe4I', 'MSEDOwl4', 'Q3', 'OPT4', 3),
('ANSSdo5Ww', 'MShKvqCr', 'Q8', 'OPT1', 0),
('ANSsE7RzW', 'MS5oYfaH', 'Q2', 'OPT2', 1),
('ANSSGtqr9', 'MSuq7kIE', 'Q4', 'OPT1', 0),
('ANSsHVaI2', 'MS5xzf68', 'Q5', 'OPT1', 0),
('ANSsHXIto', 'MS7f7XHX', 'Q3', 'OPT2', 1),
('ANSShYaky', 'MSjIxFsI', 'Q20', 'OPT1', 0),
('ANSSJI0th', 'MSZhKZEA', 'Q20', 'OPT3', 2),
('ANSsJuaU4', 'MSxafMEG', 'Q10', 'OPT1', 0),
('ANSSK3rVS', 'MS1kbVC6', 'Q19', 'OPT2', 1),
('ANSSkIEWw', 'MSpuxB2A', 'Q15', 'OPT1', 0),
('ANSSKQgXb', 'MSj5xUgK', 'Q23', 'OPT6', 1),
('ANSsKUixm', 'MS5aBjQf', 'Q15', 'OPT2', 1),
('ANSsLciWi', 'MS2E6rGI', 'Q12', 'OPT3', 2),
('ANSSLe5lG', 'MS2wWZvm', 'Q5', 'OPT1', 0),
('ANSsLJkMz', 'MSAlECsL', 'Q5', 'OPT3', 2),
('ANSsLSxrY', 'MSZhKZEA', 'Q1', 'OPT4', 3),
('ANSsNtqAb', 'MShivwr1', 'Q17', 'OPT1', 0),
('ANSsnUo3J', 'MSLEeXDP', 'Q15', 'OPT2', 1),
('ANSsnXs86', 'MSCvjNTp', 'Q23', 'OPT6', 1),
('ANSSofuZ1', 'MS1kbVC6', 'Q25', 'OPT5', 0),
('ANSsOkOvy', 'MSZhKZEA', 'Q4', 'OPT2', 1),
('ANSsokzSi', 'MSpuxB2A', 'Q24', 'OPT5', 0),
('ANSsoYaXj', 'MShC7NUw', 'Q17', 'OPT3', 2),
('ANSspeLPW', 'MSxAFfYH', 'Q23', 'OPT6', 1),
('ANSsQ2i1I', 'MSH2zQ4z', 'Q3', 'OPT2', 1),
('ANSsr5cXv', 'MSEwAQL0', 'Q20', 'OPT3', 2),
('ANSSRcuPj', 'MS6Hgbj4', 'Q6', 'OPT2', 1),
('ANSsSmTd6', 'MSLEeXDP', 'Q5', 'OPT2', 1),
('ANSsTDdvB', 'MSQq566a', 'Q21', 'OPT4', 3),
('ANSStO3IF', 'MSQq566a', 'Q10', 'OPT3', 2),
('ANSsTvDqI', 'MSKrk0hq', 'Q15', 'OPT1', 0),
('ANSstvLdn', 'MSrql4yA', 'Q15', 'OPT4', 3),
('ANSSuEghE', 'MSUJDtjy', 'Q16', 'OPT1', 0),
('ANSSUMYjH', 'MSC84et8', 'Q17', 'OPT1', 0),
('ANSsv7iJz', 'MSjSGqfF', 'Q10', 'OPT4', 3),
('ANSSVCxDE', 'MSUVdR45', 'Q13', 'OPT3', 2),
('ANSSVXS68', 'MSWzJzOu', 'Q25', 'OPT5', 0),
('ANSsWbEpO', 'MSbHrzIU', 'Q5', 'OPT2', 1),
('ANSSwHzSV', 'MSCJQ7km', 'Q20', 'OPT2', 1),
('ANSsXLox0', 'MSUVdR45', 'Q10', 'OPT3', 2),
('ANSSy3YaU', 'MSC84et8', 'Q11', 'OPT2', 1),
('ANSSyHgKC', 'MSKBVoiE', 'Q21', 'OPT1', 0),
('ANSSz94CW', 'MSb1vGgr', 'Q6', 'OPT4', 3),
('ANSsZMeSR', 'MS7f7XHX', 'Q6', 'OPT2', 1),
('ANST0hrZi', 'MSc4MwjD', 'Q1', 'OPT1', 0),
('ANSt2L3bG', 'MST7153t', 'Q11', 'OPT2', 1),
('ANST3KnhP', 'MS5aBjQf', 'Q1', 'OPT2', 1),
('ANST3O8vh', 'MSUY57sP', 'Q5', 'OPT4', 3),
('ANST3wK8A', 'MShivwr1', 'Q7', 'OPT1', 0),
('ANSt49sfd', 'MSkUYFSU', 'Q25', 'OPT5', 0),
('ANSt4QBL5', 'MSX9aTK5', 'Q23', 'OPT5', 0),
('ANST5H00V', 'MSUJDtjy', 'Q21', 'OPT3', 2),
('ANST6Tuow', 'MSYs8YjY', 'Q12', 'OPT4', 3),
('ANST7aTWy', 'MSLEeXDP', 'Q4', 'OPT2', 1),
('ANSt8EPPM', 'MSGNt66z', 'Q9', 'OPT1', 0),
('ANSTbLZyC', 'MS5xzf68', 'Q9', 'OPT1', 0),
('ANStcGnU4', 'MSpuxB2A', 'Q16', 'OPT2', 1),
('ANStCwafC', 'MSbHrzIU', 'Q26', 'OPT6', 1),
('ANSTdK0qu', 'MSGlWdke', 'Q24', 'OPT6', 1),
('ANSTE8alx', 'MSjIxFsI', 'Q19', 'OPT1', 0),
('ANSTeYkmE', 'MShivwr1', 'Q5', 'OPT2', 1),
('ANSTG2N8B', 'MShKvqCr', 'Q26', 'OPT5', 0),
('ANSTg8OVF', 'MSb1vGgr', 'Q12', 'OPT4', 3),
('ANSTgDUsW', 'MS2S9Sh8', 'Q26', 'OPT6', 1),
('ANStgTvBh', 'MStavaQ6', 'Q17', 'OPT1', 0),
('ANSTH9rYE', 'MSUY57sP', 'Q6', 'OPT2', 1),
('ANSthBUOo', 'MSrzIxEi', 'Q14', 'OPT3', 2),
('ANSthhWI0', 'MS6Hgbj4', 'Q19', 'OPT1', 0),
('ANStHIaat', 'MSWzJzOu', 'Q17', 'OPT1', 0),
('ANSthNDaa', 'MSjSGqfF', 'Q19', 'OPT2', 1),
('ANSTI5FMP', 'MS2E6rGI', 'Q6', 'OPT3', 2),
('ANSTIdgqb', 'MSGTyAts', 'Q1', 'OPT1', 0),
('ANSTJMEPq', 'MSwcZn3F', 'Q11', 'OPT2', 1),
('ANStkL8er', 'MS1kbVC6', 'Q18', 'OPT2', 1),
('ANSTKLONr', 'MSZhKZEA', 'Q14', 'OPT2', 1),
('ANSTKxCFU', 'MSH2zQ4z', 'Q1', 'OPT2', 1),
('ANStlBCDt', 'MSJCXCcq', 'Q23', 'OPT5', 0),
('ANStLBUPC', 'MSWzJzOu', 'Q12', 'OPT3', 2),
('ANStlgm7D', 'MS1kbVC6', 'Q20', 'OPT1', 0),
('ANSTlgpuB', 'MS2GS0M4', 'Q16', 'OPT1', 0),
('ANSTlOEaY', 'MSn52ZdO', 'Q19', 'OPT4', 3),
('ANStMZzLY', 'MS53AquJ', 'Q11', 'OPT3', 2),
('ANSTnEkCf', 'MSrJv6lc', 'Q20', 'OPT2', 1),
('ANSTnkMcX', 'MSkUYFSU', 'Q17', 'OPT2', 1),
('ANStO61DH', 'MSCJQ7km', 'Q25', 'OPT7', 2),
('ANSTo6ck7', 'MSWzJzOu', 'Q19', 'OPT2', 1),
('ANStOqbq9', 'MSuq7kIE', 'Q16', 'OPT1', 0),
('ANStOVo5t', 'MSGTyAts', 'Q10', 'OPT2', 1),
('ANSTPCCTr', 'MShEdYT5', 'Q15', 'OPT3', 2),
('ANSTPJ1Lf', 'MS138aYF', 'Q14', 'OPT1', 0),
('ANStQheAP', 'MSrzIxEi', 'Q10', 'OPT3', 2),
('ANSTQpEaM', 'MS1kbVC6', 'Q13', 'OPT1', 0),
('ANSTqPQyM', 'MS7T2bhr', 'Q7', 'OPT1', 0),
('ANStRCmn4', 'MSeqrFEz', 'Q10', 'OPT1', 0),
('ANSTRE7Ym', 'MSUVdR45', 'Q20', 'OPT3', 2),
('ANSTsCiOP', 'MSj941zD', 'Q23', 'OPT6', 1),
('ANStson4j', 'MSjSGqfF', 'Q14', 'OPT2', 1),
('ANSTszfrx', 'MSMzDgKm', 'Q9', 'OPT2', 1),
('ANSTtiCmg', 'MST7153t', 'Q18', 'OPT3', 2),
('ANSTtrhyg', 'MSeqrFEz', 'Q25', 'OPT5', 0),
('ANStty7Bm', 'MSLEeXDP', 'Q9', 'OPT2', 1),
('ANSttYrGO', 'MS8Ku3zf', 'Q19', 'OPT2', 1),
('ANSTUEWFb', 'MSn52ZdO', 'Q7', 'OPT1', 0),
('ANStUQmHk', 'MSQq566a', 'Q20', 'OPT2', 1),
('ANStv4T6n', 'MSEDOwl4', 'Q22', 'OPT6', 1),
('ANStxCUTb', 'MSD2CdU4', 'Q26', 'OPT5', 0),
('ANStXGoqF', 'MSwcZn3F', 'Q9', 'OPT1', 0),
('ANSTXMEvj', 'MSmCH78J', 'Q10', 'OPT1', 0),
('ANStxqTUH', 'MSrql4yA', 'Q19', 'OPT4', 3),
('ANStxvBxE', 'MSC84et8', 'Q15', 'OPT2', 1),
('ANSTzaDLy', 'MSwcZn3F', 'Q2', 'OPT1', 0),
('ANSTzjsry', 'MSuM6ldN', 'Q11', 'OPT3', 2),
('ANSu0FOzz', 'MS5oYfaH', 'Q15', 'OPT2', 1),
('ANSU0KvlG', 'MSYkD4Nr', 'Q21', 'OPT1', 0),
('ANSu1Iszd', 'MS5oYfaH', 'Q4', 'OPT2', 1),
('ANSU4UJZL', 'MS8Zetbr', 'Q4', 'OPT1', 0),
('ANSU51U0z', 'MSD2CdU4', 'Q2', 'OPT2', 1),
('ANSU54ULQ', 'MS79deyJ', 'Q4', 'OPT2', 1),
('ANSU5X2Pk', 'MSUVdR45', 'Q7', 'OPT2', 1),
('ANSu5ZcbD', 'MS2S9Sh8', 'Q18', 'OPT2', 1),
('ANSU8BLcv', 'MS1kbVC6', 'Q12', 'OPT1', 0),
('ANSU8EbRK', 'MSQq566a', 'Q23', 'OPT6', 1),
('ANSUa0HId', 'MS7f7XHX', 'Q26', 'OPT5', 0),
('ANSUa3olx', 'MSEwAQL0', 'Q14', 'OPT3', 2),
('ANSuadx8Q', 'MSUJDtjy', 'Q19', 'OPT1', 0),
('ANSUaImss', 'MSbHrzIU', 'Q1', 'OPT2', 1),
('ANSUaKZ0V', 'MS8DLldu', 'Q16', 'OPT3', 2),
('ANSUBSe04', 'MSwzvNE9', 'Q25', 'OPT6', 1),
('ANSUD20zt', 'MSEDOwl4', 'Q16', 'OPT1', 0),
('ANSuDgAmW', 'MSqHuFsF', 'Q10', 'OPT2', 1),
('ANSUDi4mU', 'MSHVECsr', 'Q14', 'OPT4', 3),
('ANSuDoQL3', 'MSjSGqfF', 'Q3', 'OPT3', 2),
('ANSUDyQ7y', 'MSLEeXDP', 'Q6', 'OPT1', 0),
('ANSUeD9kp', 'MSwTXozI', 'Q5', 'OPT1', 0),
('ANSueTjti', 'MS79deyJ', 'Q21', 'OPT1', 0),
('ANSueuFAu', 'MSxAFfYH', 'Q20', 'OPT1', 0),
('ANSuf8w2E', 'MSOTfXXF', 'Q7', 'OPT1', 0),
('ANSUFGjzh', 'MSUY57sP', 'Q19', 'OPT1', 0),
('ANSugpe3R', 'MSFQYvYL', 'Q26', 'OPT6', 1),
('ANSuHPwzW', 'MSuM6ldN', 'Q25', 'OPT5', 0),
('ANSUHwg3j', 'MSYZvsiu', 'Q5', 'OPT2', 1),
('ANSUHYTI8', 'MStavaQ6', 'Q18', 'OPT1', 0),
('ANSuICWb4', 'MSwTXozI', 'Q13', 'OPT1', 0),
('ANSuidQAj', 'MSUJDtjy', 'Q5', 'OPT3', 2),
('ANSuigdGD', 'MSKrk0hq', 'Q23', 'OPT7', 2),
('ANSuIzQHo', 'MSWzJzOu', 'Q2', 'OPT1', 0),
('ANSuJ3cl4', 'MSrJv6lc', 'Q15', 'OPT2', 1),
('ANSUJmPfC', 'MS2E6rGI', 'Q22', 'OPT6', 1),
('ANSujWZuO', 'MSrJv6lc', 'Q16', 'OPT1', 0),
('ANSukFNdQ', 'MSAlECsL', 'Q7', 'OPT1', 0),
('ANSUL6wfe', 'MSbHrzIU', 'Q11', 'OPT2', 1),
('ANSUlCtzc', 'MSjIxFsI', 'Q18', 'OPT2', 1),
('ANSULDEm1', 'MSKrk0hq', 'Q11', 'OPT3', 2),
('ANSUlijSa', 'MSGNt66z', 'Q24', 'OPT5', 0),
('ANSULLsSI', 'MSWCMBqh', 'Q12', 'OPT2', 1),
('ANSumvUOh', 'MSD2CdU4', 'Q9', 'OPT2', 1),
('ANSumwzpd', 'MSpuxB2A', 'Q20', 'OPT1', 0),
('ANSUO0KTd', 'MS7f7XHX', 'Q13', 'OPT2', 1),
('ANSuOAAwb', 'MSrzIxEi', 'Q23', 'OPT6', 1),
('ANSuoKB3W', 'MSxafMEG', 'Q26', 'OPT5', 0),
('ANSuOmysd', 'MSrzIxEi', 'Q19', 'OPT2', 1),
('ANSuPUa7q', 'MSmCH78J', 'Q5', 'OPT2', 1),
('ANSUQ3Q1j', 'MSwcZn3F', 'Q16', 'OPT1', 0),
('ANSuqgDWK', 'MSAlECsL', 'Q3', 'OPT3', 2),
('ANSUQigwO', 'MS8Ku3zf', 'Q15', 'OPT1', 0),
('ANSuQwLzv', 'MSjIxFsI', 'Q26', 'OPT5', 0),
('ANSuRaQfV', 'MSmCH78J', 'Q1', 'OPT2', 1),
('ANSus40A0', 'MSmCH78J', 'Q21', 'OPT1', 0),
('ANSutI8mi', 'MSUJDtjy', 'Q18', 'OPT3', 2),
('ANSUTlEwn', 'MSUVdR45', 'Q22', 'OPT5', 0),
('ANSuTLzgx', 'MSOiRHyL', 'Q7', 'OPT2', 1),
('ANSUtxe9z', 'MSWCMBqh', 'Q13', 'OPT2', 1),
('ANSUuKfHj', 'MSUJDtjy', 'Q26', 'OPT5', 0),
('ANSuuqSjp', 'MSD2CdU4', 'Q5', 'OPT3', 2),
('ANSUWcnds', 'MSmCH78J', 'Q13', 'OPT2', 1),
('ANSUwfgE9', 'MSmCH78J', 'Q17', 'OPT1', 0),
('ANSuwzjv2', 'MSpuxB2A', 'Q11', 'OPT2', 1),
('ANSuxEeWt', 'MSKBVoiE', 'Q5', 'OPT2', 1),
('ANSUxXhDl', 'MSrJv6lc', 'Q17', 'OPT1', 0),
('ANSuzGobJ', 'MSrJv6lc', 'Q24', 'OPT5', 0),
('ANSuZiPXk', 'MSwzvNE9', 'Q14', 'OPT2', 1),
('ANSv0GuW1', 'MS7uU47p', 'Q5', 'OPT1', 0),
('ANSV2hzzc', 'MSWzJzOu', 'Q5', 'OPT2', 1),
('ANSV3mTk5', 'MS79deyJ', 'Q25', 'OPT5', 0),
('ANSV5AoN2', 'MSkUYFSU', 'Q9', 'OPT2', 1),
('ANSv5lqh2', 'MSCcWA57', 'Q23', 'OPT5', 0),
('ANSv68Bzo', 'MSb1vGgr', 'Q23', 'OPT6', 1),
('ANSv6ybQw', 'MSGNt66z', 'Q3', 'OPT2', 1),
('ANSV8jxV3', 'MSkUYFSU', 'Q7', 'OPT1', 0),
('ANSV9tnUa', 'MSS6eh4N', 'Q24', 'OPT7', 2),
('ANSVAM7SX', 'MSLEeXDP', 'Q18', 'OPT2', 1),
('ANSVaXAa4', 'MS7T2bhr', 'Q8', 'OPT4', 3),
('ANSVBCJqO', 'MShKvqCr', 'Q4', 'OPT1', 0),
('ANSVBFCyd', 'MSUJDtjy', 'Q12', 'OPT1', 0),
('ANSvBGHpG', 'MS8DLldu', 'Q17', 'OPT1', 0),
('ANSvcV6Im', 'MS7f7XHX', 'Q19', 'OPT1', 0),
('ANSVDc2Lp', 'MSqHuFsF', 'Q20', 'OPT4', 3),
('ANSvdFGVw', 'MSOiRHyL', 'Q16', 'OPT2', 1),
('ANSVDHSA6', 'MSYZvsiu', 'Q20', 'OPT2', 1),
('ANSvEemwc', 'MSjIxFsI', 'Q10', 'OPT1', 0),
('ANSVEnVSi', 'MSxafMEG', 'Q19', 'OPT1', 0),
('ANSveTmcN', 'MST7153t', 'Q5', 'OPT2', 1),
('ANSvEYOdb', 'MSe8IKuy', 'Q17', 'OPT1', 0),
('ANSVFfgRM', 'MSuM6ldN', 'Q20', 'OPT3', 2),
('ANSVFfRZk', 'MS2GS0M4', 'Q21', 'OPT1', 0),
('ANSVFPvng', 'MSYkD4Nr', 'Q12', 'OPT2', 1),
('ANSvFQy6f', 'MS138aYF', 'Q16', 'OPT1', 0),
('ANSvG7Gkd', 'MS7uU47p', 'Q4', 'OPT1', 0),
('ANSVg7wQ7', 'MS1kbVC6', 'Q2', 'OPT1', 0),
('ANSVGKt0A', 'MSXxuKfh', 'Q1', 'OPT1', 0),
('ANSVGzdBB', 'MSmCH78J', 'Q11', 'OPT2', 1),
('ANSvhgoEL', 'MS79deyJ', 'Q22', 'OPT6', 1),
('ANSVhN870', 'MSuM6ldN', 'Q13', 'OPT3', 2),
('ANSvi91EZ', 'MS7T2bhr', 'Q10', 'OPT3', 2),
('ANSviuQ37', 'MSCcWA57', 'Q15', 'OPT1', 0),
('ANSvIwE6r', 'MSbHrzIU', 'Q6', 'OPT2', 1),
('ANSVjSaLy', 'MSUY57sP', 'Q15', 'OPT1', 0),
('ANSVjtC8U', 'MSX9aTK5', 'Q21', 'OPT1', 0),
('ANSVL3mAq', 'MSC84et8', 'Q14', 'OPT3', 2),
('ANSvlhgij', 'MSFQYvYL', 'Q21', 'OPT1', 0),
('ANSVlkbTA', 'MSuq7kIE', 'Q6', 'OPT3', 2),
('ANSVLmyXT', 'MSCcWA57', 'Q17', 'OPT1', 0),
('ANSvMA8Pt', 'MSj941zD', 'Q8', 'OPT4', 3),
('ANSVMjoRl', 'MS5oYfaH', 'Q25', 'OPT7', 2),
('ANSvMMtWr', 'MS7T2bhr', 'Q14', 'OPT1', 0),
('ANSvMNJ4b', 'MS8Zetbr', 'Q1', 'OPT2', 1),
('ANSvmNVoh', 'MS2E6rGI', 'Q25', 'OPT6', 1),
('ANSVmzCSJ', 'MS1kbVC6', 'Q1', 'OPT1', 0),
('ANSvn3j3Q', 'MSGTyAts', 'Q26', 'OPT7', 2),
('ANSVNaS6T', 'MSKBVoiE', 'Q22', 'OPT5', 0),
('ANSVnfAuY', 'MSGlWdke', 'Q8', 'OPT3', 2),
('ANSvNqTST', 'MSwcZn3F', 'Q21', 'OPT1', 0),
('ANSvO05AH', 'MSxafMEG', 'Q24', 'OPT6', 1),
('ANSvo1T7V', 'MS2S9Sh8', 'Q16', 'OPT4', 3),
('ANSVoHy6V', 'MSkUYFSU', 'Q19', 'OPT1', 0),
('ANSVpBMZJ', 'MSxafMEG', 'Q2', 'OPT1', 0),
('ANSVpDd1E', 'MShC7NUw', 'Q18', 'OPT4', 3),
('ANSvPJl6H', 'MSmLaR9I', 'Q11', 'OPT2', 1),
('ANSvpoIBH', 'MSUY57sP', 'Q4', 'OPT2', 1),
('ANSvpqrOx', 'MSrJv6lc', 'Q19', 'OPT2', 1),
('ANSVq0MNw', 'MSManaYB', 'Q1', 'OPT4', 3),
('ANSvqcVwV', 'MS53AquJ', 'Q22', 'OPT6', 1),
('ANSvQg9K5', 'MSrql4yA', 'Q3', 'OPT4', 3),
('ANSvQtdbx', 'MSC84et8', 'Q6', 'OPT2', 1),
('ANSVra1GZ', 'MS5xzf68', 'Q11', 'OPT1', 0),
('ANSvrjkFj', 'MSrql4yA', 'Q8', 'OPT4', 3),
('ANSVroedV', 'MSxAFfYH', 'Q16', 'OPT1', 0),
('ANSvrtzJM', 'MSEwAQL0', 'Q26', 'OPT5', 0),
('ANSVS2Ga0', 'MSuM6ldN', 'Q22', 'OPT7', 2),
('ANSVSFq85', 'MSX9aTK5', 'Q9', 'OPT2', 1),
('ANSvsGZHI', 'MSbHrzIU', 'Q4', 'OPT2', 1),
('ANSvSKmUK', 'MSCvjNTp', 'Q7', 'OPT1', 0),
('ANSVskO1E', 'MSYs8YjY', 'Q1', 'OPT2', 1),
('ANSVTOhBg', 'MSEDOwl4', 'Q24', 'OPT5', 0),
('ANSvuKjWp', 'MSmLaR9I', 'Q18', 'OPT2', 1),
('ANSVulfeW', 'MShKvqCr', 'Q19', 'OPT1', 0),
('ANSvuodLX', 'MShEdYT5', 'Q12', 'OPT2', 1),
('ANSVxT4jN', 'MSmLaR9I', 'Q21', 'OPT1', 0),
('ANSVXUn29', 'MSS6eh4N', 'Q2', 'OPT4', 3),
('ANSVYQloI', 'MSqHuFsF', 'Q5', 'OPT2', 1),
('ANSVZeY3m', 'MS1kbVC6', 'Q10', 'OPT2', 1),
('ANSVzn1nx', 'MSuM6ldN', 'Q19', 'OPT2', 1),
('ANSVzpeWt', 'MSUY57sP', 'Q25', 'OPT5', 0),
('ANSW18Zrs', 'MS2wWZvm', 'Q11', 'OPT2', 1),
('ANSW1F1lO', 'MS8Ku3zf', 'Q21', 'OPT4', 3),
('ANSW2uQ8j', 'MSpuxB2A', 'Q13', 'OPT2', 1),
('ANSw4o481', 'MSrJv6lc', 'Q26', 'OPT5', 0),
('ANSw5cc9E', 'MS6zWUZX', 'Q22', 'OPT5', 0),
('ANSw5EGZ4', 'MShKvqCr', 'Q1', 'OPT1', 0),
('ANSw6Lq0L', 'MSC84et8', 'Q20', 'OPT3', 2),
('ANSW7iywa', 'MS5oYfaH', 'Q1', 'OPT1', 0),
('ANSw8VAXq', 'MStavaQ6', 'Q23', 'OPT5', 0),
('ANSw9KiXT', 'MSqHuFsF', 'Q13', 'OPT3', 2),
('ANSWaEpwg', 'MSCvjNTp', 'Q5', 'OPT2', 1),
('ANSwaHKYe', 'MSe8IKuy', 'Q19', 'OPT1', 0),
('ANSWAvCGQ', 'MShivwr1', 'Q12', 'OPT1', 0),
('ANSWb4PJN', 'MS6zWUZX', 'Q8', 'OPT2', 1),
('ANSwBVGkp', 'MSuq7kIE', 'Q25', 'OPT6', 1),
('ANSWc4vnX', 'MSGlWdke', 'Q20', 'OPT1', 0),
('ANSWCvovX', 'MSj941zD', 'Q19', 'OPT4', 3),
('ANSwdH7rL', 'MS5xzf68', 'Q2', 'OPT1', 0),
('ANSwdka4V', 'MSrql4yA', 'Q13', 'OPT4', 3),
('ANSwdlx1L', 'MS1kbVC6', 'Q24', 'OPT5', 0),
('ANSWdMevA', 'MS7f7XHX', 'Q17', 'OPT1', 0),
('ANSWdRhGT', 'MSUY57sP', 'Q1', 'OPT2', 1),
('ANSwdVjPm', 'MSj941zD', 'Q26', 'OPT7', 2),
('ANSwe4IRx', 'MSX9aTK5', 'Q17', 'OPT1', 0),
('ANSwE5KCk', 'MS5xzf68', 'Q3', 'OPT1', 0),
('ANSwFBQhF', 'MSOiRHyL', 'Q12', 'OPT1', 0),
('ANSwFmQnS', 'MShC7NUw', 'Q11', 'OPT3', 2),
('ANSwfNhbc', 'MSWCMBqh', 'Q9', 'OPT1', 0),
('ANSwhhM4e', 'MSKBVoiE', 'Q1', 'OPT2', 1),
('ANSWhMmZ9', 'MSX9aTK5', 'Q12', 'OPT1', 0),
('ANSwICtqO', 'MSuq7kIE', 'Q3', 'OPT1', 0),
('ANSwIi4d5', 'MSrsK5M0', 'Q14', 'OPT2', 1),
('ANSwIZHzb', 'MS5oYfaH', 'Q10', 'OPT1', 0),
('ANSwjNTdF', 'MS8DLldu', 'Q5', 'OPT2', 1),
('ANSWjYLWM', 'MSuM6ldN', 'Q1', 'OPT2', 1),
('ANSWjyRLA', 'MS7f7XHX', 'Q24', 'OPT6', 1),
('ANSWLgStj', 'MS138aYF', 'Q18', 'OPT4', 3),
('ANSWM3KwS', 'MSxAFfYH', 'Q26', 'OPT5', 0),
('ANSWm4vTv', 'MSOiRHyL', 'Q1', 'OPT2', 1),
('ANSWm7B9h', 'MSYkD4Nr', 'Q26', 'OPT6', 1),
('ANSWN8WT0', 'MSUVdR45', 'Q4', 'OPT1', 0),
('ANSWnA0xF', 'MSMzDgKm', 'Q3', 'OPT2', 1),
('ANSwNc5t1', 'MSCJQ7km', 'Q6', 'OPT4', 3),
('ANSwNKpA4', 'MS5aBjQf', 'Q14', 'OPT1', 0),
('ANSWOQ5ho', 'MS138aYF', 'Q17', 'OPT1', 0),
('ANSWpKixH', 'MStavaQ6', 'Q15', 'OPT2', 1),
('ANSwr9zqt', 'MSqHuFsF', 'Q15', 'OPT3', 2),
('ANSwRtO7T', 'MSYkD4Nr', 'Q11', 'OPT2', 1),
('ANSwS5hvN', 'MShivwr1', 'Q13', 'OPT2', 1),
('ANSWS9kUU', 'MSKrk0hq', 'Q14', 'OPT2', 1),
('ANSWtJ6tD', 'MS6Hgbj4', 'Q16', 'OPT1', 0),
('ANSWtO5i4', 'MSH2zQ4z', 'Q24', 'OPT5', 0),
('ANSWUhlHy', 'MSYkD4Nr', 'Q2', 'OPT1', 0),
('ANSwUM496', 'MSYkD4Nr', 'Q14', 'OPT2', 1),
('ANSwuoiLn', 'MS5xzf68', 'Q4', 'OPT1', 0),
('ANSWuvxT4', 'MStavaQ6', 'Q21', 'OPT1', 0),
('ANSwvaoiH', 'MSXxuKfh', 'Q6', 'OPT2', 1),
('ANSwVz5Hv', 'MSFQYvYL', 'Q16', 'OPT2', 1),
('ANSwWZI54', 'MSMzDgKm', 'Q26', 'OPT5', 0),
('ANSwxiopd', 'MSB9hlkR', 'Q13', 'OPT1', 0),
('ANSwy4nFQ', 'MS6Hgbj4', 'Q10', 'OPT1', 0),
('ANSwzgJUr', 'MSwzvNE9', 'Q10', 'OPT2', 1),
('ANSWZnUMZ', 'MSmCH78J', 'Q23', 'OPT5', 0),
('ANSX2G19d', 'MSXxuKfh', 'Q2', 'OPT2', 1),
('ANSX3lNR0', 'MSJCXCcq', 'Q12', 'OPT1', 0),
('ANSx5aARn', 'MSYs8YjY', 'Q19', 'OPT2', 1),
('ANSx6hpcE', 'MSH2zQ4z', 'Q22', 'OPT5', 0),
('ANSx6QpzD', 'MSJCXCcq', 'Q7', 'OPT1', 0),
('ANSX6WDZK', 'MStavaQ6', 'Q22', 'OPT5', 0),
('ANSx7od1R', 'MSUY57sP', 'Q9', 'OPT1', 0),
('ANSX7pDg9', 'MSeqrFEz', 'Q24', 'OPT5', 0),
('ANSx7wfqL', 'MS2S9Sh8', 'Q17', 'OPT4', 3),
('ANSX85PJx', 'MSX9aTK5', 'Q8', 'OPT1', 0),
('ANSX8Xvz5', 'MSLEeXDP', 'Q7', 'OPT1', 0),
('ANSX9Jgyh', 'MSGTyAts', 'Q20', 'OPT2', 1),
('ANSxaJmxc', 'MSpuxB2A', 'Q3', 'OPT1', 0),
('ANSXbbDKf', 'MSj5xUgK', 'Q14', 'OPT1', 0),
('ANSXbp7L4', 'MSMzDgKm', 'Q24', 'OPT7', 2),
('ANSXcdtWB', 'MSuq7kIE', 'Q10', 'OPT1', 0),
('ANSXCo6VF', 'MSj5xUgK', 'Q6', 'OPT2', 1),
('ANSxCov1n', 'MSYkD4Nr', 'Q24', 'OPT5', 0),
('ANSxCPAC2', 'MS6Hgbj4', 'Q18', 'OPT1', 0),
('ANSxcW95e', 'MSe8IKuy', 'Q21', 'OPT1', 0),
('ANSXDGtu2', 'MSrsK5M0', 'Q19', 'OPT1', 0),
('ANSxDMCNk', 'MSYkD4Nr', 'Q5', 'OPT2', 1),
('ANSxDNDuT', 'MSeqrFEz', 'Q1', 'OPT2', 1),
('ANSXdYqGv', 'MSMzDgKm', 'Q19', 'OPT1', 0),
('ANSxE1yKH', 'MSWCMBqh', 'Q6', 'OPT3', 2),
('ANSxEn2EX', 'MST7153t', 'Q3', 'OPT1', 0),
('ANSxEYy91', 'MSrJv6lc', 'Q13', 'OPT2', 1),
('ANSxfBv1i', 'MSn52ZdO', 'Q26', 'OPT5', 0),
('ANSXfYUNo', 'MS53AquJ', 'Q21', 'OPT2', 1),
('ANSXgGeuG', 'MSrzIxEi', 'Q18', 'OPT3', 2),
('ANSxGpSbn', 'MSMzDgKm', 'Q18', 'OPT3', 2),
('ANSxh3iUq', 'MSXxuKfh', 'Q14', 'OPT3', 2),
('ANSxhaEpH', 'MS5oYfaH', 'Q14', 'OPT2', 1),
('ANSxhiXIq', 'MS5xzf68', 'Q19', 'OPT1', 0),
('ANSxi0rII', 'MSrql4yA', 'Q1', 'OPT4', 3),
('ANSxIa8H0', 'MSGlWdke', 'Q9', 'OPT4', 3),
('ANSXKUZHb', 'MSXxuKfh', 'Q26', 'OPT6', 1),
('ANSXnd5LZ', 'MSWzJzOu', 'Q9', 'OPT2', 1),
('ANSxnEnGU', 'MSOTfXXF', 'Q1', 'OPT2', 1),
('ANSxNUlvw', 'MS79deyJ', 'Q20', 'OPT2', 1),
('ANSxnXESP', 'MSmLaR9I', 'Q14', 'OPT2', 1),
('ANSXORYMF', 'MS8DLldu', 'Q4', 'OPT1', 0),
('ANSXozFa6', 'MSn52ZdO', 'Q12', 'OPT1', 0),
('ANSxqDweW', 'MSmLaR9I', 'Q6', 'OPT2', 1),
('ANSxqrUAC', 'MSAlECsL', 'Q20', 'OPT2', 1),
('ANSXrfw4F', 'MSrsK5M0', 'Q15', 'OPT2', 1),
('ANSXsmmfd', 'MSMzDgKm', 'Q11', 'OPT4', 3),
('ANSXSNURP', 'MSZhKZEA', 'Q6', 'OPT4', 3),
('ANSxT06XF', 'MSEDOwl4', 'Q1', 'OPT3', 2),
('ANSXT6dGb', 'MSuM6ldN', 'Q9', 'OPT4', 3),
('ANSxTIjAT', 'MSCvjNTp', 'Q25', 'OPT7', 2),
('ANSXU7x3U', 'MS5xzf68', 'Q7', 'OPT1', 0),
('ANSxutrt4', 'MSYZvsiu', 'Q3', 'OPT2', 1),
('ANSXvbbSq', 'MS6zWUZX', 'Q3', 'OPT4', 3),
('ANSxw0EHP', 'MSn52ZdO', 'Q11', 'OPT4', 3),
('ANSXwL8qr', 'MS2E6rGI', 'Q11', 'OPT4', 3),
('ANSxXjk1E', 'MS138aYF', 'Q1', 'OPT2', 1),
('ANSXxZmfZ', 'MSUJDtjy', 'Q3', 'OPT3', 2),
('ANSxY7ALP', 'MSZhKZEA', 'Q22', 'OPT7', 2),
('ANSXynlEw', 'MSQq566a', 'Q14', 'OPT2', 1),
('ANSXYQ5Wv', 'MSrJv6lc', 'Q22', 'OPT5', 0),
('ANSXyQEdT', 'MS6zWUZX', 'Q16', 'OPT2', 1),
('ANSXz9tk7', 'MSMSWRrW', 'Q24', 'OPT5', 0),
('ANSxzKaB7', 'MSQq566a', 'Q22', 'OPT5', 0),
('ANSy1fzyd', 'MSrzIxEi', 'Q3', 'OPT3', 2),
('ANSy3HZZ3', 'MS2S9Sh8', 'Q9', 'OPT2', 1),
('ANSy3zwlm', 'MSEwAQL0', 'Q17', 'OPT2', 1),
('ANSY5gNiQ', 'MSj5xUgK', 'Q13', 'OPT2', 1),
('ANSYbs2sF', 'MSJCXCcq', 'Q11', 'OPT2', 1),
('ANSybtKL7', 'MST7153t', 'Q23', 'OPT5', 0),
('ANSyBUNMn', 'MSGNt66z', 'Q4', 'OPT1', 0),
('ANSYCdMZE', 'MSjSGqfF', 'Q1', 'OPT3', 2),
('ANSyCdVit', 'MSS6eh4N', 'Q15', 'OPT3', 2),
('ANSYcnbaZ', 'MSj5xUgK', 'Q16', 'OPT2', 1),
('ANSyDEove', 'MS2S9Sh8', 'Q25', 'OPT6', 1),
('ANSYDR8cd', 'MSKBVoiE', 'Q2', 'OPT1', 0),
('ANSYDtOMm', 'MSwTXozI', 'Q3', 'OPT1', 0),
('ANSYEwVwH', 'MSrql4yA', 'Q7', 'OPT4', 3),
('ANSyeXUqs', 'MSXxuKfh', 'Q10', 'OPT3', 2),
('ANSygiU6p', 'MSn52ZdO', 'Q2', 'OPT4', 3),
('ANSYgl1Pp', 'MS1kbVC6', 'Q6', 'OPT2', 1),
('ANSyHBpk1', 'MSD2CdU4', 'Q23', 'OPT6', 1),
('ANSyHm4T2', 'MS5oYfaH', 'Q19', 'OPT2', 1),
('ANSYHnGBC', 'MS8Ku3zf', 'Q5', 'OPT4', 3),
('ANSYI4prf', 'MS8DLldu', 'Q15', 'OPT1', 0),
('ANSYidfra', 'MSLEeXDP', 'Q1', 'OPT2', 1),
('ANSYiiBTZ', 'MSj941zD', 'Q14', 'OPT3', 2),
('ANSyiQkIX', 'MSjSGqfF', 'Q6', 'OPT2', 1),
('ANSYIvsFW', 'MSpuxB2A', 'Q2', 'OPT4', 3),
('ANSYIX5kA', 'MSwzvNE9', 'Q7', 'OPT3', 2),
('ANSyJ9lFF', 'MSYs8YjY', 'Q16', 'OPT2', 1),
('ANSyJCKOB', 'MSjSGqfF', 'Q2', 'OPT2', 1),
('ANSyje6IJ', 'MSWzJzOu', 'Q18', 'OPT2', 1),
('ANSyJoSij', 'MSwTXozI', 'Q6', 'OPT1', 0),
('ANSYKcmH0', 'MSX9aTK5', 'Q10', 'OPT1', 0),
('ANSyl6vhc', 'MSqHuFsF', 'Q1', 'OPT2', 1),
('ANSYlmUyU', 'MS5aBjQf', 'Q26', 'OPT6', 1),
('ANSym9l29', 'MSuM6ldN', 'Q8', 'OPT3', 2),
('ANSYMDLee', 'MSCvjNTp', 'Q15', 'OPT2', 1),
('ANSyMKLVz', 'MSGTyAts', 'Q24', 'OPT6', 1),
('ANSyMpuyf', 'MSn52ZdO', 'Q21', 'OPT3', 2),
('ANSyNAu8d', 'MSxAFfYH', 'Q10', 'OPT1', 0),
('ANSYndk5D', 'MSuM6ldN', 'Q6', 'OPT4', 3),
('ANSYNh6wa', 'MSeqrFEz', 'Q17', 'OPT1', 0),
('ANSYnYm9T', 'MS1kbVC6', 'Q5', 'OPT1', 0),
('ANSYp0VKa', 'MSkUYFSU', 'Q26', 'OPT5', 0),
('ANSyP8tyo', 'MSuM6ldN', 'Q17', 'OPT4', 3),
('ANSYPBW0F', 'MSZhKZEA', 'Q12', 'OPT4', 3),
('ANSYPCRZR', 'MSEwAQL0', 'Q8', 'OPT3', 2),
('ANSypROsZ', 'MSOiRHyL', 'Q14', 'OPT4', 3),
('ANSYqAFgw', 'MS138aYF', 'Q6', 'OPT2', 1),
('ANSYQkQzC', 'MSB9hlkR', 'Q15', 'OPT1', 0),
('ANSyqrfQC', 'MSGNt66z', 'Q14', 'OPT2', 1),
('ANSyqWruY', 'MSwTXozI', 'Q18', 'OPT1', 0),
('ANSyRCIqF', 'MSC84et8', 'Q25', 'OPT7', 2),
('ANSyrfnmt', 'MSjIxFsI', 'Q13', 'OPT2', 1),
('ANSyrNdZ5', 'MS79deyJ', 'Q14', 'OPT3', 2),
('ANSYroYxm', 'MSbHrzIU', 'Q3', 'OPT2', 1),
('ANSYrRRdf', 'MShKvqCr', 'Q7', 'OPT1', 0),
('ANSyrSHdL', 'MSMzDgKm', 'Q8', 'OPT2', 1),
('ANSYs6DyU', 'MSxafMEG', 'Q14', 'OPT1', 0),
('ANSYs7woq', 'MS2GS0M4', 'Q25', 'OPT5', 0),
('ANSySCVjt', 'MSEDOwl4', 'Q7', 'OPT1', 0),
('ANSySnDRS', 'MSn52ZdO', 'Q25', 'OPT5', 0),
('ANSYSXQJP', 'MSUY57sP', 'Q2', 'OPT2', 1),
('ANSYThM4S', 'MSwcZn3F', 'Q7', 'OPT1', 0),
('ANSYthQAn', 'MSD2CdU4', 'Q11', 'OPT2', 1),
('ANSYTqLEM', 'MSGNt66z', 'Q13', 'OPT1', 0),
('ANSYU2elM', 'MSKrk0hq', 'Q26', 'OPT6', 1),
('ANSyu4oXo', 'MSKBVoiE', 'Q12', 'OPT2', 1),
('ANSYu5ceC', 'MS7T2bhr', 'Q15', 'OPT4', 3),
('ANSYu7vYO', 'MSrJv6lc', 'Q12', 'OPT2', 1),
('ANSYuycTd', 'MSUVdR45', 'Q15', 'OPT2', 1),
('ANSyV3dDw', 'MSj5xUgK', 'Q3', 'OPT2', 1),
('ANSyVgjVS', 'MSB9hlkR', 'Q26', 'OPT5', 0),
('ANSywDnoG', 'MSb1vGgr', 'Q1', 'OPT3', 2),
('ANSyWvgaV', 'MS5xzf68', 'Q1', 'OPT1', 0),
('ANSYXa3lh', 'MSZhKZEA', 'Q24', 'OPT6', 1),
('ANSyXKRiJ', 'MS2E6rGI', 'Q21', 'OPT3', 2),
('ANSyXxcAw', 'MSkUYFSU', 'Q12', 'OPT2', 1),
('ANSyy9ZlP', 'MS2E6rGI', 'Q10', 'OPT3', 2),
('ANSYYNIup', 'MSOTfXXF', 'Q5', 'OPT3', 2),
('ANSYYposr', 'MSH2zQ4z', 'Q13', 'OPT2', 1),
('ANSYzrPK2', 'MSYs8YjY', 'Q13', 'OPT4', 3),
('ANSyzX0JP', 'MSkUYFSU', 'Q23', 'OPT5', 0),
('ANSZ0JrNV', 'MSC84et8', 'Q13', 'OPT2', 1),
('ANSZ1cEJx', 'MSmLaR9I', 'Q25', 'OPT5', 0),
('ANSz1vnNT', 'MSxafMEG', 'Q12', 'OPT1', 0),
('ANSz1W9f7', 'MSwzvNE9', 'Q18', 'OPT2', 1),
('ANSz1y1tv', 'MStavaQ6', 'Q14', 'OPT1', 0),
('ANSz1ZnGU', 'MSrzIxEi', 'Q22', 'OPT6', 1),
('ANSZ3LUBD', 'MS6Hgbj4', 'Q3', 'OPT2', 1),
('ANSz4IvaU', 'MSj5xUgK', 'Q4', 'OPT2', 1),
('ANSz5zPiW', 'MS5aBjQf', 'Q2', 'OPT1', 0),
('ANSz6rkqY', 'MS138aYF', 'Q5', 'OPT2', 1),
('ANSZ73GB8', 'MSHVECsr', 'Q4', 'OPT1', 0),
('ANSz88S5k', 'MSKrk0hq', 'Q10', 'OPT1', 0),
('ANSz9gQJs', 'MSjSGqfF', 'Q4', 'OPT2', 1),
('ANSZA9cEL', 'MSuM6ldN', 'Q4', 'OPT3', 2),
('ANSZAFFhn', 'MSmCH78J', 'Q4', 'OPT1', 0),
('ANSzAFpMg', 'MSwzvNE9', 'Q16', 'OPT3', 2),
('ANSZaKamh', 'MSZhKZEA', 'Q19', 'OPT3', 2),
('ANSZAKVrV', 'MSYkD4Nr', 'Q15', 'OPT2', 1),
('ANSZaXT2n', 'MSxafMEG', 'Q23', 'OPT5', 0),
('ANSzBmC2p', 'MSCcWA57', 'Q10', 'OPT1', 0),
('ANSZCUq0O', 'MS7f7XHX', 'Q10', 'OPT1', 0),
('ANSzd4tRW', 'MSC84et8', 'Q8', 'OPT3', 2),
('ANSZdHo2Z', 'MS5aBjQf', 'Q17', 'OPT1', 0),
('ANSzdTGDL', 'MST7153t', 'Q20', 'OPT1', 0),
('ANSze9LTZ', 'MST7153t', 'Q26', 'OPT6', 1),
('ANSzEj9aD', 'MSUJDtjy', 'Q23', 'OPT5', 0),
('ANSzfC8Ff', 'MSj941zD', 'Q21', 'OPT4', 3),
('ANSZFR5Ve', 'MSMzDgKm', 'Q16', 'OPT2', 1),
('ANSZftFwW', 'MS2S9Sh8', 'Q1', 'OPT4', 3),
('ANSzgjeUw', 'MSUJDtjy', 'Q15', 'OPT4', 3),
('ANSzhhnYU', 'MSUVdR45', 'Q26', 'OPT5', 0),
('ANSzI6qJt', 'MSwTXozI', 'Q1', 'OPT1', 0),
('ANSzIPEY9', 'MSKBVoiE', 'Q23', 'OPT5', 0),
('ANSziX440', 'MSCcWA57', 'Q26', 'OPT5', 0),
('ANSZJ9pDN', 'MSGNt66z', 'Q7', 'OPT1', 0),
('ANSzkH5jx', 'MSmCH78J', 'Q15', 'OPT1', 0),
('ANSZkIIyO', 'MSEwAQL0', 'Q23', 'OPT5', 0),
('ANSZKvKjG', 'MSwzvNE9', 'Q21', 'OPT2', 1),
('ANSZlOJok', 'MS7f7XHX', 'Q12', 'OPT1', 0),
('ANSzloqR7', 'MS6Hgbj4', 'Q24', 'OPT7', 2),
('ANSZMvZ4I', 'MSH2zQ4z', 'Q26', 'OPT5', 0),
('ANSzmwMRZ', 'MShEdYT5', 'Q26', 'OPT5', 0),
('ANSzMzxk0', 'MSWCMBqh', 'Q14', 'OPT2', 1),
('ANSzpwLfF', 'MSUVdR45', 'Q24', 'OPT6', 1),
('ANSzqyaWI', 'MSmCH78J', 'Q6', 'OPT2', 1),
('ANSZr3yvB', 'MSB9hlkR', 'Q20', 'OPT1', 0),
('ANSZree6e', 'MS6Hgbj4', 'Q11', 'OPT1', 0),
('ANSZRQcAC', 'MSOTfXXF', 'Q9', 'OPT2', 1),
('ANSzS7iXZ', 'MSEwAQL0', 'Q24', 'OPT5', 0),
('ANSZSIyDK', 'MSJCXCcq', 'Q20', 'OPT1', 0),
('ANSzSv707', 'MSYZvsiu', 'Q12', 'OPT2', 1),
('ANSZtrste', 'MSjIxFsI', 'Q15', 'OPT1', 0),
('ANSzuE5Qg', 'MSe8IKuy', 'Q12', 'OPT1', 0),
('ANSZvDjzR', 'MSEwAQL0', 'Q22', 'OPT5', 0),
('ANSZvrTn7', 'MSn52ZdO', 'Q5', 'OPT3', 2),
('ANSzVuZLn', 'MSUJDtjy', 'Q24', 'OPT7', 2),
('ANSZwHEl7', 'MS5xzf68', 'Q23', 'OPT5', 0),
('ANSzwywtH', 'MSCcWA57', 'Q11', 'OPT1', 0),
('ANSZxPC51', 'MS7uU47p', 'Q26', 'OPT5', 0),
('ANSzxuh3S', 'MSYZvsiu', 'Q8', 'OPT2', 1),
('ANSZxx6XA', 'MSjSGqfF', 'Q8', 'OPT1', 0),
('ANSZYBVLT', 'MS6zWUZX', 'Q20', 'OPT2', 1),
('ANSZyGR1q', 'MSS6eh4N', 'Q25', 'OPT6', 1),
('ANSZYgUBm', 'MSxAFfYH', 'Q8', 'OPT3', 2),
('ANSzyKFIb', 'MS79deyJ', 'Q9', 'OPT2', 1),
('ANSZYolBF', 'MSWCMBqh', 'Q15', 'OPT2', 1),
('ANSZZA44w', 'MSe8IKuy', 'Q18', 'OPT2', 1);

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `messages`
--

INSERT INTO `messages` (`id`, `message`) VALUES
('m1', 'You\'re getting closer, keep up the good work.'),
('m2', 'Don\'t stop now, you\'re doing great.\r\n'),
('m3', 'Time for the next segment!<br>\r\n<i>One step closer to your results</i>\r\n'),
('m4', 'Thank you for trusting us with your mental wellbeing. \r\n');

-- --------------------------------------------------------

--
-- Table structure for table `option_mapping`
--

CREATE TABLE `option_mapping` (
  `id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `option_name` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `score` int(1) NOT NULL,
  `date_added` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `option_mapping`
--

INSERT INTO `option_mapping` (`id`, `option_name`, `score`, `date_added`) VALUES
('OPT1', 'Never', 0, '2023-03-05 07:52:35'),
('OPT2', 'Sometimes', 1, '2023-03-05 07:53:55'),
('OPT3', 'Often', 2, '2023-03-05 07:53:55'),
('OPT4', 'Always', 3, '2023-03-05 07:53:55'),
('OPT5', 'Satisfied', 0, '2023-03-05 07:53:55'),
('OPT6', 'Partly Satisfied', 1, '2023-03-05 07:53:55'),
('OPT7', 'Not at all', 2, '2023-03-05 07:53:55');

-- --------------------------------------------------------

--
-- Table structure for table `questions`
--

CREATE TABLE `questions` (
  `id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `question` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sequence` int(11) NOT NULL,
  `option1` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `option2` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `option3` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `option4` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_added` timestamp NOT NULL DEFAULT current_timestamp(),
  `show_message` text COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `questions`
--

INSERT INTO `questions` (`id`, `question`, `type`, `sequence`, `option1`, `option2`, `option3`, `option4`, `date_added`, `show_message`) VALUES
('Q1', 'I struggle to relax.', 'stress', 1, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-06 20:14:01', ''),
('Q10', 'I felt that I had nothing to look forward to.', 'depression', 10, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q11', 'I found myself getting agitated ', 'stress', 11, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q12', 'I found it difficult to relax', 'stress', 12, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q13', 'I felt sad ', 'stress', 13, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q14', 'I was easily frustrated when interrupted.', 'stress', 14, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'm2'),
('Q15', 'I felt I was close to panic.', 'anxiety', 15, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q16', 'I was unable to become enthusiastic about anything', 'depression', 16, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q17', 'I felt I wasn\'t worth much as a person.', 'depression', 17, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q18', 'I felt easily irritated.', 'stress', 18, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q19', 'I felt my heart beating fast even when I was not physically active (e.g. sense of heart rate increase, heart missing a beat)', 'anxiety', 19, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q2', 'I was aware of the dryness of my mouth.', 'anxiety', 2, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q20', 'I felt scared without any good reason', 'anxiety', 20, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q21', 'I felt that life was meaningless', 'depression', 21, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q22', 'How satisfied are you with your self growth and development?', 'cause', 22, 'OPT5', 'OPT6', 'OPT7', '', '2023-03-05 07:49:36', 'm3'),
('Q23', 'How satisfied are you with your Family life?', 'cause', 23, 'OPT5', 'OPT6', 'OPT7', '', '2023-03-05 07:49:36', 'no'),
('Q24', 'Are you satisfied with your social life?', 'cause', 24, 'OPT5', 'OPT6', 'OPT7', '', '2023-03-05 07:49:36', 'no'),
('Q25', 'Do you feel that you have/had a satisfactory romantic relationship', 'cause', 25, 'OPT5', 'OPT6', 'OPT7', '', '2023-03-05 07:49:36', 'no'),
('Q26', 'Are you satisfied with your educational and professional achievements? ', 'cause', 26, 'OPT5', 'OPT6', 'OPT7', '', '2023-03-05 07:49:36', 'm4'),
('Q3', 'I can\'t feel any positivity.', 'depression ', 3, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q4', 'I experienced breathing difficulty (e.g. excessively rapid breathing, breathlessness in the absence of physical exertion)', 'anxiety ', 4, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q5', 'I lack motivation to do things.', 'depression ', 5, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q6', 'I tended to over-react to situations.', 'stress ', 6, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q7', 'I experienced trembling (e.g. in the hands).', 'anxiety', 7, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'm1'),
('Q8', 'I feel anxious.', 'stress ', 8, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no'),
('Q9', 'I was worried about situations in which I might panic and make a fool of myself', 'anxiety', 9, 'OPT1', 'OPT2', 'OPT3', 'OPT4', '2023-03-05 07:49:36', 'no');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(12) COLLATE utf8mb4_unicode_ci NOT NULL,
  `gender` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `language` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `time_of_registration` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `email`, `phone`, `gender`, `language`, `time_of_registration`) VALUES
('MS138aYF', 'Sonali kashinath Solkar ', '<EMAIL>', '8657642309', 'female', 'hi', '2023-03-10 08:07:03'),
('MS1kbVC6', 'Neelam Mehul Upadhyay', '<EMAIL>', '9869112778', 'female', 'hi', '2023-03-10 07:59:19'),
('MS2E6rGI', 'Deep', '<EMAIL>', '8193080485', 'female', 'en', '2023-03-10 11:13:31'),
('MS2GS0M4', 'Namrata Sunil Parab ', '<EMAIL>', '9833812590', 'female', 'en', '2023-03-10 09:59:53'),
('MS2S9Sh8', 'Tejal Manish Bamania', '<EMAIL>', '7021788529', 'female', 'en', '2023-03-10 07:10:16'),
('MS2V6Hhl', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-04 21:31:29'),
('MS2wWZvm', 'Sneha meghsham shraddha', '<EMAIL>', '9892738146', 'female', 'en', '2023-03-10 07:06:33'),
('MS53AquJ', 'deepa kapoor', '<EMAIL>', '9967551065', 'female', 'en', '2023-03-10 06:56:26'),
('MS5aBjQf', 'Anuja ', '<EMAIL>', '8788742429', 'female', 'en', '2023-03-10 13:49:08'),
('MS5oYfaH', 'kd tesing ', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 19:47:04'),
('MS5xzf68', 'XYZ', '<EMAIL>', '00', 'female', 'en', '2023-03-16 07:29:39'),
('MS6Hgbj4', 'Tejaswini Agre Shradha', '<EMAIL>', '9145712523', 'female', 'en', '2023-03-10 06:37:09'),
('MS6zWUZX', 'Rahul pillai', '<EMAIL>', '8108013596', 'male', 'en', '2023-03-10 11:59:42'),
('MS79deyJ', 'Deepika Suvarna', '<EMAIL>', '9892161367', 'female', 'en', '2023-03-10 07:16:25'),
('MS7f7XHX', 'Maya shankbhagar shraddha', '', '9137531813', 'female', 'en', '2023-03-10 09:47:19'),
('MS7R05qv', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 15:18:13'),
('MS7T2bhr', 'Kinjal final demo', '<EMAIL>', '8433805514', 'female', 'en', '2023-03-10 04:41:58'),
('MS7uU47p', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-17 04:54:20'),
('MS7Vtamd', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 09:34:55'),
('MS8DLldu', 'Akshita', '', '8433805514', 'female', 'en', '2023-03-10 08:10:06'),
('MS8Ku3zf', 'Raju mehta', '<EMAIL>', '9819222063', 'male', 'en', '2023-03-10 07:05:42'),
('MS8Zetbr', 'Khushmita m jain', '<EMAIL>', '9967340340', 'female', 'en', '2023-03-10 06:55:02'),
('MSAlECsL', 'Bhavin shah', '<EMAIL>', '7666421879', 'male', 'en', '2023-03-10 09:11:23'),
('MSaqqKXP', 'kinjal', '<EMAIL>', '9820884481', 'female', 'eng', '2023-03-09 13:28:50'),
('MSb1vGgr', 'Pushpa Thakkar', '', '7977747374', 'female', 'en', '2023-03-10 12:00:09'),
('MSB9hlkR', 'Parvati Subhash kadam ', '<EMAIL>', '9322880517', 'female', 'en', '2023-03-10 07:51:12'),
('MSBdvZ5R', 'khubilala', '<EMAIL>', '8356820082', 'male', 'hi', '2023-03-10 06:34:34'),
('MSbHrzIU', 'Bhakti Dilip Joshi', '<EMAIL>', '8879187940', 'female', 'en', '2023-03-10 12:07:22'),
('MSc4MwjD', 'parvati', '', '9322880517', 'female', 'hi', '2023-03-10 07:46:34'),
('MSC84et8', 'Manasvi Vikas Vaity', '<EMAIL>', '9930737778', 'female', 'en', '2023-03-09 14:50:42'),
('MSCcWA57', 'Poonam gurav', '<EMAIL>', '8451859596', 'female', 'en', '2023-03-10 07:12:28'),
('MSCJQ7km', 'Dharmesh Vinod Jain', '<EMAIL>', '7021840314', 'male', 'en', '2023-03-10 12:52:32'),
('MScRhQqn', 'Riya Rajendra Gurav shraddha ', '<EMAIL>', '7208248407', 'female', 'hi', '2023-03-10 12:06:32'),
('MSCvjNTp', 'Nidhi Kothari', '<EMAIL>', '9619046688', 'female', 'en', '2023-03-10 10:19:43'),
('MSD2CdU4', 'kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 14:54:36'),
('MSdDb13E', 'kinjal', '<EMAIL>', '9820884841', 'female', 'eng', '2023-03-08 05:06:46'),
('MSdO9BfU', 'Sable Rupali Eknath', '<EMAIL>', '8788912544', 'female', 'hi', '2023-03-10 09:53:52'),
('MSe8IKuy', 'khubilala', '', '8433805514', 'male', 'en', '2023-03-10 06:35:31'),
('MSEDOwl4', 'Ashwini Thokale Shraddha ', '<EMAIL>', '8356091906', 'female', 'hi', '2023-03-10 10:32:59'),
('MSeqrFEz', 'Snehalata Thorat ', '<EMAIL>', '9967883398', 'female', 'en', '2023-03-10 08:29:16'),
('MSEwAQL0', 'MANISH BAMANIA ', '<EMAIL>', '9987799763', 'male', 'en', '2023-03-10 07:29:35'),
('MSEWyoBU', 'jain ', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 17:50:11'),
('MSFm3Gji', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 15:21:59'),
('MSFQYvYL', 'Banabai shraddha', '', '8433805514', 'female', 'en', '2023-03-10 06:17:55'),
('MSGlWdke', 'Aarushi Bamania', '<EMAIL>', '7977860160', 'female', 'en', '2023-03-10 07:09:46'),
('MSgMeQ3N', 'Shruti Santosh temkar shraddha', '<EMAIL>', '9930515314', 'female', 'en', '2023-03-10 10:13:30'),
('MSGmMStj', 'Kunal ', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 05:13:41'),
('MSGNt66z', 'Nandini Shah', '<EMAIL>', '9821118610', 'female', 'en', '2023-03-10 09:30:33'),
('MSGPR5KO', 'kunal dawda', '<EMAIL>', '9172267465', 'female', 'eng', '2023-03-04 15:27:05'),
('MSGTyAts', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 15:22:57'),
('MSH2zQ4z', 'Sheela ullas heere shraddha', '', '7039583133', 'female', 'en', '2023-03-10 07:39:20'),
('MShC7NUw', 'Barkha', '<EMAIL>', '9870453130', 'female', 'en', '2023-03-10 09:14:04'),
('MShEdYT5', 'Anjali Anil bhalerao ', '<EMAIL>', '7039682061', 'female', 'en', '2023-03-10 07:19:33'),
('MShivwr1', 'Geeta Aiya ', '', '9833244667', 'female', 'hi', '2023-03-10 16:15:11'),
('MShKvqCr', 'Sunita Bharat nair', '', '8383996223', 'female', 'en', '2023-03-10 08:06:51'),
('MSHVECsr', 'Ravindra Rajaram Deshmukh ', '<EMAIL>', '9321711116', 'male', 'hi', '2023-03-10 07:15:37'),
('MSi08nGl', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-10 10:28:28'),
('MSIvGcST', 'shweta', '<EMAIL>', '00', 'female', 'en', '2023-03-17 10:24:29'),
('MSj5xUgK', 'Satish Kumar ', '<EMAIL>', '9820673147', 'male', 'en', '2023-03-10 07:19:57'),
('MSj941zD', 'Rudri', '<EMAIL>', '9769763054', 'female', 'en', '2023-03-11 06:54:52'),
('MSjBMdqf', 'Raju mehta', '<EMAIL>', '9819222063', 'male', 'hi', '2023-03-10 07:04:53'),
('MSJCXCcq', 'Priya More', '<EMAIL>', '7387410706', 'female', 'en', '2023-03-10 09:59:48'),
('MSjdi27k', 'kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 17:53:13'),
('MSjIxFsI', 'Riya Rajendra Gurav ', '<EMAIL>', '7208248407', 'female', 'hi', '2023-03-10 11:12:01'),
('MSjSGqfF', 'Dharmik', '<EMAIL>', '7666868686', 'male', 'en', '2023-03-11 06:24:09'),
('MSKBVoiE', 'Shruti Santosh temkar shraddha', '<EMAIL>', '9930515314', 'female', 'hi', '2023-03-10 10:15:00'),
('MSKoJ0hO', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-04 21:26:55'),
('MSKrk0hq', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-10 09:47:00'),
('MSkUYFSU', 'datta shankar kadam', '<EMAIL>', '9867966006', 'male', 'hi', '2023-03-10 07:22:26'),
('MSLEeXDP', 'Tejal Upadhyay ', '<EMAIL>', '09819160049', 'female', 'en', '2023-03-10 12:33:02'),
('MSLZKKb0', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 14:47:34'),
('MSm2DciE', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 15:19:16'),
('MSMal6lS', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 15:19:29'),
('MSManaYB', 'Sonali kashinath Solkar ', '<EMAIL>', '8657642309', 'female', 'hi', '2023-03-10 08:05:28'),
('MSmCH78J', 'khushmita jain', '<EMAIL>', '9967340340', 'female', 'hi', '2023-03-11 06:30:28'),
('MSmLaR9I', 'Soniya jain', '<EMAIL>', '9619853026', 'female', 'en', '2023-03-10 05:33:02'),
('MSMSWRrW', 'kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 17:54:20'),
('MSMtJngH', 'Sangeeta sogam', 'sangeetasogam@2017', '9930205903', 'female', 'en', '2023-03-10 07:42:04'),
('MSmX6x6p', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 15:16:49'),
('MSMzDgKm', 'Premchander yadav ', '<EMAIL>', '9768017789', 'male', 'en', '2023-03-10 09:10:06'),
('MSn52ZdO', 'Peter ', '', '9920403260', 'male', 'en', '2023-03-10 12:40:07'),
('MSOiRHyL', 'Shishir prabhu sangale shraadha', '', '9594521913', 'male', 'en', '2023-03-10 09:32:41'),
('MSojDjTA', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 14:53:22'),
('MSOTfXXF', 'kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 14:50:42'),
('MSpuxB2A', 'Bindu Thakkar', '<EMAIL>', '9920486712', 'female', 'en', '2023-03-10 09:56:58'),
('MSqHuFsF', 'Geeta.Mitesh.Gandhi', '<EMAIL>', '9819909069', 'female', 'en', '2023-03-10 12:36:45'),
('MSQq566a', 'dr shital shinde', '<EMAIL>', '9920924104', 'female', 'en', '2023-03-10 10:42:12'),
('MSQUX6eO', 'Sonali kashinath Solkar ', '<EMAIL>', '8657642309', 'female', 'en', '2023-03-10 07:59:48'),
('MSRI1hog', 'Sable Rupali Eknath', '<EMAIL>', '8788912544', 'female', 'en', '2023-03-10 09:50:47'),
('MSrJv6lc', 'Amita dilip joshi', '<EMAIL>', '983307694', 'female', 'en', '2023-03-10 12:07:43'),
('MSRL2rKz', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-06 18:22:07'),
('MSrql4yA', 'Chetan', '<EMAIL>', '9320411755', 'male', 'hin', '2023-03-09 14:26:07'),
('MSrsK5M0', 'Sangeeta sogam', 'sangeetasogam@2017', '9930205903', 'female', 'hi', '2023-03-10 07:43:05'),
('MSrzIxEi', 'Indrajeet Deshmukh', '<EMAIL>', '9757342378', 'male', 'en', '2023-03-12 17:43:16'),
('MSS6eh4N', 'Shweta', '<EMAIL>', '9930298333', 'female', 'en', '2023-03-09 15:58:54'),
('MSSbGTht', 'sharmila k mehta', '', '9167667458', 'female', 'en', '2023-03-10 06:23:32'),
('MST7153t', 'khushi', '<EMAIL>', '9930690101', 'female', 'en', '2023-03-10 09:35:11'),
('MStavaQ6', 'Vicky Dharamshi', '<EMAIL>', '9167361767', 'male', 'en', '2023-03-10 12:51:01'),
('MStqKLFz', 'Saif', '<EMAIL>', '8433684007', 'male', 'eng', '2023-03-09 11:06:00'),
('MSTR9IjR', 'kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 16:50:43'),
('MSu8bgvR', 'kinjal ', '<EMAIL>', '982084841', 'female', 'hi', '2023-03-09 14:53:50'),
('MSUBkHdP', 'kinjal', '<EMAIL>', '9820884841', 'female', 'eng', '2023-03-08 08:33:57'),
('MSUJDtjy', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 15:14:42'),
('MSuM6ldN', 'Aashika Jain', '<EMAIL>', '8552050369', 'female', 'en', '2023-03-10 05:20:45'),
('MSuq7kIE', 'Juhi Manoj Patil ', '<EMAIL>', '9594838498', 'female', 'hi', '2023-03-10 10:16:17'),
('MSUT3dMp', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 11:24:41'),
('MSUVdR45', 'Maninderpal Singh', '<EMAIL>', '917453040234', 'male', 'en', '2023-03-10 11:36:37'),
('MSUY57sP', 'Pallavi Joshi', '<EMAIL>', '8850594626', 'female', 'en', '2023-03-10 13:00:04'),
('MSVt9Y7z', 'kinjal ', '<EMAIL>', '9820884841', 'female', 'eng', '2023-03-08 15:27:20'),
('MSVzegi3', 'kunal dawda', '<EMAIL>', '9172267465', 'female', 'eng', '2023-03-04 15:31:48'),
('MSWC6Gxp', 'Sable Rupali Eknath', '<EMAIL>', '8788912544', 'female', 'en', '2023-03-10 09:55:03'),
('MSWCMBqh', 'Kavya ', '<EMAIL>', '9757141311', 'female', 'en', '2023-03-10 12:53:20'),
('MSwcZn3F', 'Siddhi Aiya ', '<EMAIL>', '9619521245', 'female', 'en', '2023-03-09 16:38:59'),
('MSwj8D02', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hi', '2023-03-09 15:17:10'),
('MSWS9yer', 'kunal dawda', '<EMAIL>', '00', 'male', 'en', '2023-03-17 04:45:29'),
('MSwTXozI', 'Trupti arem shraadha', '', '9004390528', 'female', 'en', '2023-03-10 11:22:17'),
('MSWXidyO', 'Sable Rupali Eknath', '<EMAIL>', '8788912544', 'female', 'hi', '2023-03-10 09:40:47'),
('MSWYwgIZ', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'hin', '2023-03-09 14:34:31'),
('MSWzJzOu', 'Shyni Arun c. K', '<EMAIL>', '7977683128', 'female', 'en', '2023-03-10 07:19:52'),
('MSwzvNE9', 'Kinjal', '<EMAIL>', '9820884841', 'female', 'en', '2023-03-09 19:25:05'),
('MSX9aTK5', 'priyanka Dedha', '<EMAIL>', '9930145132', 'female', 'en', '2023-03-10 12:59:48'),
('MSxAFfYH', 'Manisha Mehta ', '', '9967776419', 'female', 'hi', '2023-03-10 11:25:14'),
('MSxafMEG', 'manish mehta', '', '8433805514', 'female', 'en', '2023-03-10 06:47:30'),
('MSXxuKfh', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 19:56:54'),
('MSYkD4Nr', 'ramakant kadam', '<EMAIL>', '9322880517', 'male', 'hi', '2023-03-10 07:52:02'),
('MSYs8YjY', 'Sable Rupali Eknath', '<EMAIL>', '8788912544', 'female', 'hi', '2023-03-10 09:56:31'),
('MSYZvsiu', 'devi', '<EMAIL>', '09920444955', 'female', 'en', '2023-03-10 09:03:42'),
('MSZ5tlB6', 'Kunal ', '<EMAIL>', '9172267465', 'male', 'eng', '2023-03-08 05:15:26'),
('MSZhKZEA', 'Anita RObinson ', '<EMAIL>', '9967338008', 'female', 'en', '2023-03-10 12:29:02'),
('MSZY8R01', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 15:19:18'),
('MSZz9BBs', 'kunal dawda', '<EMAIL>', '9172267465', 'male', 'en', '2023-03-09 16:49:42');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `answers`
--
ALTER TABLE `answers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `option_mapping`
--
ALTER TABLE `option_mapping`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
