#!/bin/bash

# Deploy Docker Compose proxy for mindscan.mendingmind.org
echo "🚀 Deploying Coolify Docker Compose proxy for mindscan.mendingmind.org"

# Stop any existing proxy containers
echo "🛑 Stopping existing proxy containers..."
docker stop mindscan-proxy 2>/dev/null || true
docker rm mindscan-proxy 2>/dev/null || true

# Make sure Apache is still running
echo "📊 Checking Apache status..."
if pm2 status | grep -q "mindscan-apache.*online"; then
    echo "✅ Apache is running on port 8081"
else
    echo "❌ Apache is not running! Starting Apache first..."
    cd /var/www/html
    pm2 start ecosystem.config.js
    sleep 3
fi

# Test Apache locally
echo "🧪 Testing Apache connection..."
if curl -s http://localhost:8081 > /dev/null; then
    echo "✅ Apache responds on port 8081"
else
    echo "❌ Apache not responding on port 8081"
    echo "Please check Apache status: pm2 logs mindscan-apache"
    exit 1
fi

# Check if coolify network exists
echo "🔗 Checking Docker networks..."
if ! docker network ls | grep -q coolify; then
    echo "❌ Coolify network not found. Available networks:"
    docker network ls
    echo "Please use the correct network name in your Docker Compose file"
    exit 1
else
    echo "✅ Coolify network found"
fi

# Deploy with Docker Compose
echo "🚀 Deploying proxy with Docker Compose..."

# Copy nginx config
cp nginx-proxy.conf nginx.conf

# Deploy the compose file
docker-compose -f coolify-compose.yml up -d

# Wait for container to start
sleep 5

# Check container status
echo "📊 Checking container status..."
if docker ps | grep -q mindscan-proxy; then
    echo "✅ Proxy container is running"
    docker ps | grep mindscan-proxy
else
    echo "❌ Proxy container failed to start"
    docker logs mindscan-proxy 2>/dev/null || echo "No logs available"
    exit 1
fi

# Test the proxy connection
echo "🧪 Testing proxy connection..."
CONTAINER_IP=$(docker inspect mindscan-proxy --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}')
echo "Container IP: $CONTAINER_IP"

if curl -s -H "Host: mindscan.mendingmind.org" http://$CONTAINER_IP > /dev/null; then
    echo "✅ Proxy container responds correctly"
else
    echo "❌ Proxy container not responding"
    echo "Container logs:"
    docker logs mindscan-proxy --tail 20
fi

# Test domain access
echo "🌐 Testing domain access..."
sleep 3
DOMAIN_TEST=$(curl -s -o /dev/null -w "%{http_code}" http://mindscan.mendingmind.org 2>/dev/null || echo "failed")
echo "Domain response: $DOMAIN_TEST"

if [ "$DOMAIN_TEST" = "200" ] || [[ "$DOMAIN_TEST" =~ ^30[0-9]$ ]]; then
    echo "🎉 SUCCESS! Domain is working!"
    echo "✅ http://mindscan.mendingmind.org is now accessible"
else
    echo "⏳ Domain might need a few more minutes to propagate"
    echo "   Check again in 2-3 minutes"
fi

echo ""
echo "📋 Final Status:"
echo "================"
echo "✅ Apache: Running on port 8081"
echo "✅ Proxy: Running in Docker container"
echo "✅ Traefik: Should route mindscan.mendingmind.org → Apache"
echo ""
echo "🧪 Test commands:"
echo "curl -I http://mindscan.mendingmind.org"
echo "curl -I https://mindscan.mendingmind.org"
echo ""
echo "📊 Monitor containers:"
echo "docker logs mindscan-proxy -f"
echo "pm2 logs mindscan-apache"
